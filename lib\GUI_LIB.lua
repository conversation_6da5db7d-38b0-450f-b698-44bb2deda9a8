local GUI = {}

GUI.elements = {}
GUI.windows = {}
GUI.activeWindow = nil
GUI.draggingWindow = nil
GUI.hoveredElement = nil
GUI.activeElement = nil
GUI.openCombobox = nil
GUI.keyBindingElement = nil
GUI.isBindingKey = false
GUI.contextMenu = nil
GUI.lastMouseX = 0
GUI.lastMouseY = 0
GUI.mouseDown = false
GUI.mousePressed = false
GUI.mouseReleased = false
GUI.rightMouseDown = false
GUI.rightMousePressed = false
GUI.rightMouseReleased = false
GUI.defaultFont = "Tahoma"
GUI.titileFont = "Verdana"
GUI.textSize = 14
GUI.elementSpacing = 8
GUI.initialized = false
GUI.nextElementY = 0
GUI.currentWindow = nil
GUI.rounding = 4

GUI.colors = {
    window = Color(21, 21, 21, 255),
    windowOutline = Color(12, 12, 12, 255),
    header = Color(14, 14, 14, 255),
    headerText = Color(137, 154, 224, 255),
    headerAccent = Color(137, 154, 224, 255),
    button = Color(30, 30, 30, 255),
    buttonHover = Color(50, 50, 50, 255),
    buttonActive = Color(70, 70, 70, 255),
    sidebarButtonActive = Color(137, 154, 224, 255),
    sidebarButtonTextActive = Color(255, 255, 255, 255),
    checkbox = Color(30, 30, 30, 255),
    checkboxActive = Color(137, 154, 224, 255),
    slider = Color(21, 21, 21, 255),
    sliderOutline = Color(13, 13, 13, 255),
    sliderTrack = Color(30, 30, 30, 255),
    sliderBar = Color(137, 154, 224, 255),        
    sliderHandle = Color(255, 255, 255, 255),
    sliderTextActive = Color(255, 255, 255, 255),
    text = Color(255, 255, 255, 255),
    titleText = Color(220, 220, 220, 255),
    combobox = Color(30, 30, 30, 255),
    comboboxHover = Color(50, 50, 50, 255),
    comboboxActive = Color(70, 70, 70, 255),
    comboboxArrow = Color(255, 255, 255, 255),
    comboboxDropdownBg = Color(25, 25, 25, 250),
    comboboxDropdownOutline = Color(14, 14, 14, 255),
    comboboxOption = Color(255, 255, 255, 255),
    comboboxOptionHover = Color(137, 154, 224, 255)
}

GUI.themes = {

    ["Bloodline"] = {
        window = Color(28, 28, 28, 255),
        windowOutline = Color(18, 18, 18, 255),
        header = Color(22, 22, 22, 255),
        headerText = Color(157, 174, 244, 255),
        headerAccent = Color(157, 174, 244, 255),
        button = Color(42, 42, 42, 255),
        buttonHover = Color(68, 68, 68, 255),
        buttonActive = Color(90, 90, 90, 255),
        sidebarButtonActive = Color(157, 174, 244, 255),
        sidebarButtonTextActive = Color(245, 250, 255, 255),
        checkbox = Color(40, 40, 40, 255),
        checkboxActive = Color(157, 174, 244, 255),
        slider = Color(28, 28, 28, 255),
        sliderOutline = Color(19, 19, 19, 255),
        sliderTrack = Color(42, 42, 42, 255),
        sliderBar = Color(157, 174, 244, 255),
        sliderHandle = Color(245, 245, 245, 255),
        sliderTextActive = Color(245, 245, 245, 255),
        text = Color(245, 250, 255, 255),
        titleText = Color(215, 225, 255, 255),
        combobox = Color(42, 42, 42, 255),
        comboboxHover = Color(68, 68, 68, 255),
        comboboxActive = Color(90, 90, 90, 255),
        comboboxArrow = Color(245, 245, 245, 255),
        comboboxDropdownBg = Color(35, 35, 35, 250),
        comboboxDropdownOutline = Color(22, 22, 22, 255),
        comboboxOption = Color(245, 245, 245, 255),
        comboboxOptionHover = Color(157, 174, 244, 255)
    },

    ["Crimson Twilight"] = {
        window = Color(30, 12, 12, 255),
        windowOutline = Color(75, 15, 15, 255),
        header = Color(55, 15, 15, 255),
        headerText = Color(255, 110, 110, 255),
        headerAccent = Color(255, 80, 80, 255),
        button = Color(45, 15, 15, 255),
        buttonHover = Color(75, 30, 30, 255),
        buttonActive = Color(105, 40, 40, 255),
        sidebarButtonActive = Color(255, 110, 110, 255),
        sidebarButtonTextActive = Color(225, 255, 250, 255),
        checkbox = Color(45, 15, 15, 255),
        checkboxActive = Color(255, 110, 110, 255),
        slider = Color(30, 12, 12, 255),
        sliderOutline = Color(18, 12, 12, 255),
        sliderTrack = Color(45, 15, 15, 255),
        sliderBar = Color(255, 110, 110, 255),
        sliderHandle = Color(255, 255, 255, 255),
        sliderTextActive = Color(255, 255, 255, 255),
        text = Color(225, 255, 250, 255),
        titleText = Color(200, 255, 255, 255),
        combobox = Color(45, 15, 15, 255),
        comboboxHover = Color(75, 30, 30, 255),
        comboboxActive = Color(105, 40, 40, 255),
        comboboxArrow = Color(255, 255, 255, 255),
        comboboxDropdownBg = Color(55, 15, 15, 250),
        comboboxDropdownOutline = Color(75, 15, 15, 255),
        comboboxOption = Color(255, 230, 230, 255),
        comboboxOptionHover = Color(255, 110, 110, 255)
    },

    ["Cyber Mint"] = {
        window = Color(18, 18, 18, 255),
        windowOutline = Color(0, 30, 30, 255),
        header = Color(0, 45, 45, 255),
        headerText = Color(0, 255, 220, 255),
        headerAccent = Color(0, 255, 170, 255),
        button = Color(18, 18, 18, 255),
        buttonHover = Color(38, 38, 38, 255),
        buttonActive = Color(58, 58, 58, 255),
        sidebarButtonActive = Color(0, 255, 220, 255),
        sidebarButtonTextActive = Color(0, 0, 0, 255),
        checkbox = Color(35, 35, 35, 255),
        checkboxActive = Color(0, 255, 220, 255),
        slider = Color(18, 18, 18, 255),
        sliderOutline = Color(0, 30, 30, 255),
        sliderTrack = Color(38, 38, 38, 255),
        sliderBar = Color(0, 255, 220, 255),
        sliderHandle = Color(245, 245, 245, 255),
        sliderTextActive = Color(0, 0, 0, 255),
        text = Color(255, 255, 255, 255),
        titleText = Color(255, 255, 255, 255),
        combobox = Color(18, 18, 18, 255),
        comboboxHover = Color(38, 38, 38, 255),
        comboboxActive = Color(58, 58, 58, 255),
        comboboxArrow = Color(245, 245, 245, 255),
        comboboxDropdownBg = Color(0, 45, 45, 250),
        comboboxDropdownOutline = Color(0, 55, 55, 255),
        comboboxOption = Color(255, 255, 255, 255),
        comboboxOptionHover = Color(0, 255, 220, 255),
    },

    -- New Themes:

    ["Solar Flare"] = {
        window = Color(40, 25, 10, 255),
        windowOutline = Color(80, 50, 15, 255),
        header = Color(55, 35, 15, 255),
        headerText = Color(255, 180, 90, 255),
        headerAccent = Color(255, 140, 50, 255),
        button = Color(60, 40, 20, 255),
        buttonHover = Color(90, 60, 30, 255),
        buttonActive = Color(115, 80, 40, 255),
        sidebarButtonActive = Color(255, 180, 90, 255),
        sidebarButtonTextActive = Color(255, 255, 240, 255),
        checkbox = Color(60, 40, 20, 255),
        checkboxActive = Color(255, 180, 90, 255),
        slider = Color(40, 25, 10, 255),
        sliderOutline = Color(20, 10, 5, 255),
        sliderTrack = Color(60, 40, 20, 255),
        sliderBar = Color(255, 180, 90, 255),
        sliderHandle = Color(255, 255, 240, 255),
        sliderTextActive = Color(255, 255, 240, 255),
        text = Color(255, 255, 240, 255),
        titleText = Color(255, 220, 150, 255),
        combobox = Color(60, 40, 20, 255),
        comboboxHover = Color(90, 60, 30, 255),
        comboboxActive = Color(115, 80, 40, 255),
        comboboxArrow = Color(255, 255, 240, 255),
        comboboxDropdownBg = Color(45, 25, 10, 250),
        comboboxDropdownOutline = Color(80, 50, 15, 255),
        comboboxOption = Color(255, 255, 240, 255),
        comboboxOptionHover = Color(255, 180, 90, 255),
    },

    ["Midnight Violet"] = {
        window = Color(20, 15, 40, 255),
        windowOutline = Color(35, 30, 65, 255),
        header = Color(40, 30, 70, 255),
        headerText = Color(200, 160, 255, 255),
        headerAccent = Color(160, 120, 255, 255),
        button = Color(45, 35, 75, 255),
        buttonHover = Color(70, 60, 110, 255),
        buttonActive = Color(95, 85, 140, 255),
        sidebarButtonActive = Color(200, 160, 255, 255),
        sidebarButtonTextActive = Color(230, 230, 255, 255),
        checkbox = Color(45, 35, 75, 255),
        checkboxActive = Color(200, 160, 255, 255),
        slider = Color(20, 15, 40, 255),
        sliderOutline = Color(10, 8, 20, 255),
        sliderTrack = Color(45, 35, 75, 255),
        sliderBar = Color(200, 160, 255, 255),
        sliderHandle = Color(240, 240, 255, 255),
        sliderTextActive = Color(230, 230, 255, 255),
        text = Color(230, 230, 255, 255),
        titleText = Color(210, 200, 255, 255),
        combobox = Color(45, 35, 75, 255),
        comboboxHover = Color(70, 60, 110, 255),
        comboboxActive = Color(95, 85, 140, 255),
        comboboxArrow = Color(240, 240, 255, 255),
        comboboxDropdownBg = Color(30, 20, 50, 250),
        comboboxDropdownOutline = Color(35, 30, 65, 255),
        comboboxOption = Color(230, 230, 255, 255),
        comboboxOptionHover = Color(200, 160, 255, 255),
    },

    ["Ocean Breeze"] = {
        window = Color(15, 30, 35, 255),
        windowOutline = Color(15, 40, 50, 255),
        header = Color(20, 45, 55, 255),
        headerText = Color(150, 230, 230, 255),
        headerAccent = Color(100, 210, 210, 255),
        button = Color(30, 50, 60, 255),
        buttonHover = Color(50, 80, 90, 255),
        buttonActive = Color(70, 110, 120, 255),
        sidebarButtonActive = Color(150, 230, 230, 255),
        sidebarButtonTextActive = Color(240, 255, 255, 255),
        checkbox = Color(30, 50, 60, 255),
        checkboxActive = Color(150, 230, 230, 255),
        slider = Color(15, 30, 35, 255),
        sliderOutline = Color(10, 25, 30, 255),
        sliderTrack = Color(30, 50, 60, 255),
        sliderBar = Color(150, 230, 230, 255),
        sliderHandle = Color(240, 255, 255, 255),
        sliderTextActive = Color(240, 255, 255, 255),
        text = Color(240, 255, 255, 255),
        titleText = Color(200, 240, 240, 255),
        combobox = Color(30, 50, 60, 255),
        comboboxHover = Color(50, 80, 90, 255),
        comboboxActive = Color(70, 110, 120, 255),
        comboboxArrow = Color(240, 255, 255, 255),
        comboboxDropdownBg = Color(20, 40, 50, 250),
        comboboxDropdownOutline = Color(15, 40, 50, 255),
        comboboxOption = Color(240, 255, 255, 255),
        comboboxOptionHover = Color(150, 230, 230, 255),
    }
}



GUI.mainMenu = {
    title = "", contentTitleText = "", topRightText = "",
    x = 100, y = 100, width = 1000, height = 650,
    sidebarWidth = 170, sidebarSpacing = 10, contentTitleSpacing = 15,
    currentCategory = nil, categories = {}, window = nil,
    mainAreaStartX = 0, mainAreaWidth = 0, contentStartY = 0
}
GUI.activeCategoryForAdding = nil
GUI.currentElementOffsetX = 0

local widthMapGUI = {
  [' '] = 4,
  ['!'] = 5,
  ['"'] = 6,
  ['#'] = 10,
  ['$'] = 8,
  ['%'] = 14,
  ['&'] = 9,
  ['\''] = 3,
  ['('] = 5,
  [')'] = 5,
  ['*'] = 8,
  ['+'] = 10,
  [','] = 4,
  ['-'] = 5,
  ['.'] = 4,
  ['/'] = 5,
  ['0'] = 8,
  ['1'] = 8,
  ['2'] = 8,
  ['3'] = 8,
  ['4'] = 8,
  ['5'] = 8,
  ['6'] = 8,
  ['7'] = 8,
  ['8'] = 8,
  ['9'] = 8,
  [':'] = 5,
  [';'] = 5,
  ['<'] = 10,
  ['='] = 10,
  ['>'] = 10,
  ['?'] = 7,
  ['@'] = 13,
  ['A'] = 8,
  ['B'] = 8,
  ['C'] = 8,
  ['D'] = 10,
  ['E'] = 8,
  ['F'] = 7,
  ['G'] = 9,
  ['H'] = 9,
  ['I'] = 5,
  ['J'] = 6,
  ['K'] = 8,
  ['L'] = 7,
  ['M'] = 11,
  ['N'] = 9,
  ['O'] = 10,
  ['P'] = 8,
  ['Q'] = 10,
  ['R'] = 9,
  ['S'] = 8,
  ['T'] = 8,
  ['U'] = 9,
  ['V'] = 8,
  ['W'] = 13,
  ['X'] = 8,
  ['Y'] = 8,
  ['Z'] = 8,
  ['['] = 5,
  ['\\'] = 5,
  [']'] = 5,
  ['^'] = 10,
  ['_'] = 8,
  ['`'] = 8,
  ['a'] = 7,
  ['b'] = 8,
  ['c'] = 6,
  ['d'] = 8,
  ['e'] = 7,
  ['f'] = 4,
  ['g'] = 8,
  ['h'] = 8,
  ['i'] = 3,
  ['j'] = 4,
  ['k'] = 7,
  ['l'] = 3,
  ['m'] = 12,
  ['n'] = 8,
  ['o'] = 8,
  ['p'] = 8,
  ['q'] = 8,
  ['r'] = 5,
  ['s'] = 6,
  ['t'] = 5,
  ['u'] = 8,
  ['v'] = 7,
  ['w'] = 10,
  ['x'] = 7,
  ['y'] = 7,
  ['z'] = 6,
  ['{'] = 7,
  ['|'] = 5,
  ['}'] = 7,
  ['~'] = 10,
}

function GUI.ApproxTextWidth(txt)
    local currentTextSize = GUI.textSize or 11
    local width = 0
    for i = 1, #txt do
         local char = txt:sub(i, i)
         width = width + ((widthMapGUI[char] or 5) * (currentTextSize / 13))
    end
    return width
end

function GUI.IsPointInRect(x, y, rectX, rectY, rectWidth, rectHeight)
    return x >= rectX and x <= rectX + rectWidth and
           y >= rectY and y <= rectY + rectHeight
end

function GUI.Initialize(register_render_callback)
    if GUI.initialized then return end
    if Renderer and Renderer.LoadFontFromFile then
        Renderer.LoadFontFromFile(GUI.defaultFont, "Tahoma", GUI.textSize, false)
        Renderer.LoadFontFromFile(GUI.titileFont, "Verdana", GUI.textSize, false)
    else
        print("Warning: Renderer or LoadFontFromFile not available. GUI Font may not load.")
    end

    if register_render_callback == nil then register_render_callback = true end

    if register_render_callback and Cheat and Cheat.RegisterCallback then
        Cheat.RegisterCallback("OnRenderer", function() GUI.Render() end)
    elseif not register_render_callback then
        -- Main script will handle registering a master renderer
    else
        print("Warning: Cheat or Cheat.RegisterCallback not available for GUI.Initialize to self-register OnRenderer.")
    end
    GUI.initialized = true
end

-- Function to get readable key names
function GUI.GetKeyName(keyCode)
    local keyNames = {
        [0] = "None",
        [0x01] = "LMB", [0x02] = "RMB", [0x04] = "MMB",
        [0x08] = "Backspace", [0x09] = "Tab", [0x0D] = "Enter",
        [0x10] = "Shift", [0x11] = "Ctrl", [0x12] = "Alt",
        [0x14] = "Caps", [0x1B] = "Esc", [0x20] = "Space",
        [0x21] = "PgUp", [0x22] = "PgDn", [0x23] = "End", [0x24] = "Home",
        [0x25] = "Left", [0x26] = "Up", [0x27] = "Right", [0x28] = "Down",
        [0x2D] = "Insert", [0x2E] = "Delete",
        [0x30] = "0", [0x31] = "1", [0x32] = "2", [0x33] = "3", [0x34] = "4",
        [0x35] = "5", [0x36] = "6", [0x37] = "7", [0x38] = "8", [0x39] = "9",
        [0x41] = "A", [0x42] = "B", [0x43] = "C", [0x44] = "D", [0x45] = "E",
        [0x46] = "F", [0x47] = "G", [0x48] = "H", [0x49] = "I", [0x4A] = "J",
        [0x4B] = "K", [0x4C] = "L", [0x4D] = "M", [0x4E] = "N", [0x4F] = "O",
        [0x50] = "P", [0x51] = "Q", [0x52] = "R", [0x53] = "S", [0x54] = "T",
        [0x55] = "U", [0x56] = "V", [0x57] = "W", [0x58] = "X", [0x59] = "Y",
        [0x5A] = "Z",
        [0x70] = "F1", [0x71] = "F2", [0x72] = "F3", [0x73] = "F4",
        [0x74] = "F5", [0x75] = "F6", [0x76] = "F7", [0x77] = "F8",
        [0x78] = "F9", [0x79] = "F10", [0x7A] = "F11", [0x7B] = "F12"
    }
    return keyNames[keyCode] or "Key" .. keyCode
end

local function GetVerticallyAlignedTextY(containerAbsY, containerHeight)
    local textHeight = GUI.textSize or 11
    return containerAbsY + (containerHeight - textHeight) / 2 + 3   -- increased padding between text and element border
end

function GUI.ProcessInput()
    if not Input or not Input.GetCursorPos or not Input.GetKeyDown then return end

    local cursorPos = Input.GetCursorPos()
    local mouseX, mouseY = cursorPos.x, cursorPos.y

    local leftMouseDown = Input.GetKeyDown(0x01)
    local rightMouseDown = Input.GetKeyDown(0x02)
    
    -- Track left mouse button state
    GUI.mousePressed = leftMouseDown and not GUI.mouseDown
    GUI.mouseReleased = not leftMouseDown and GUI.mouseDown
    GUI.mouseDown = leftMouseDown
    
    -- Track right mouse button state
    GUI.rightMousePressed = rightMouseDown and not GUI.rightMouseDown
    GUI.rightMouseReleased = not rightMouseDown and GUI.rightMouseReleased
    GUI.rightMouseDown = rightMouseDown

    local inputHandledByOpenCombobox = false

    -- Handle keybind input
    if GUI.isBindingKey and GUI.keyBindingElement then
        -- Check for any key press to bind
        for keyCode = 0x01, 0xFF do
            if Input.GetKeyDown(keyCode) and keyCode ~= 0x01 and keyCode ~= 0x02 then -- Exclude mouse buttons from binding
                GUI.keyBindingElement.boundKey = keyCode
                GUI.keyBindingElement.keyName = GUI.GetKeyName(keyCode)
                GUI.isBindingKey = false
                if GUI.keyBindingElement.onChange then
                    GUI.keyBindingElement.onChange(keyCode, GUI.keyBindingElement.mode)
                end
                GUI.keyBindingElement = nil
                inputHandledByOpenCombobox = true
                GUI.mousePressed = false
                goto next_input_cycle
            end
        end
        
        -- Cancel binding with Escape
        if Input.GetKeyDown(0x1B) then -- Escape key
            GUI.isBindingKey = false
            GUI.keyBindingElement = nil
            inputHandledByOpenCombobox = true
            GUI.mousePressed = false
            goto next_input_cycle
        end
    end

    -- Handle context menu for keybinds
    if GUI.contextMenu then
        if GUI.mousePressed or GUI.rightMousePressed then
            local menu = GUI.contextMenu
            local menuClicked = false
            
            --print("DEBUG: Click detected while context menu open")
            
            -- Check if clicked on context menu
            if GUI.IsPointInRect(mouseX, mouseY, menu.x, menu.y, menu.width, menu.height) then
                local optionHeight = 20
                local clickedOption = math.floor((mouseY - menu.y) / optionHeight) + 1
                
                --print("DEBUG: Clicked on context menu option: " .. clickedOption)
                
                if clickedOption == 1 then -- Toggle
                    menu.element.mode = "Toggle"
                    --print("Mode changed to Toggle")
                    if menu.element.onChange then
                        menu.element.onChange(menu.element.boundKey, "Toggle")
                    end
                elseif clickedOption == 2 then -- Hold
                    menu.element.mode = "Hold"
                    --print("Mode changed to Hold")
                    if menu.element.onChange then
                        menu.element.onChange(menu.element.boundKey, "Hold")
                    end
                end
                menuClicked = true
            else
                --print("DEBUG: Clicked outside context menu - closing")
            end
            
            GUI.contextMenu = nil
            if menuClicked then
                GUI.mousePressed = false
                GUI.rightMousePressed = false
                goto next_input_cycle
            end
        end
    end

    if GUI.openCombobox and GUI.openCombobox.isOpen then
        local cb = GUI.openCombobox; local win = cb.window; local cbAbsX = win.x + cb.offsetX; local cbAbsY = win.y + cb.offsetY
        local dropdownWidth = cb.width
        if cb.dropdownWidth then dropdownWidth = cb.dropdownWidth end

        local dropdownX = cbAbsX; local dropdownY = cbAbsY + cb.height; local itemHeight = 14
        local dropdownTotalHeight = #cb.options * itemHeight

        if GUI.mousePressed then
            local clickedOnDropdownItem = false
            if GUI.IsPointInRect(mouseX, mouseY, dropdownX, dropdownY, dropdownWidth, dropdownTotalHeight) then
                local relativeYInDropdown = mouseY - dropdownY; local itemIndex = math.floor(relativeYInDropdown / itemHeight) + 1
                if itemIndex >= 1 and itemIndex <= #cb.options then
                    local newIndex = itemIndex - 1
                    if newIndex ~= cb.selectedIndex then cb.selectedIndex = newIndex; if cb.onChange then cb.onChange(newIndex, cb.options[newIndex+1]) end end
                    cb.isOpen = false; GUI.openCombobox = nil; GUI.activeElement = nil
                    inputHandledByOpenCombobox = true; clickedOnDropdownItem = true
                end
            end
            if not clickedOnDropdownItem then
                 local onMainBody = GUI.IsPointInRect(mouseX, mouseY, cbAbsX, cbAbsY, cb.width, cb.height)
                 if not onMainBody then
                    cb.isOpen = false; GUI.openCombobox = nil;
                 else
                    cb.isOpen = false; GUI.openCombobox = nil; inputHandledByOpenCombobox = true;
                 end
            end
            if inputHandledByOpenCombobox then
                 GUI.mousePressed = false; if not GUI.mouseDown then GUI.mouseReleased = false end
                 return
            end
        end
        if GUI.openCombobox and GUI.openCombobox.isOpen and GUI.mousePressed and not GUI.IsPointInRect(mouseX,mouseY, dropdownX, dropdownY, dropdownWidth, dropdownTotalHeight) and not GUI.IsPointInRect(mouseX,mouseY, cbAbsX, cbAbsY, cb.width, cb.height) then
            GUI.openCombobox.isOpen = false
            GUI.openCombobox = nil
            inputHandledByOpenCombobox = true
            GUI.mousePressed = false; if not GUI.mouseDown then GUI.mouseReleased = false end
            return
        end
    end

    if GUI.draggingWindow then
        if GUI.mouseDown then
            local deltaX = mouseX - GUI.lastMouseX
            local deltaY = mouseY - GUI.lastMouseY
            GUI.draggingWindow.x = GUI.draggingWindow.x + deltaX
            GUI.draggingWindow.y = GUI.draggingWindow.y + deltaY
            local screenSize = Renderer.GetScreenSize()
            GUI.draggingWindow.x = math.max(0, math.min(GUI.draggingWindow.x, screenSize.x - GUI.draggingWindow.width))
            GUI.draggingWindow.y = math.max(0, math.min(GUI.draggingWindow.y, screenSize.y - GUI.draggingWindow.height))
        else
            GUI.draggingWindow = nil
        end
    end

    GUI.hoveredElement = nil

    for i = #GUI.windows, 1, -1 do
        local window = GUI.windows[i]
        if window.visible then
            local inHeader = GUI.IsPointInRect(mouseX, mouseY, window.x, window.y, window.width, 30)
            if inHeader and GUI.mousePressed then
                GUI.draggingWindow = window; GUI.activeWindow = window
                local temp = GUI.windows[i]; table.remove(GUI.windows, i); table.insert(GUI.windows, temp)
                if GUI.openCombobox then GUI.openCombobox.isOpen = false; GUI.openCombobox = nil; end
                GUI.mousePressed = false; GUI.mouseReleased = false;
                goto next_input_cycle
            end

            local inWindow = GUI.IsPointInRect(mouseX, mouseY, window.x, window.y, window.width, window.height)
            if inWindow then
                for j = #window.elements, 1, -1 do
                    local element = window.elements[j]
                    local isElementInActiveCategory = true
                    if window == GUI.mainMenu.window and element.category then
                         if element.category ~= GUI.mainMenu.currentCategory then
                             isElementInActiveCategory = false
                         end
                    end

                    -- Only process input for visible elements in the active category
                    if isElementInActiveCategory and element.visible then
                        local elAbsX = window.x + element.offsetX
                        local elAbsY = window.y + element.offsetY
                        local elementDrawWidth = element.width

                        if GUI.IsPointInRect(mouseX, mouseY, elAbsX, elAbsY, elementDrawWidth, element.height) then
                             GUI.hoveredElement = element
                        end

                        -- FIXED: Handle keybind clicks FIRST, before general mouse processing
                        if element.type == "keybind" and GUI.hoveredElement == element then
                            if GUI.rightMousePressed then  -- Changed from rightMouseDown to rightMousePressed
                                --print("DEBUG: Right click PRESSED on keybind (single event)")
                                GUI.contextMenu = {
                                    x = mouseX,
                                    y = mouseY,
                                    width = 80,  -- Made wider for better text display
                                    height = 40,
                                    element = element
                                }
                                --print("DEBUG: Context menu created at X:" .. mouseX .. " Y:" .. mouseY)
                                goto next_input_cycle
                            elseif GUI.mousePressed then
                                --print("DEBUG: Left click detected on keybind")
                                GUI.isBindingKey = true
                                GUI.keyBindingElement = element
                                GUI.mousePressed = false
                                GUI.mouseReleased = false
                                goto next_input_cycle
                            end
                        end

                        if GUI.mousePressed and GUI.hoveredElement == element then
                            GUI.activeElement = element
                            if element.type == "button" and element.onClick then element.onClick() end
                            if element.type == "checkbox" and element.onChange then
                                element.checked = not element.checked
                                element.onChange(element.checked)
                            end
                            if element.type == "combobox" then
                                if GUI.openCombobox == element then
                                    element.isOpen = false; 
                                else
                                    if GUI.openCombobox then GUI.openCombobox.isOpen = false end
                                    element.isOpen = true; 
                                    element.animProgress = 0 -- Reset animation progress when opening
                                end
                                GUI.openCombobox = element
                            end
                            if element.type == "colorpicker" and element.onClick then
                                element.onClick()
                            end
                            GUI.mousePressed = false; GUI.mouseReleased = false;
                            goto next_input_cycle
                        end

                        if element.type == "slider" and GUI.activeElement == element and GUI.mouseDown then
                            local sliderRectX = elAbsX
                            local sliderRectWidth = elementDrawWidth
                            local gap = 5
                            local dynamicNumBoxWidth = math.max(40, GUI.ApproxTextWidth(tostring(element.max)) + 10)
                            local sliderAreaX = sliderRectX + dynamicNumBoxWidth + gap
                            local availableTrackWidth = sliderRectWidth - dynamicNumBoxWidth - gap
                            availableTrackWidth = math.max(1, availableTrackWidth)
                            
                            local clampedMouseX = math.max(sliderAreaX, math.min(mouseX, sliderAreaX + availableTrackWidth))
                            local fillRatio = (clampedMouseX - sliderAreaX) / availableTrackWidth
                            local newValue = element.min + (element.max - element.min) * fillRatio
                            
                            element.value = math.floor(newValue + 0.5)
                            
                            if element.value < element.min then element.value = element.min end
                            if element.value > element.max then element.value = element.max end
                            
                            if element.onChange then element.onChange(element.value) end
                        end
                    end
                end
            end
        end
    end
    ::next_input_cycle::

    if GUI.mouseReleased then
        if GUI.activeElement and GUI.activeElement.type ~= "slider" and GUI.activeElement.type ~= "textinput" then
            GUI.activeElement = nil
        elseif not GUI.mouseDown then
            if not (GUI.activeElement and GUI.activeElement.type == "textinput") then
                GUI.activeElement = nil
            end
        end
    end

    if GUI.activeElement and GUI.activeElement.type == "textinput" then
        local now = Globals.GetCurrentTime()
        -- Toggle CapsLock behavior remains available via virtual key 0x14
        if Input.GetKeyDown(0x14) then
            GUI.activeElement.capsMode = not GUI.activeElement.capsMode
            GUI.activeElement.lastKeyTime = now
        end
        if not GUI.activeElement.lastKeyTime or (now - GUI.activeElement.lastKeyTime >= 0.15) then
            if Input.GetKeyDown(0x08) then  -- Backspace
                local currentText = GUI.activeElement.text or ""
                GUI.activeElement.text = currentText:sub(1, #currentText - 1)
                if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                GUI.activeElement.lastKeyTime = now
            end
            if Input.GetKeyDown(0x20) then  -- Space
                GUI.activeElement.text = (GUI.activeElement.text or "") .. " "
                if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                GUI.activeElement.lastKeyTime = now
            end
            local shiftDown = Input.GetKeyDown(0x10)  -- SHIFT key
            local altGrDown = Input.GetKeyDown(0xA5)    -- right Alt (AltGr)
            for key = 0x30, 0x39 do
                if Input.GetKeyDown(key) then
                    local numMapping = {
                        [0x30] = shiftDown and "" or "0",
                        [0x31] = shiftDown and "!" or "1",
                        [0x32] = shiftDown and "@" or "2",
                        [0x33] = shiftDown and "#" or "3",
                        [0x34] = shiftDown and "¤" or "4",
                        [0x35] = shiftDown and "%" or "5",
                        [0x36] = shiftDown and "&" or "6",
                        [0x37] = shiftDown and "/" or "7",
                        [0x38] = shiftDown and "(" or "8",
                        [0x39] = shiftDown and ")" or "9"
                    }
                    local char = numMapping[key]
                    GUI.activeElement.text = (GUI.activeElement.text or "") .. char
                    if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                    GUI.activeElement.lastKeyTime = now
                end
            end
            for key = 0x41, 0x5A do
                if Input.GetKeyDown(key) then
                    local char = string.char(key)
                    if not shiftDown and not GUI.activeElement.capsMode then
                        char = string.lower(char)
                    end
                    GUI.activeElement.text = (GUI.activeElement.text or "") .. char
                    if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                    GUI.activeElement.lastKeyTime = now
                end
            end
            -- Existing question mark and plus handling:
            if Input.GetKeyDown(0xBF) then  -- OEM_2 key
                local char = shiftDown and "?" or "/"
                GUI.activeElement.text = (GUI.activeElement.text or "") .. char
                if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                GUI.activeElement.lastKeyTime = now
            end
            if Input.GetKeyDown(0xBB) then  -- OEM_PLUS key
                local char = shiftDown and "?" or "+"
                GUI.activeElement.text = (GUI.activeElement.text or "") .. char
                if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                GUI.activeElement.lastKeyTime = now
            end
            -- AltGr mappings (for @, $, {, } and [ , ])
            if altGrDown then
                 if Input.GetKeyDown(0x51) then  -- Q key
                     GUI.activeElement.text = (GUI.activeElement.text or "") .. "@"
                     if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                     GUI.activeElement.lastKeyTime = now
                 end
                 if Input.GetKeyDown(0x34) then  -- '4' key
                     GUI.activeElement.text = (GUI.activeElement.text or "") .. "$"
                     if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                     GUI.activeElement.lastKeyTime = now
                 end
                 if Input.GetKeyDown(0xDB) then  -- OEM_4 key (usually [)
                     local char = shiftDown and "{" or "["
                     GUI.activeElement.text = (GUI.activeElement.text or "") .. char
                     if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                     GUI.activeElement.lastKeyTime = now
                 end
                 if Input.GetKeyDown(0xDD) then  -- OEM_6 key (usually ])
                     local char = shiftDown and "}" or "]"
                     GUI.activeElement.text = (GUI.activeElement.text or "") .. char
                     if GUI.activeElement.onChange then GUI.activeElement.onChange(GUI.activeElement.text) end
                     GUI.activeElement.lastKeyTime = now
                 end
            end
        end
    end

	GUI.lastMouseX = mouseX
	GUI.lastMouseY = mouseY
end

function GUI.Render()
    if Input.IsMenuOpen() then
        GUI.ProcessInput()
    else
        if GUI.draggingWindow then GUI.draggingWindow = nil end
        if GUI.activeElement then GUI.activeElement = nil end
        if GUI.openCombobox then GUI.openCombobox.isOpen = false; GUI.openCombobox = nil end
        if GUI.isBindingKey then GUI.isBindingKey = false; GUI.keyBindingElement = nil end
        if GUI.contextMenu then GUI.contextMenu = nil end
        GUI.hoveredElement = nil
    end

    if not Renderer then return end

    if Input.IsMenuOpen() then
        for i, window in ipairs(GUI.windows) do
            if window.visible then
                -- Use GUI.rounding instead of hardcoded values
                Renderer.DrawRectFilled(Vector2D(window.x, window.y),
                    Vector2D(window.x + window.width, window.y + window.height),
                    GUI.colors.window, GUI.rounding)
                Renderer.DrawRectFilled(Vector2D(window.x, window.y),
                    Vector2D(window.x + window.width, window.y + 30),
                    GUI.colors.header, GUI.rounding)
                -- Modified separator line: reduced thickness from 2 to 1
                Renderer.DrawLine(Vector2D(window.x, window.y + 30), Vector2D(window.x + window.width, window.y + 30), GUI.colors.sliderOutline, 1)
                Renderer.DrawText(GUI.titileFont, window.title, Vector2D(window.x + 10, window.y + 8), false, false, GUI.colors.headerText)
                if window == GUI.mainMenu.window then
                    local sepX = window.x + GUI.mainMenu.mainAreaStartX - (GUI.mainMenu.sidebarSpacing / 2)
                    Renderer.DrawLine(Vector2D(sepX, window.y + 30), Vector2D(sepX, window.y + window.height), GUI.colors.sliderOutline, 2)
                end

                for _, element in ipairs(window.elements) do
                    local isElementInActiveCategory = true
                    if window == GUI.mainMenu.window and element.category then
                        if element.category ~= GUI.mainMenu.currentCategory then
                            isElementInActiveCategory = false
                        end
                    end

                    -- Only render if the element is in the active category AND is visible
                    if isElementInActiveCategory and element.visible then
                        local elAbsX = window.x + element.offsetX
                        local elAbsY = window.y + element.offsetY
                        local textY_nonCentered = GetVerticallyAlignedTextY(elAbsY, element.height)
                        local elementDrawWidth = element.width
                        if element.type == "checkbox" then
                            -- Render the label on the left
                            if Renderer.DrawText then 
                                Renderer.DrawText(GUI.defaultFont, element.text, Vector2D(elAbsX, textY_nonCentered), false, false, GUI.colors.text) 
                            end
                            -- Animated rounded switch on the right with sliding blue fill
                            local switchWidth = 40
                            local switchHeight = element.height - 5  -- one pixel less tall
                            local switchX = elAbsX + element.width - switchWidth - 5
                            local switchY = elAbsY + (element.height - switchHeight) / 2 + 3
                            local radius = switchHeight / 2
                            local target = element.checked and 1 or 0
                            if element.animProgress == nil then
                                element.animProgress = target
                            else
                                element.animProgress = element.animProgress + (target - element.animProgress) * 0.05
                            end
                            if Renderer.DrawRectFilled then
                                -- Draw inactive background
                                Renderer.DrawRectFilled(Vector2D(switchX, switchY), Vector2D(switchX + switchWidth, switchY + switchHeight), GUI.colors.checkbox, radius)
                                -- Only draw active blue fill when switch is on; fill up to the switch head without extra pixels
                                if element.checked then
                                    local fillWidth = switchWidth * element.animProgress
                                    Renderer.DrawRectFilled(Vector2D(switchX, switchY), Vector2D(switchX + fillWidth, switchY + switchHeight), GUI.colors.checkboxActive, radius)
                                end
                            end
                            if Renderer.DrawCircleFilled then
                                local knobRadius = radius - 1
                                local knobX = switchX + radius + (switchWidth - 2 * radius) * element.animProgress
                                local knobY = switchY + radius
                                Renderer.DrawCircleFilled(Vector2D(knobX, knobY), GUI.colors.text, knobRadius)
                            end
                        elseif element.type == "button" then
                            local buttonColor = GUI.colors.button
                            local buttonTextColor = GUI.colors.text
                            if GUI.hoveredElement == element then 
                                buttonColor = (GUI.activeElement == element and GUI.mouseDown) and GUI.colors.buttonActive or GUI.colors.buttonHover 
                            end
                            if element.isSidebarButton and GUI.mainMenu.currentCategory == element.categoryTarget then 
                                buttonColor = GUI.colors.sidebarButtonActive
                                buttonTextColor = GUI.colors.sidebarButtonTextActive
                            end
                            if Renderer.DrawRectFilled then 
                                Renderer.DrawRectFilled(Vector2D(elAbsX, elAbsY), Vector2D(elAbsX + elementDrawWidth, elAbsY + element.height), buttonColor, GUI.rounding)  -- use GUI.rounding
                            end
                            local buttonTextY = GetVerticallyAlignedTextY(elAbsY, element.height) - 3.5  -- lifted text by 2 pixels
                            local displayText = element.text
                            if element.icon then
                                displayText = element.icon .. " " .. displayText
                            end
                            if Renderer.DrawText then 
                                Renderer.DrawText(GUI.defaultFont, displayText, Vector2D(elAbsX + (elementDrawWidth / 2), buttonTextY), true, false, buttonTextColor)
                            end
                        elseif element.type == "slider" then
                            local sliderRectX = elAbsX
                            local sliderRectY = elAbsY
                            local sliderRectWidth = elementDrawWidth
                            local sliderRectHeight = element.height
                            local dynamicNumBoxWidth = math.max(40, GUI.ApproxTextWidth(tostring(element.max)) + 10)
                            local gap = 5  -- space between number box and slider area

                            local newSliderHeight = sliderRectHeight + 4
                            local numBoxRadius = math.min(GUI.rounding, newSliderHeight / 2)  -- use GUI.rounding
                            -- Use 21,21,21 color (GUI.colors.window) for the side rect:
                            local numberBoxColor = GUI.colors.sliderBar
                            local sliderValueTextColor = GUI.colors.sliderTextActive
                            if element.depCheckbox and not element.depCheckbox.checked then
                                numberBoxColor = Color(30,30,30,255)
                                sliderValueTextColor = GUI.colors.text
                            end
                            if Renderer.DrawRectFilled then
                                Renderer.DrawRectFilled(Vector2D(sliderRectX, sliderRectY), Vector2D(sliderRectX + dynamicNumBoxWidth, sliderRectY + newSliderHeight), numberBoxColor, numBoxRadius)
                            end
                            local valueStr = ""
                            if element.value == math.floor(element.value) and element.min == math.floor(element.min) and element.max == math.floor(element.max) then
                                valueStr = string.format("%.0f", element.value)
                            else
                                valueStr = string.format("%.2f", element.value)
                            end
                            local textWidth = GUI.ApproxTextWidth(valueStr)
                            local textX = sliderRectX + ((dynamicNumBoxWidth - textWidth) / 2) + 1 -- changed from +3 to +2
                            local textY = GetVerticallyAlignedTextY(sliderRectY, newSliderHeight) - 3.5
                            if Renderer.DrawText then
                                Renderer.DrawText(GUI.defaultFont, valueStr, Vector2D(textX, textY), false, false, sliderValueTextColor)
                            end

                            local sliderAreaX = sliderRectX + dynamicNumBoxWidth + gap
                            local sliderAreaWidth = sliderRectWidth - dynamicNumBoxWidth - gap
                            if Renderer.DrawRectFilled then
                                Renderer.DrawRectFilled(Vector2D(sliderAreaX, sliderRectY), Vector2D(sliderAreaX + sliderAreaWidth, sliderRectY + newSliderHeight), GUI.colors.sliderTrack, numBoxRadius)
                            end
                            local fillRatio = 0
                            if (element.max - element.min) ~= 0 then 
                                fillRatio = (element.value - element.min) / (element.max - element.min)
                            end
                            local fillWidth = sliderAreaWidth * fillRatio
                            if Renderer.DrawRectFilled then
                                Renderer.DrawRectFilled(Vector2D(sliderAreaX, sliderRectY), Vector2D(sliderAreaX + fillWidth, sliderRectY + newSliderHeight), GUI.colors.sliderBar, numBoxRadius)
                            end
                        elseif element.type == "text" then
                            if Renderer.DrawText then Renderer.DrawText(GUI.defaultFont, element.text, Vector2D(elAbsX, GetVerticallyAlignedTextY(elAbsY, element.height)), false, false, GUI.colors.text) end
                        elseif element.type == "combobox" then
                            local bgColor = GUI.colors.combobox
                            if GUI.hoveredElement == element or element.isOpen then 
                                bgColor = (GUI.activeElement == element and GUI.mouseDown or element.isOpen) and GUI.colors.comboboxActive or GUI.colors.comboboxHover 
                            end
                            if Renderer.DrawRectFilled then 
                                Renderer.DrawRectFilled(Vector2D(elAbsX, elAbsY), Vector2D(elAbsX + elementDrawWidth, elAbsY + element.height) , bgColor, GUI.rounding) 
                            end
                            local selectedText = (element.options and element.selectedIndex and element.options[element.selectedIndex + 1]) or ""
                            if Renderer.DrawText then 
                                Renderer.DrawText(GUI.defaultFont, selectedText, Vector2D(elAbsX + 5, GetVerticallyAlignedTextY(elAbsY, element.height) - 3.5), false, false, GUI.colors.text) 
                            end
                            local arrowX = elAbsX + elementDrawWidth - 12; local arrowY = elAbsY + element.height/2
                            if Renderer.DrawLine then
                                Renderer.DrawLine(Vector2D(arrowX - 3, arrowY - 2), Vector2D(arrowX, arrowY + 1), GUI.colors.comboboxArrow, 1)
                                Renderer.DrawLine(Vector2D(arrowX, arrowY + 1), Vector2D(arrowX + 3, arrowY - 2), GUI.colors.comboboxArrow, 1)
                            end
                        elseif element.type == "textinput" then
							local borderColor = GUI.colors.windowOutline
							if GUI.activeElement == element then
								borderColor = Color(137, 154, 224, 255)  -- Highlight border while active
							end
							-- Draw border as a filled rounded rectangle
							if Renderer.DrawRectFilled then 
								Renderer.DrawRectFilled(
									Vector2D(elAbsX, elAbsY),
									Vector2D(elAbsX + elementDrawWidth, elAbsY + element.height),
									borderColor, GUI.rounding)  -- use GUI.rounding
								Renderer.DrawRectFilled(
									Vector2D(elAbsX + 1, elAbsY + 1),
									Vector2D(elAbsX + elementDrawWidth - 1, elAbsY + element.height - 1),
									Color(30, 30, 30, 255), GUI.rounding)  -- use GUI.rounding
							end
							-- Draw input text
							if Renderer.DrawText then 
								Renderer.DrawText(
									GUI.defaultFont,
									element.text or "",
									Vector2D(elAbsX + 5, GetVerticallyAlignedTextY(elAbsY, element.height) - 3.5),
									false, false, GUI.colors.text
								)
							end
							-- Add blinking typing cursor for active textinput (reverting to previous working code)
							if GUI.activeElement == element then
                                local blink = math.floor(Globals.GetCurrentTime() * 2) % 2 == 0  -- blink timing
                                if blink then
                                     local textStr = element.text or ""
                                     local computedWidth = GUI.ApproxTextWidth(textStr)
                                     local correctionFactor = 1
                                     if textStr:sub(-1) == " " then
                                          correctionFactor = 0.5  -- lower correction for trailing space
                                     end
                                     local correction = (#textStr) * correctionFactor
                                     local cursorX = elAbsX + 5 + computedWidth - correction
                                     Renderer.DrawLine(Vector2D(cursorX, elAbsY + 2), Vector2D(cursorX, elAbsY + element.height - 2), GUI.colors.text, 1)
                                end
                            end
                        elseif element.type == "colorpicker" then
                            -- Draw the color picker box
                            if Renderer.DrawRectFilled then
                                -- Draw the border for the color picker
                                Renderer.DrawRectFilled(
                                Vector2D(elAbsX, elAbsY),
                                Vector2D(elAbsX + element.width, elAbsY + element.height),
                                GUI.colors.windowOutline)

                                -- Draw the actual color inside the box
                                Renderer.DrawRectFilled(
                                Vector2D(elAbsX + 1, elAbsY + 1),
                                Vector2D(elAbsX + element.width - 1, elAbsY + element.height - 1),
                                element.color)
                            end
                        elseif element.type == "keybind" then
                            -- Determine colors based on state
                            local bgColor = GUI.colors.combobox
                            local textColor = GUI.colors.text
                            
                            if GUI.isBindingKey and GUI.keyBindingElement == element then
                                bgColor = GUI.colors.comboboxActive
                                textColor = GUI.colors.headerAccent
                            elseif GUI.hoveredElement == element then
                                bgColor = GUI.colors.comboboxHover
                            end
                            
                            -- Draw keybind box
                            if Renderer.DrawRectFilled then 
                                Renderer.DrawRectFilled(
                                    Vector2D(elAbsX, elAbsY), 
                                    Vector2D(elAbsX + elementDrawWidth, elAbsY + element.height), 
                                    bgColor, GUI.rounding
                                ) 
                            end
                            
                            -- Draw key text
                            local displayText = element.keyName
                            if GUI.isBindingKey and GUI.keyBindingElement == element then
                                displayText = "..."
                            end
                            
                            if Renderer.DrawText then 
                                Renderer.DrawText(
                                    GUI.defaultFont, 
                                    displayText, 
                                    Vector2D(elAbsX + 5, GetVerticallyAlignedTextY(elAbsY, element.height) - 3.5), 
                                    false, false, textColor
                                ) 
                            end
                            
                            -- Draw mode indicator (T for Toggle, H for Hold)
                            local modeText = element.mode == "Toggle" and "T" or "H"
                            local modeTextWidth = GUI.ApproxTextWidth(modeText)
                            if Renderer.DrawText then
                                Renderer.DrawText(
                                    GUI.defaultFont,
                                    modeText,
                                    Vector2D(elAbsX + elementDrawWidth - modeTextWidth - 5, GetVerticallyAlignedTextY(elAbsY, element.height) - 3.5),
                                    false, false, GUI.colors.headerAccent
                                )
                            end
                        end
                    end
                end
            end
        end

        if GUI.openCombobox then -- Removed the 'and GUI.openCombobox.isOpen' check here to allow closing animation
            local cb = GUI.openCombobox; local win = cb.window
            local cbAbsX = win.x + cb.offsetX; local cbAbsY = win.y + cb.offsetY
            local dropdownWidth = cb.width
            if cb.dropdownWidth then dropdownWidth = cb.dropdownWidth end
            local dropdownX = cbAbsX; local dropdownY = cbAbsY + cb.height; 
            local itemHeight = 14
            local dropdownTotalHeight = #cb.options * itemHeight

            local targetAnimProgress = cb.isOpen and 1 or 0
            if cb.animProgress == nil then cb.animProgress = 0 end -- Initialize if not set
            cb.animProgress = cb.animProgress + (targetAnimProgress - cb.animProgress) * 0.2 -- Interpolate

            local currentDropdownHeight = dropdownTotalHeight * cb.animProgress
            
            -- Only draw if the dropdown has a visible height
            if currentDropdownHeight > 1 then
                if Renderer and Renderer.DrawRectFilled and Renderer.DrawRect then
                    Renderer.DrawRectFilled(Vector2D(dropdownX, dropdownY), Vector2D(dropdownX + dropdownWidth, dropdownY + currentDropdownHeight), GUI.colors.comboboxDropdownBg)
                    Renderer.DrawRect(Vector2D(dropdownX, dropdownY), Vector2D(dropdownX + dropdownWidth, dropdownY + currentDropdownHeight), GUI.colors.comboboxDropdownOutline, 1)
                end

                local currentItemY = dropdownY;
                local cursorPos = Input.GetCursorPos and Input.GetCursorPos() or {x=-1, y=-1}
                for i, optionText in ipairs(cb.options) do
                    -- Only draw options if they are within the current animated height
                    if currentItemY + itemHeight <= dropdownY + currentDropdownHeight + 1 then -- +1 for minor visual overflow
                        local itemColor = GUI.colors.comboboxOption
                        local itemTextY = currentItemY + ((itemHeight - GUI.textSize) / 2)
                        if GUI.IsPointInRect(cursorPos.x, cursorPos.y, dropdownX, currentItemY, dropdownWidth, itemHeight) and cb.isOpen then -- Only highlight on hover if open
                            itemColor = GUI.colors.comboboxOptionHover
                            if Renderer and Renderer.DrawRectFilled then 
                                Renderer.DrawRectFilled(Vector2D(dropdownX + 1, currentItemY + 1), Vector2D(dropdownX + dropdownWidth - 1, currentItemY + itemHeight - 1), Color(itemColor.r, itemColor.g, itemColor.b, 50))
                            end
                        end
                        if Renderer and Renderer.DrawText then 
                            Renderer.DrawText(GUI.defaultFont, optionText, Vector2D(dropdownX + 5, itemTextY), false, false, itemColor)
                        end
                    end
                    currentItemY = currentItemY + itemHeight
                end
            end

            -- If animation is almost complete and dropdown is meant to be closed, clear GUI.openCombobox
            if not cb.isOpen and cb.animProgress < 0.01 then
                 GUI.openCombobox = nil
                 cb.animProgress = nil -- Reset animProgress
            end
        end

        -- Render context menu for keybinds
        if GUI.contextMenu then
            --print("DEBUG: Attempting to render context menu")
            local menu = GUI.contextMenu
            
            -- Force menu to be within screen bounds
            local screenSize = Renderer.GetScreenSize()
            local menuX = math.max(0, math.min(menu.x, screenSize.x - menu.width))
            local menuY = math.max(0, math.min(menu.y, screenSize.y - menu.height))
            
            --print("DEBUG: Rendering context menu at " .. menuX .. "," .. menuY .. " Size: " .. menu.width .. "x" .. menu.height)
            
            -- Draw context menu background
            if Renderer and Renderer.DrawRectFilled then
                --print("DEBUG: Drawing context menu background")
                -- Normal background color (change from red back to normal)
                Renderer.DrawRectFilled(
                    Vector2D(menuX, menuY),
                    Vector2D(menuX + menu.width, menuY + menu.height),
                    Color(35, 35, 35, 255)  -- Dark background
                )
                
                -- Border
                if Renderer.DrawRect then
                    Renderer.DrawRect(
                        Vector2D(menuX, menuY),
                        Vector2D(menuX + menu.width, menuY + menu.height),
                        Color(100, 100, 100, 255), 2  -- Gray border
                    )
                end
            else
               --print("DEBUG: Renderer.DrawRectFilled not available")
            end
            
            -- Draw menu options
            local optionHeight = 20
            local options = {"Toggle", "Hold"}
            local cursorPos = Input.GetCursorPos and Input.GetCursorPos() or {x=-1, y=-1}
            
            for i, option in ipairs(options) do
                local optionY = menuY + (i-1) * optionHeight
                local textColor = Color(255, 255, 255, 255)
                
                -- Highlight hovered option
                if GUI.IsPointInRect(cursorPos.x, cursorPos.y, menuX, optionY, menu.width, optionHeight) then
                    textColor = Color(137, 154, 224, 255)  -- Blue when hovered
                    if Renderer and Renderer.DrawRectFilled then
                        Renderer.DrawRectFilled(
                            Vector2D(menuX + 2, optionY + 2),
                            Vector2D(menuX + menu.width - 2, optionY + optionHeight - 2),
                            Color(50, 50, 50, 255)
                        )
                    end
                end
                
                local displayText = option
                
                if Renderer and Renderer.DrawText then
                    --print("DEBUG: Drawing text: " .. displayText)
                    Renderer.DrawText(
                        GUI.defaultFont,
                        displayText,
                        Vector2D(menuX + 5, optionY + 2),
                        false, false, textColor
                    )
                else
                    --print("DEBUG: Renderer.DrawText not available")
                end
            end
        else
            -- Debug when no context menu
            if GUI.rightMousePressed then
                --print("DEBUG: Right mouse pressed but no context menu exists")
            end
        end
    end
end

function GUI.Window(title, x, y, width, height)
    local newWindow = {
        title = title or "Window", x = x or 100, y = y or 100,
        width = width or 200, height = height or 150,
        visible = true, elements = {}
    }
    table.insert(GUI.windows, newWindow)
    GUI.SetActiveWindow(newWindow) -- Automatically set new window as active context for adding elements
    return newWindow
end

local function AddElement(elementData) -- Used by legacy/non-menu elements potentially
    if not GUI.currentWindow then print("GUI Error: No current window context for AddElement"); return nil end
    local currentElementPadding = GUI.elementSpacing or 4
    local baseElement = {
        window = GUI.currentWindow, offsetX = currentElementPadding, offsetY = GUI.nextElementY,
        width = GUI.currentWindow.width - (currentElementPadding * 2), height = 16,
        visible = true -- Added visible property
    }
    for k, v in pairs(elementData) do baseElement[k] = v end
    table.insert(GUI.currentWindow.elements, baseElement)
    GUI.nextElementY = GUI.nextElementY + baseElement.height + (GUI.elementSpacing or 4)
    return baseElement
end

function GUI.CreatePropperMenuLayout(config)
    if not GUI.Window then print("Error: GUI.Window function is not available."); return nil end
    GUI.mainMenu.title = config.windowTitle or GUI.mainMenu.title
    GUI.mainMenu.contentTitleText = config.contentTitle or GUI.mainMenu.contentTitleText
    GUI.mainMenu.topRightText = config.topRightText or GUI.mainMenu.topRightText
    GUI.mainMenu.x = config.x or GUI.mainMenu.x; GUI.mainMenu.y = config.y or GUI.mainMenu.y
    GUI.mainMenu.width = config.width or GUI.mainMenu.width; GUI.mainMenu.height = config.height or GUI.mainMenu.height
    GUI.mainMenu.sidebarWidth = config.sidebarWidth or GUI.mainMenu.sidebarWidth
    GUI.mainMenu.sidebarSpacing = config.sidebarSpacing or GUI.mainMenu.sidebarSpacing
    GUI.mainMenu.contentTitleSpacing = config.contentTitleSpacing or GUI.mainMenu.contentTitleSpacing
    GUI.mainMenu.categories = config.categories or {"User Info", "Default"}
    GUI.mainMenu.currentCategory = GUI.mainMenu.categories[1]

    local win = GUI.Window(GUI.mainMenu.title, GUI.mainMenu.x, GUI.mainMenu.y, GUI.mainMenu.width, GUI.mainMenu.height)
    if not win then print("Error: GUI.Window failed to create a window for PropperMenuLayout."); return nil end
    GUI.mainMenu.window = win
    GUI.currentWindow = win -- Set context for elements being added to this menu

    -- Moved sections upward by reducing vertical offset from 30 to 20
    local currentY = 20 + (GUI.elementSpacing or 4)
    local elementPadding = GUI.elementSpacing or 4

    if GUI.mainMenu.contentTitleText and GUI.mainMenu.contentTitleText ~= "" then
        local titleTextWidth = GUI.ApproxTextWidth(GUI.mainMenu.contentTitleText)
        local titleHeight = 18
        local titleElement = {
            window = win, type = "text", text = GUI.mainMenu.contentTitleText,
            offsetX = (GUI.mainMenu.width - titleTextWidth) / 2, -- Placeholder, will be centered in render
            offsetY = currentY,
            width = titleTextWidth, height = titleHeight,
            isContentTitle = true,
            visible = true -- Added visible property
        }
        table.insert(win.elements, titleElement)
        currentY = currentY + titleHeight
    end

    if GUI.mainMenu.topRightText and GUI.mainMenu.topRightText ~= "" then
        local topRightTextWidth = GUI.ApproxTextWidth(GUI.mainMenu.topRightText)
        local topRightHeight = 18
        -- Adjusted offset: move the top-right text slightly further to the right (+2 pixels)
        local topRightOffsetX = GUI.mainMenu.width - topRightTextWidth - elementPadding - 5 + 2
        local topRightElement = {
            window = win, type = "text", text = GUI.mainMenu.topRightText,
            offsetX = topRightOffsetX,
            offsetY = 30 + (GUI.elementSpacing or 4),
            width = topRightTextWidth, height = topRightHeight,
            isTopRightTitle = true,
            visible = true -- Added visible property
        }
        table.insert(win.elements, topRightElement)
        if not (GUI.mainMenu.contentTitleText and GUI.mainMenu.contentTitleText ~= "") then
             currentY = currentY + topRightHeight
        end
    end

    if (GUI.mainMenu.contentTitleText and GUI.mainMenu.contentTitleText ~= "") or (GUI.mainMenu.topRightText and GUI.mainMenu.topRightText ~= "") then
        currentY = currentY + GUI.mainMenu.contentTitleSpacing
    end
    GUI.mainMenu.contentStartY = currentY

    local sidebarX = elementPadding
    GUI.mainMenu.contentStartY = currentY
    GUI.nextElementY = currentY

    for _, categoryName in ipairs(GUI.mainMenu.categories) do
        GUI.AddSidebarButton(categoryName, sidebarX, GUI.mainMenu.sidebarWidth)
    end

    GUI.mainMenu.mainAreaStartX = sidebarX + GUI.mainMenu.sidebarWidth + GUI.mainMenu.sidebarSpacing
    GUI.mainMenu.mainAreaWidth = GUI.mainMenu.width - GUI.mainMenu.mainAreaStartX - elementPadding

    return win
end

function GUI.AddSidebarButton(categoryName, x, width)
    if not GUI.currentWindow then return nil end
    local elementHeight = 20
    local baseElement = {
        window = GUI.currentWindow, offsetX = x, offsetY = GUI.nextElementY,
        width = width, height = elementHeight, type = "button", text = categoryName,
        isSidebarButton = true, categoryTarget = categoryName,
        onClick = function() GUI.mainMenu.currentCategory = categoryName end,
        visible = true -- Added visible property
    }
    table.insert(GUI.currentWindow.elements, baseElement)
    GUI.nextElementY = GUI.nextElementY + baseElement.height + (GUI.elementSpacing or 4)
    return baseElement
end

function GUI.BeginCategory(categoryName)
    if not GUI.mainMenu.window then GUI.activeCategoryForAdding = nil; return end
    local isValidCategory = false
    for _, cat in ipairs(GUI.mainMenu.categories) do 
        if cat == categoryName then 
            isValidCategory = true; break; 
        end 
    end
    if not isValidCategory then 
        print("Warning: Category '" .. categoryName .. "' not defined."); 
        GUI.activeCategoryForAdding = nil; 
        return 
    end

    GUI.activeCategoryForAdding = categoryName
    GUI.currentWindow = GUI.mainMenu.window
    GUI.nextElementY = GUI.mainMenu.contentStartY
    GUI.currentElementOffsetX = GUI.mainMenu.mainAreaStartX
    -- If the active category is "User Info", add a header for it using the global function
    if categoryName == "User Info" then
        GUI.AddElementToCategory({type = "text", text = "User Info", height = 20})
    end
end

local function AddElementToCategory(elementData)
    if not GUI.activeCategoryForAdding or not GUI.currentWindow then return nil end
    local baseHeight = elementData.type == "button" and 20 or (elementData.type == "combobox" and 20 or (elementData.type == "slider" and 8 or 16))
    local elementHeight = elementData.height or baseHeight

    local element_offsetX = GUI.currentElementOffsetX
    if elementData.offsetX then -- Allow overriding offset X for columnar layout
        element_offsetX = elementData.offsetX
    end
    
    local element_width = GUI.mainMenu.mainAreaWidth
    if elementData.width then -- Allow overriding width
        element_width = elementData.width
    end

    local baseElement = {
        window = GUI.currentWindow,
        offsetX = element_offsetX,
        offsetY = GUI.nextElementY, -- Uses the managed nextElementY for vertical stacking
        width = element_width,
        height = elementHeight,
        category = GUI.activeCategoryForAdding,
        visible = true -- Added visible property
    }
    for k, v in pairs(elementData) do baseElement[k] = v end
    if baseElement.type == "combobox" then 
        baseElement.dropdownWidth = baseElement.width
        baseElement.animProgress = 0 -- Initialize animation progress for combobox
    end

    table.insert(GUI.currentWindow.elements, baseElement)
    
    if not elementData.offsetY then -- If offsetY is not specified, it's part of normal flow
        GUI.nextElementY = GUI.nextElementY + baseElement.height + (GUI.elementSpacing or 4)
    end
    return baseElement
end
-- Export the function so it is available globally
GUI.AddElementToCategory = AddElementToCategory

-- Standard GUI element creation functions (can be used for non-menu windows if GUI.currentWindow is set)
function GUI.Button(text, onClick) return AddElement({type = "button", text = text, height = 20, onClick = onClick}) end
function GUI.Checkbox(text, defaultChecked, onChange) return AddElement({type = "checkbox", text = text, checked = defaultChecked or false, height = 16, onChange = onChange}) end
function GUI.Slider(text, min, max, defaultValue, onChange)
    AddElement({type = "text", text = text, height = 16})
    return AddElement({type = "slider", min = min, max = max, value = defaultValue or min, onChange = onChange, height = 8})
end
function GUI.Combobox(labelText, options, defaultIndex, onChange)
    AddElement({type = "text", text = labelText, height = 16})
    return AddElement({type = "combobox", height = 20, options = options or {}, selectedIndex = defaultIndex or 0, isOpen = false, onChange = onChange, animProgress = 0}) -- Initialize animProgress here too
end
function GUI.Label(text) return AddElement({type = "text", text = text, height = 16}) end
-- Add new TextInput element creation
function GUI.TextInput(label, defaultText, onChange)
	AddElement({type = "text", text = label, height = 16})
	return AddElement({type = "textinput", height = 20, text = defaultText or "", onChange = onChange})
end

-- Standard keybind creation function
function GUI.Keybind(labelText, defaultKey, defaultMode, onChange)
    AddElement({type = "text", text = labelText, height = 16})
    return AddElement({
        type = "keybind", 
        height = 20, 
        width = 200,
        boundKey = defaultKey or 0,
        keyName = GUI.GetKeyName(defaultKey or 0),
        mode = defaultMode or "Toggle", -- "Toggle" or "Hold"
        isActive = false,
        onChange = onChange,
        contextMenuOpen = false
    })
end

-- Menu-specific element creation functions
function GUI.MenuButton(text, onClick)
    if not GUI.activeCategoryForAdding then print("Warning: Not in an active category (MenuButton)."); return end
    return AddElementToCategory({type = "button", text = text, height = 20, onClick = onClick})
end

function GUI.MenuCheckbox(text, defaultChecked, onChange)
    if not GUI.activeCategoryForAdding then print("Warning: Not in an active category (MenuCheckbox)."); return end
    return AddElementToCategory({type = "checkbox", text = text, checked = defaultChecked or false, height = 16, onChange = onChange})
end

function GUI.MenuSlider(labelText, min, max, defaultValue, onChange)
    if not GUI.activeCategoryForAdding then print("Warning: Not in an active category (MenuSlider)."); return end
    AddElementToCategory({type = "text", text = labelText, category = GUI.activeCategoryForAdding, height = 16})
    return AddElementToCategory({type = "slider", min = min, max = max, value = defaultValue or min, onChange = onChange, height = 8})
end

function GUI.MenuCombobox(labelText, options, defaultIndex, onChange)
    if not GUI.activeCategoryForAdding then print("Warning: Not in an active category (MenuCombobox)."); return end
    local labelElement = AddElementToCategory({type = "text", text = labelText, category = GUI.activeCategoryForAdding, height = 16})
    -- The combobox itself needs to be added after the label, so nextElementY should be updated by the label
    -- If AddElementToCategory doesn't advance GUI.nextElementY correctly in all cases, this might need adjustment
    return AddElementToCategory({type = "combobox", height = 20, options = options or {}, selectedIndex = defaultIndex or 0, isOpen = false, onChange = onChange, animProgress = 0}) -- Initialize animProgress here too
end

function GUI.MenuLabel(text)
    if not GUI.activeCategoryForAdding then print("Warning: Not in an active category (MenuLabel)."); return false end
    return AddElementToCategory({type = "text", text = text, height = 16})
end

function GUI.MenuColorPicker(labelText, defaultColor, onChange)
    if not GUI.activeCategoryForAdding then
        print("Warning: Not in an active category (MenuColorPicker).")
        return nil
    end

    -- Add label for the color picker
    local label = AddElementToCategory({
        type = "text",
        text = labelText,
        height = 16,
        width = GUI.ApproxTextWidth(labelText) + 10 -- Calculate label width dynamically
    })
    
    -- Add the color picker element
    local colorPicker = AddElementToCategory({
        type = "colorpicker",
        color = defaultColor or Color(255, 255, 255, 255),
        height = 10,
        width = 35, -- Square box for the color picker
        offsetX = GUI.mainMenu.mainAreaStartX + GUI.mainMenu.mainAreaWidth - 35 - 5, -- Right-align with 5 pixels padding
        offsetY = label.offsetY + 9,
        onChange = onChange,
        isOpen = false,
        popupWindow = nil
    })

    -- Create popup window for color adjustment when the color picker is clicked
    colorPicker.onClick = function()
        if colorPicker.popupWindow then
            GUI.HideWindow(colorPicker.popupWindow)
            colorPicker.popupWindow = nil
            return
        end

        local win = GUI.Window("Color Picker", 
            colorPicker.window.x + colorPicker.offsetX + colorPicker.width + 10,
            colorPicker.window.y + colorPicker.offsetY,
            200, 160)
            
        colorPicker.popupWindow = win
        
        -- Add RGB sliders to adjust the color
        GUI.Slider("Red", 0, 255, colorPicker.color.r, function(value)
            colorPicker.color = Color(value, colorPicker.color.g, colorPicker.color.b, 255)
            if colorPicker.onChange then
                colorPicker.onChange(colorPicker.color)
            end
        end)
        
        GUI.Slider("Green", 0, 255, colorPicker.color.g, function(value)
            colorPicker.color = Color(colorPicker.color.r, value, colorPicker.color.b, 255)
            if colorPicker.onChange then
                colorPicker.onChange(colorPicker.color)
            end
        end)
        
        GUI.Slider("Blue", 0, 255, colorPicker.color.b, function(value)
            colorPicker.color = Color(colorPicker.color.r, colorPicker.color.g, value, 255)
            if colorPicker.onChange then
                colorPicker.onChange(colorPicker.color)
            end
        end)
    end

    return colorPicker
end

-- Menu-specific keybind creation function
function GUI.MenuKeybind(labelText, defaultKey, defaultMode, onChange)
    if not GUI.activeCategoryForAdding then 
        print("Warning: Not in an active category (MenuKeybind)."); 
        return 
    end
    
    -- Add label
    local label = AddElementToCategory({
        type = "text", 
        text = labelText, 
        height = 16,
        width = GUI.ApproxTextWidth(labelText) + 10
    })
    
    -- Add keybind element positioned to the right of the label
    return AddElementToCategory({
        type = "keybind",
        height = 16,
        width = 80, -- Width of the keybind box
        offsetX = GUI.mainMenu.mainAreaStartX + GUI.mainMenu.mainAreaWidth - 80 - 5, 
        offsetY = label.offsetY + 2,
        boundKey = defaultKey or 0,
        keyName = GUI.GetKeyName(defaultKey or 0),
        mode = defaultMode or "Toggle",
        isActive = false,
        onChange = onChange,
        contextMenuOpen = false
    })
end
function GUI.MenuTextInput(labelText, defaultText, onChange)
    if not GUI.activeCategoryForAdding then
        print("Warning: Not in an active category (MenuTextInput).");
        return
    end
    AddElementToCategory({type = "text", text = labelText, height = 16})
    return AddElementToCategory({type = "textinput", height = 20, text = defaultText or "", onChange = onChange})
end

function GUI.SetActiveWindow(window)
    GUI.activeWindow = window; GUI.currentWindow = window
    -- Bring to front
    for i, w in ipairs(GUI.windows) do if w == window then table.remove(GUI.windows, i); table.insert(GUI.windows, window); break; end end
    -- Reset nextElementY for the new active window, assuming elements will be added from top
    if window then GUI.nextElementY = 30 + (GUI.elementSpacing or 4) else GUI.nextElementY = 0 end
end

function GUI.HideWindow(window) if window then window.visible = false end end
function GUI.ShowWindow(window) if window then window.visible = true end end
function GUI.ToggleWindow(window) if window then window.visible = not window.visible end end

-- New functions to show/hide individual elements
function GUI.ShowElement(element)
    if element then
        element.visible = true
    end
end

function GUI.HideElement(element)
    if element then
        element.visible = false
    end
end

function GUI.SetValue(element, newValue, property)
    if not element then return end
    
    -- Auto-detect property based on element type if not specified
    local prop = property
    if not prop then
        if element.type == "checkbox" then
            prop = "checked"
        elseif element.type == "combobox" then
            prop = "selectedIndex"
        elseif element.type == "colorpicker" then
            prop = "color"
        elseif element.type == "slider" then
            prop = "value"
        elseif element.type == "textinput" then
            prop = "text"
        elseif element.type == "keybind" then
            prop = "boundKey"
        else
            prop = "value"
        end
    end
    
    -- Set the value
    element[prop] = newValue
    
    -- Handle special cases and trigger onChange callback
    if element.onChange then
        if element.type == "checkbox" and prop == "checked" then
            element.onChange(newValue)
        elseif element.type == "combobox" and prop == "selectedIndex" then
            local optionText = element.options and element.options[newValue + 1] or nil
            element.onChange(newValue, optionText)
        elseif element.type == "colorpicker" and prop == "color" then
            element.onChange(newValue)
        elseif element.type == "slider" and prop == "value" then
            if element.min and newValue < element.min then
                element.value = element.min
                newValue = element.min
            end
            if element.max and newValue > element.max then
                element.value = element.max
                newValue = element.max
            end
            element.onChange(newValue)
        elseif element.type == "textinput" and prop == "text" then
            element.onChange(newValue)
        elseif element.type == "keybind" then
            if prop == "boundKey" then
                element.keyName = GUI.GetKeyName(newValue)
                element.onChange(newValue, element.mode)
            elseif prop == "mode" then
                element.onChange(element.boundKey, newValue)
            end
        else
            element.onChange(newValue)
        end
    end
end

function GUI.SetTheme(name)
    if GUI.themes[name] then
        GUI.colors = GUI.themes[name]
    end
end


return GUI
