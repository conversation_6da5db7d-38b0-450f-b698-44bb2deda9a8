
local ConfigManager = {}




local CONFIG_VARIABLES = {

    
    "keybind_pos_x",
    "keybind_pos_y",

    "spectlist_pos_x",
    "spectlist_pos_y",

    "antiaim_sidebar_button",
    "misc_sidebar_button",
    
    
    "fake_pitch_zeus",
    "trashtalk",
    
    
    "pitch_modification",
    "pitch_mode",
    "custom_pitch",
    
    
    "pitch_jitter_mode",
    "pitch_jitter_speed",
    
    
    "yaw_modification",
    "fix auto-strafer",
    "look_at_nearest_player",
    "base_yaw_offset",
    "yaw_mode",
    "yaw_jitter_range",
    "yaw_jitter_speed",
    "spinbot_speed",
    "switch_angle",
    "switch_ticks",
    "x_way_range",
    
    
    "micro_move",
    "move_jitter_intensity",
    "move_jitter_speed",
    "freestanding",
    "autopeek",


    
    "show_indicators",
    "show_speedometer",
    "show_killeffect",
    "show_hitmarker",
    "show_hitdmg",
    "show_movement_indicators",
    "killeffect",
    
    
    "Weapon_model",
    "change_model",
    "theme",
    "indicators_pos",
    
    
    "play_hitsound",
    "hitsound",
    "indicators_sound",
    "movement_indicator_sound",
    "anti_back_stab",
    "show_anti_back_stab_target",
    "randomize_angles_on_miss",
    "border_less",
    "keybindlist_enabled",
    "show_spect_list",


    "Logo"
}


local DEFAULT_VALUES = {
    
    antiaim_sidebar_button = false,
    misc_sidebar_button = false,
    
    
    fake_pitch_zeus = false,
    trashtalk = false,
    
    
    pitch_modification = false,
    pitch_mode = 1,
    custom_pitch = 0,
    
    
    pitch_jitter_mode = 1,
    pitch_jitter_speed = 5,
    
    
    yaw_modification = false,
    ["fix auto-strafer"] = true,
    look_at_nearest_player = true,
    base_yaw_offset = 0,
    yaw_mode = 1,
    yaw_jitter_range = 45,
    yaw_jitter_speed = 5,
    spinbot_speed = 10,
    switch_angle = 90,
    switch_ticks = 30,
    x_way_range = 60,
    
    
    micro_move = false,
    move_jitter_intensity = 50,
    move_jitter_speed = 3,
    
    
    show_indicators = false,
    show_speedometer = false,
    show_killeffect = true,
    killeffect = 1,
    
    
    Weapon_model = 0,
    change_model = false,
    theme = 1,
    indicators_pos = 2,
    
    
    play_hitsound = false,
    hitsound = 1,
    anti_back_stab = true,
    show_anti_back_stab_target = true,
    randomize_angles_on_miss = false,
    border_less = false
}


local function encode_json(data)
    local function serialize(obj)
        local t = type(obj)
        if t == "number" then
            return tostring(obj)
        elseif t == "boolean" then
            return obj and "true" or "false"
        elseif t == "string" then
            return '"' .. obj:gsub('"', '\\"'):gsub('\n', '\\n'):gsub('\r', '\\r'):gsub('\t', '\\t') .. '"'
        elseif t == "table" then
            local result = "{"
            local first = true
            for k, v in pairs(obj) do
                if not first then result = result .. "," end
                result = result .. '"' .. tostring(k) .. '":' .. serialize(v)
                first = false
            end
            return result .. "}"
        elseif obj == nil then
            return "null"
        end
        return "null"
    end
    return serialize(data)
end


local function decode_json(str)
    if not str or str == "" then return {} end
    
    
    str = str:gsub("^%s+", ""):gsub("%s+$", "")
    
    
    local function parse_value(s, pos)
        pos = pos or 1
        
        
        while pos <= #s and s:sub(pos, pos):match("%s") do
            pos = pos + 1
        end
        
        if pos > #s then return nil, pos end
        
        local char = s:sub(pos, pos)
        
        if char == '"' then
            
            local start_pos = pos + 1
            local end_pos = start_pos
            local escaped = false
            
            while end_pos <= #s do
                local c = s:sub(end_pos, end_pos)
                if escaped then
                    escaped = false
                elseif c == '\\' then
                    escaped = true
                elseif c == '"' then
                    break
                end
                end_pos = end_pos + 1
            end
            
            if end_pos > #s then
                return nil, pos 
            end
            
            local str_val = s:sub(start_pos, end_pos - 1)
            str_val = str_val:gsub('\\"', '"'):gsub('\\n', '\n'):gsub('\\r', '\r'):gsub('\\t', '\t')
            return str_val, end_pos + 1
            
        elseif char == '{' then
            
            local obj = {}
            pos = pos + 1
            
            
            while pos <= #s and s:sub(pos, pos):match("%s") do
                pos = pos + 1
            end
            
            
            if pos <= #s and s:sub(pos, pos) == '}' then
                return obj, pos + 1
            end
            
            while pos <= #s do
                
                while pos <= #s and s:sub(pos, pos):match("%s") do
                    pos = pos + 1
                end
                
                
                local key, new_pos = parse_value(s, pos)
                if not key then break end
                pos = new_pos
                
                
                while pos <= #s and (s:sub(pos, pos):match("%s") or s:sub(pos, pos) == ':') do
                    pos = pos + 1
                end
                
                
                local value, new_pos2 = parse_value(s, pos)
                pos = new_pos2
                
                obj[key] = value
                
                
                while pos <= #s and s:sub(pos, pos):match("%s") do
                    pos = pos + 1
                end
                
                
                if pos <= #s then
                    local next_char = s:sub(pos, pos)
                    if next_char == '}' then
                        pos = pos + 1
                        break
                    elseif next_char == ',' then
                        pos = pos + 1
                    else
                        break 
                    end
                end
            end
            
            return obj, pos
            
        elseif char:match("[%d%-]") then
            
            local start_pos = pos
            while pos <= #s and s:sub(pos, pos):match("[%d%.%-eE+]") do
                pos = pos + 1
            end
            local num_str = s:sub(start_pos, pos - 1)
            return tonumber(num_str), pos
            
        elseif s:sub(pos, pos + 3) == "true" then
            return true, pos + 4
        elseif s:sub(pos, pos + 4) == "false" then
            return false, pos + 5
        elseif s:sub(pos, pos + 3) == "null" then
            return nil, pos + 4
        else
            return nil, pos
        end
    end
    
    local result, _ = parse_value(str)
    return result or {}
end


local function file_exists(filename)
    local file = io.open(filename, "r")
    if file then
        file:close()
        return true
    end
    return false
end
local function listFilesRecursive(base_path)
    local files = {}
    local command = 'dir "' .. base_path .. '" /s /b /a-d' 
    local p = io.popen(command)
    if not p then
        return files
    end
    for file in p:lines() do
        local filename = file:match("([^\\]+)$") 
        table.insert(files, {path = file, file = filename})
    end
    p:close()
    return files
end

function ConfigManager.ListConfigs(directory)
    local base_path = directory or "." 
    local all_files = listFilesRecursive(base_path)
    local config_files_data = {}
    local config_names = {}

    -- debug: print("Available configs in '" .. base_path .. "':")
    
    
    for _, file_info in pairs(all_files) do
        if file_info.file:match("%.gp$") then
            local config_name = file_info.file:gsub("%.gp$", "")
            table.insert(config_files_data, {
                name = config_name,
                path = file_info.path,
                file = file_info.file
            })
            table.insert(config_names, config_name)
            -- debug: print("  - " .. config_name .. " (" .. file_info.path .. ")")
        end
    end
    
    if #config_files_data == 0 then
        -- debug: print("  No config files found. Use CreateNewFile() to create a new config")
    else
        -- debug: print("Found " .. #config_files_data .. " config file(s)")
    end
    
    return config_files_data, config_names
end

function ConfigManager.LoadDefaults()
    -- debug: print("Loading default values...")
    local loaded_count = 0
    
    for var_name, default_value in pairs(DEFAULT_VALUES) do
        UI.SetValue(var_name, default_value)
        loaded_count = loaded_count + 1
    end
    
    -- debug: print("Loaded " .. loaded_count .. " default values")
end


function ConfigManager.CreateNewFile(name, UI)
    if not name or name == "" then
        -- debug: print("Error: Config name cannot be empty")
        return false
    end
    
    local filename = name .. ".gp"
    
    
    if file_exists(filename) then
        -- debug: print("Warning: Config file '" .. filename .. "' already exists")
        return false
    end
    
    
    local config_data = {}
    
    for _, var_name in ipairs(CONFIG_VARIABLES) do
        local current_value = UI.GetValue(var_name)
        if current_value ~= nil then
            config_data[var_name] = current_value
        else
            config_data[var_name] = DEFAULT_VALUES[var_name]
        end
    end
    
    
    local file = io.open(filename, "w")
    if file then
        file:write(encode_json(config_data))
        file:close()
        -- debug: print("Created new config: " .. filename)
        return true
    else
        -- debug: print("Error: Could not create config file: " .. filename)
        return false
    end
end


function ConfigManager.LoadConfigFromFile(name, UI)
    if not name or name == "" then
        -- debug: print("Error: Config name cannot be empty")
        return false
    end
    
    local filename = name .. ".gp"
    
    
    if not file_exists(filename) then
        -- debug: print("Error: Config file '" .. filename .. "' does not exist")
        return false
    end
    
    
    local file = io.open(filename, "r")
    if not file then
        -- debug: print("Error: Could not open config file: " .. filename)
        return false
    end
    
    local content = file:read("*all")
    file:close()
    
    if not content or content == "" then
        -- debug: print("Error: Config file is empty: " .. filename)
        return false
    end
    
    
    local config_data = decode_json(content)
    if not config_data then
        -- debug: print("Error: Could not parse config file: " .. filename)
        return false
    end
    
    
    local loaded_count = 0
    local missing_count = 0
    
    for _, var_name in ipairs(CONFIG_VARIABLES) do
        local value = config_data[var_name]
        
        if value ~= nil then
            UI.SetValue(var_name, value)
            loaded_count = loaded_count + 1
        else
            
            local default_value = DEFAULT_VALUES[var_name]
            if default_value ~= nil then
                UI.SetValue(var_name, default_value)
                missing_count = missing_count + 1
            end
        end
    end
    
    
    local keybind_count = 0
    -- debug: print("Debug: config_data.keybinds type:", type(config_data.keybinds))
    if config_data.keybinds then
        -- debug: print("Debug: keybinds exists, type:", type(config_data.keybinds))
        if type(config_data.keybinds) == "table" then
            -- debug: print("Debug: keybinds is table, iterating...")
            UI.keybinds = UI.keybinds or {}
            
            for keybind_id, keybind_data in pairs(config_data.keybinds) do
                -- debug: print("Debug: Processing keybind:", keybind_id, "type:", type(keybind_data))
                if not UI.keybinds[keybind_id] then
                    UI.keybinds[keybind_id] = {}
                end
                
                
                if type(keybind_data) == "table" then
                    UI.keybinds[keybind_id].key = keybind_data.key
                    UI.keybinds[keybind_id].type = keybind_data.type or "toggle"
                    UI.keybinds[keybind_id].enabled = keybind_data.enabled or false
                    UI.keybinds[keybind_id].waitingForKey = false 
                    UI.keybinds[keybind_id].showTypeDropdown = false 
                    
                    keybind_count = keybind_count + 1
                else
                    -- debug: print("Warning: Invalid keybind data for '" .. keybind_id .. "', skipping")
                end
            end
        end
    end
    
    -- debug: print("Loaded config: " .. filename .. " (" .. loaded_count .. " values loaded, " .. missing_count .. " defaults used, " .. keybind_count .. " keybinds loaded)")
    return true
end


function ConfigManager.SaveConfigToFile(name, UI)
    if not name or name == "" then
        -- debug: print("Error: Config name cannot be empty")
        return false
    end
    
    local filename = name .. ".gp"
    
    
    local config_data = {}
    local saved_count = 0
    
    for _, var_name in ipairs(CONFIG_VARIABLES) do
        
        local value = UI.GetValue(var_name)
        
        
        if value ~= nil then
            config_data[var_name] = value
            saved_count = saved_count + 1
        else
            
            local default_value = DEFAULT_VALUES[var_name]
            if default_value ~= nil then
                config_data[var_name] = default_value
            end
        end
    end
    
    
    local keybind_count = 0
    if UI.keybinds then
        config_data.keybinds = {}
        
        for keybind_id, keybind_data in pairs(UI.keybinds) do
            
            if keybind_data.key or keybind_data.type ~= "toggle" or keybind_data.enabled then
                config_data.keybinds[keybind_id] = {
                    key = keybind_data.key,
                    type = keybind_data.type,
                    enabled = keybind_data.enabled
                    
                }
                keybind_count = keybind_count + 1
            end
        end
        
        
        if keybind_count == 0 then
            config_data.keybinds = nil
        end
    end
    
    
    local json_content = encode_json(config_data)
    if not json_content then
        -- debug: print("Error: Could not encode config data to JSON")
        return false
    end
    
    
    local file = io.open(filename, "w")
    if not file then
        -- debug: print("Error: Could not create/open config file: " .. filename)
        return false
    end
    
    file:write(json_content)
    file:close()
    
    -- debug: print("Saved config: " .. filename .. " (" .. saved_count .. " values saved, " .. keybind_count .. " keybinds saved)")
    return true
end

return ConfigManager