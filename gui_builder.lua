-- GUI Builder Script for plaguecheat.cc | by fate
-- Enable Unsafe Scripts!

local layers = {}
local selectedLayerIndex = nil

local draggingElementIndex = nil
local resizingElementIndex = nil
local draggingLineEndpoint = nil
local dragOffsetX, dragOffsetY = 0, 0
local lastClickTime = 0
local clickCooldown = 0.1

local showColorControls = false
local colorSliderType = "r" -- rgba

local defaultFontName = "ArialDefault"
local defaultFontSize = 14

-- static & fallbacks
Renderer.LoadFontFromFile("Arial", "Arial", defaultFontSize, true)
Renderer.LoadFontFromFile(defaultFontName, "Arial", defaultFontSize, true)
Renderer.LoadFontFromFile("Arial12", "Arial", 12, true)
Renderer.LoadFontFromFile("Arial24", "Arial", 24, true)
Renderer.LoadFontFromFile("Arial32", "Arial", 32, true)

local fontInstances = {}

for size = 8, 31 do
    local fontKey = "Arial" .. size
    Renderer.LoadFontFromFile(fontKey, "Arial", size, true)
    fontInstances[size] = fontKey
end

local function GetFontForSize(targetSize)
    local clampedSize = math.max(8, math.min(31, targetSize))
    return fontInstances[clampedSize], clampedSize
end

local menuToggle = Menu.Checker("GUI Builder", false) --> Main Toggle

-- Text input key maps
local keyToCharMap = {
    [0x41] = "a", [0x42] = "b", [0x43] = "c", [0x44] = "d", [0x45] = "e", [0x46] = "f", [0x47] = "g",
    [0x48] = "h", [0x49] = "i", [0x4A] = "j", [0x4B] = "k", [0x4C] = "l", [0x4D] = "m", [0x4E] = "n",
    [0x4F] = "o", [0x50] = "p", [0x51] = "q", [0x52] = "r", [0x53] = "s", [0x54] = "t", [0x55] = "u",
    [0x56] = "v", [0x57] = "w", [0x58] = "x", [0x59] = "y", [0x5A] = "z",
    [0x30] = "0", [0x31] = "1", [0x32] = "2", [0x33] = "3", [0x34] = "4", [0x35] = "5", [0x36] = "6",
    [0x37] = "7", [0x38] = "8", [0x39] = "9",
    [0x20] = " ", [0xBA] = ";", [0xBB] = "=", [0xBC] = ",", [0xBD] = "-", [0xBE] = ".", [0xBF] = "/",
    [0xC0] = "`", [0xDB] = "[", [0xDC] = "\\", [0xDD] = "]", [0xDE] = "'"
}

local shiftedKeyMap = {
    [0x30] = ")", [0x31] = "!", [0x32] = "@", [0x33] = "#", [0x34] = "$", [0x35] = "%", [0x36] = "^",
    [0x37] = "&", [0x38] = "*", [0x39] = "(",
    [0xBA] = ":", [0xBB] = "+", [0xBC] = "<", [0xBD] = "_", [0xBE] = ">", [0xBF] = "?",
    [0xC0] = "~", [0xDB] = "{", [0xDC] = "|", [0xDD] = "}", [0xDE] = "\""
}

local keyRepeatDelay = 0.5
local keyRepeatRate = 0.08
local activeKeys = {}
local textEditMode = false
local editingTextElement = nil
local textCursor = 0
local lastKeyTime = 0

local function ProcessTextInput(element)
    if not element or element.type ~= "DrawText" or not textEditMode then return end
    
    local currentTime = Globals.GetCurrentTime()
    local isShiftPressed = Input.GetKeyDown(0x10)
    local isCtrlPressed = Input.GetKeyDown(0x11)
    
    local function ShouldTriggerKey(keyCode)
        if Input.GetKeyDown(keyCode) then
            if not activeKeys[keyCode] then
                activeKeys[keyCode] = currentTime
                return true
            else
                local timeSinceFirst = currentTime - activeKeys[keyCode]
                if timeSinceFirst >= keyRepeatDelay then
                    local repeatTime = timeSinceFirst - keyRepeatDelay
                    local repeatCount = math.floor(repeatTime / keyRepeatRate)
                    local expectedTime = keyRepeatDelay + (repeatCount + 1) * keyRepeatRate
                    
                    if timeSinceFirst >= expectedTime then
                        return true
                    end
                end
            end
        else
            activeKeys[keyCode] = nil
        end
        return false
    end
    
    if ShouldTriggerKey(0x08) then -- Backspace
        if #element.properties.text > 0 and textCursor > 0 then
            element.properties.text = element.properties.text:sub(1, textCursor - 1) .. element.properties.text:sub(textCursor + 1)
            textCursor = textCursor - 1
        end
        return
    end
    
    if ShouldTriggerKey(0x2E) then -- Delete
        if textCursor < #element.properties.text then
            element.properties.text = element.properties.text:sub(1, textCursor) .. element.properties.text:sub(textCursor + 2)
        end
        return
    end
    
    if ShouldTriggerKey(0x25) then -- Left arrow
        textCursor = math.max(0, textCursor - 1)
        return
    end
    
    if ShouldTriggerKey(0x27) then -- Right arrow
        textCursor = math.min(#element.properties.text, textCursor + 1)
        return
    end
    
    if ShouldTriggerKey(0x24) then -- Home
        textCursor = 0
        return
    end
    
    if ShouldTriggerKey(0x23) then -- End
        textCursor = #element.properties.text
        return
    end
    
    if ShouldTriggerKey(0x0D) then -- Enter
        textEditMode = false
        editingTextElement = nil
        textCursor = 0
        return
    end
    
    -- Handle Escape key to cancel editing
    if ShouldTriggerKey(0x1B) then -- Escape
        textEditMode = false
        editingTextElement = nil
        textCursor = 0
        return
    end
    
    -- Handle Ctrl+A (Select All)
    if isCtrlPressed and ShouldTriggerKey(0x41) then
        textCursor = #element.properties.text
        return
    end
    
    -- Handle regular character input
    for keyCode, char in pairs(keyToCharMap) do
        if ShouldTriggerKey(keyCode) then
            if isShiftPressed then
                if keyCode >= 0x41 and keyCode <= 0x5A then -- Letters
                    char = char:upper()
                elseif shiftedKeyMap[keyCode] then
                    char = shiftedKeyMap[keyCode]
                end
            end
            

            if #element.properties.text < 100 then
                element.properties.text = element.properties.text:sub(1, textCursor) .. char .. element.properties.text:sub(textCursor + 1)
                textCursor = textCursor + 1
            end
            
            break 
        end
    end
end

local function IsPointInRect(px, py, rx, ry, rw, rh)
    return px >= rx and px <= rx + rw and py >= ry and py <= ry + rh
end

local function IsPointInCircle(px, py, cx, cy, cr)
    local dx, dy = px - cx, py - cy
    return (dx * dx + dy * dy) <= (cr * cr)
end

local function RotatePoint(x, y, centerX, centerY, angleDegrees)
    local rad = math.rad(angleDegrees)
    local cos_a = math.cos(rad)
    local sin_a = math.sin(rad)
    
    local dx = x - centerX
    local dy = y - centerY
    
    local rotatedX = centerX + (dx * cos_a - dy * sin_a)
    local rotatedY = centerY + (dx * sin_a + dy * cos_a)
    
    return rotatedX, rotatedY
end

local function GetRotatedLinePoints(props)
    if not props.rotation or props.rotation == 0 then
        return props.start.x, props.start.y, props.endPos.x, props.endPos.y
    end
    
    local centerX = (props.start.x + props.endPos.x) / 2
    local centerY = (props.start.y + props.endPos.y) / 2
    
    local rotatedStartX, rotatedStartY = RotatePoint(props.start.x, props.start.y, centerX, centerY, props.rotation)
    local rotatedEndX, rotatedEndY = RotatePoint(props.endPos.x, props.endPos.y, centerX, centerY, props.rotation)
    
    return rotatedStartX, rotatedStartY, rotatedEndX, rotatedEndY
end

local function IsPointNearEndpoint(px, py, endpointX, endpointY, tolerance)
    local dx = px - endpointX
    local dy = py - endpointY
    return math.sqrt(dx * dx + dy * dy) <= tolerance
end

local function DrawCircleOutline(centerX, centerY, radius, color, segments, thickness)
    segments = segments or 32
    thickness = thickness or 2
    local angleStep = (2 * math.pi) / segments
    
    for i = 0, segments - 1 do
        local angle1 = i * angleStep
        local angle2 = (i + 1) * angleStep
        
        local x1 = centerX + math.cos(angle1) * radius
        local y1 = centerY + math.sin(angle1) * radius
        local x2 = centerX + math.cos(angle2) * radius
        local y2 = centerY + math.sin(angle2) * radius
        
        Renderer.DrawLine(Vector2D(x1, y1), Vector2D(x2, y2), color, thickness)
    end
end

local function DrawRectOutline(x, y, width, height, color, thickness)
    thickness = thickness or 2
    
    local topLeft = Vector2D(x, y)
    local topRight = Vector2D(x + width, y)
    local bottomRight = Vector2D(x + width, y + height)
    local bottomLeft = Vector2D(x, y + height)
    
    Renderer.DrawLine(topLeft, topRight, color, thickness)
    Renderer.DrawLine(topRight, bottomRight, color, thickness)
    Renderer.DrawLine(bottomRight, bottomLeft, color, thickness)
    Renderer.DrawLine(bottomLeft, topLeft, color, thickness)
end

-- Export function
local function ExportGUI()
    local file = io.open("C:\\plaguecheat.cc\\exported_gui.lua", "w")
    if file then
        print("Exporting GUI to exported_gui.lua...")
        file:write("-- Generated GUI Layout\n")
        
        local usedFontSizes = {}
        for _, layer in ipairs(layers) do
            if layer.type == "DrawText" and layer.properties.fontSize then
                usedFontSizes[layer.properties.fontSize] = true
            end
        end
        

        if next(usedFontSizes) then
            file:write("\n-- Font loading for used sizes\n")
            for fontSize, _ in pairs(usedFontSizes) do
                file:write(string.format("Renderer.LoadFontFromFile(\"Arial%d\", \"Arial\", %d, true)\n", fontSize, fontSize))
            end
            file:write("\n")
        end
        
        file:write("-- Rotation helper function\n")
        file:write("local function RotatePoint(x, y, centerX, centerY, angleDegrees)\n")
        file:write("    local rad = math.rad(angleDegrees)\n")
        file:write("    local cos_a = math.cos(rad)\n")
        file:write("    local sin_a = math.sin(rad)\n")
        file:write("    local dx = x - centerX\n")
        file:write("    local dy = y - centerY\n")
        file:write("    local rotatedX = centerX + (dx * cos_a - dy * sin_a)\n")
        file:write("    local rotatedY = centerY + (dx * sin_a + dy * cos_a)\n")
        file:write("    return rotatedX, rotatedY\n")        file:write("end\n\n")
        file:write("local function RenderGUI()\n")
        for i, layer in ipairs(layers) do
            local props = layer.properties
            file:write(string.format("    -- ELEMENT_%d_START: %s\n", i, layer.type))
              if layer.type == "DrawRect" and props.position and props.size and props.color then
                file:write(string.format("    -- META: pos=(%d,%d) size=(%d,%d) rounding=%d rotation=%.1f\n", 
                    props.position.x, props.position.y, props.size.width, props.size.height, 
                    props.rounding or 0, props.rotation or 0))
                file:write(string.format("    Renderer.DrawRect(Vector2D(%d, %d), Vector2D(%d, %d), Color(%d, %d, %d, %d), %d)\n",
                    props.position.x, props.position.y,
                    props.position.x + props.size.width, props.position.y + props.size.height,
                    props.color.r, props.color.g, props.color.b, props.color.a,
                    props.rounding or 0))
            elseif layer.type == "DrawLineRect" and props.position and props.size and props.color then
                file:write(string.format("    -- META: pos=(%d,%d) size=(%d,%d) thickness=%d rotation=%.1f\n", 
                    props.position.x, props.position.y, props.size.width, props.size.height, 
                    props.thickness or 2, props.rotation or 0))
                if props.rotation and props.rotation ~= 0 then
                    file:write(string.format("    -- Simulated rotated rectangle using lines\n"))
                    local centerX = props.position.x + props.size.width / 2
                    local centerY = props.position.y + props.size.height / 2
                    local halfW = props.size.width / 2
                    local halfH = props.size.height / 2
                    
                    local corners = {
                        {-halfW, -halfH}, {halfW, -halfH}, {halfW, halfH}, {-halfW, halfH}
                    }
                      for i = 1, 4 do
                        local nextI = (i % 4) + 1
                        local x1, y1 = RotatePoint(centerX + corners[i][1], centerY + corners[i][2], centerX, centerY, props.rotation)
                        local x2, y2 = RotatePoint(centerX + corners[nextI][1], centerY + corners[nextI][2], centerX, centerY, props.rotation)
                        file:write(string.format("    Renderer.DrawLine(Vector2D(%.1f, %.1f), Vector2D(%.1f, %.1f), Color(%d, %d, %d, %d), %d)\n",
                            x1, y1, x2, y2, props.color.r, props.color.g, props.color.b, props.color.a, props.thickness or 2))
                    end
                else
                    file:write(string.format("    -- Line-based rectangle outline\n"))
                    file:write(string.format("    Renderer.DrawLine(Vector2D(%d, %d), Vector2D(%d, %d), Color(%d, %d, %d, %d), %d) -- Top\n",
                        props.position.x, props.position.y, props.position.x + props.size.width, props.position.y,
                        props.color.r, props.color.g, props.color.b, props.color.a, props.thickness or 2))
                    file:write(string.format("    Renderer.DrawLine(Vector2D(%d, %d), Vector2D(%d, %d), Color(%d, %d, %d, %d), %d) -- Right\n",
                        props.position.x + props.size.width, props.position.y, props.position.x + props.size.width, props.position.y + props.size.height,
                        props.color.r, props.color.g, props.color.b, props.color.a, props.thickness or 2))
                    file:write(string.format("    Renderer.DrawLine(Vector2D(%d, %d), Vector2D(%d, %d), Color(%d, %d, %d, %d), %d) -- Bottom\n",
                        props.position.x + props.size.width, props.position.y + props.size.height, props.position.x, props.position.y + props.size.height,
                        props.color.r, props.color.g, props.color.b, props.color.a, props.thickness or 2))
                    file:write(string.format("    Renderer.DrawLine(Vector2D(%d, %d), Vector2D(%d, %d), Color(%d, %d, %d, %d), %d) -- Left\n",
                        props.position.x, props.position.y + props.size.height, props.position.x, props.position.y,
                        props.color.r, props.color.g, props.color.b, props.color.a, props.thickness or 2))
                end            elseif layer.type == "DrawRectFilled" and props.position and props.size and props.color then
                file:write(string.format("    -- META: pos=(%d,%d) size=(%d,%d) rounding=%d rotation=%.1f\n", 
                    props.position.x, props.position.y, props.size.width, props.size.height, 
                    props.rounding or 0, props.rotation or 0))
                if props.rotation and props.rotation ~= 0 then
                    file:write(string.format("    -- Simulated rotated filled rectangle using thick lines\n"))
                    local centerX = props.position.x + props.size.width / 2
                    local centerY = props.position.y + props.size.height / 2
                    local halfW = props.size.width / 2
                    local halfH = props.size.height / 2
                    
                    local corners = {
                        {-halfW, -halfH}, {halfW, -halfH}, {halfW, halfH}, {-halfW, halfH}
                    }
                    
                    for i = 1, 4 do
                        local nextI = (i % 4) + 1
                        local x1, y1 = RotatePoint(centerX + corners[i][1], centerY + corners[i][2], centerX, centerY, props.rotation)
                        local x2, y2 = RotatePoint(centerX + corners[nextI][1], centerY + corners[nextI][2], centerX, centerY, props.rotation)
                        file:write(string.format("    Renderer.DrawLine(Vector2D(%.1f, %.1f), Vector2D(%.1f, %.1f), Color(%d, %d, %d, %d), 3)\n",
                            x1, y1, x2, y2, props.color.r, props.color.g, props.color.b, props.color.a))
                    end
                else
                    file:write(string.format("    Renderer.DrawRectFilled(Vector2D(%d, %d), Vector2D(%d, %d), Color(%d, %d, %d, %d), %d)\n",
                        props.position.x, props.position.y,
                        props.position.x + props.size.width, props.position.y + props.size.height,
                        props.color.r, props.color.g, props.color.b, props.color.a,                        props.rounding or 0))
                end            elseif layer.type == "DrawText" and props.position and props.color and props.text then
                local rotationValue = props.rotation or 0.0
                local fontSize = props.fontSize or 16
                local fontName = string.format("Arial%d", fontSize)
                file:write(string.format("    -- META: pos=(%d,%d) fontSize=%d rotation=%.1f\n", 
                    props.position.x, props.position.y, fontSize, rotationValue))
                file:write(string.format("    Renderer.DrawText(%s, %s, Vector2D(%d, %d), %s, %s, Color(%d, %d, %d, %d))\n",
                    fontName,
                    string.format("%q", props.text or "Text"),
                    props.position.x, props.position.y,
                    props.centered and "true" or "false", 
                    props.outlined and "true" or "false",
                    props.color.r, props.color.g, props.color.b, props.color.a))elseif layer.type == "DrawCircleFilled" and props.position and props.color and props.radius then
                file:write(string.format("    -- META: pos=(%d,%d) radius=%d rotation=%.1f\n", 
                    props.position.x, props.position.y, props.radius, props.rotation or 0))
                file:write(string.format("    Renderer.DrawCircleFilled(Vector2D(%d, %d), Color(%d, %d, %d, %d), %d)\n",
                    props.position.x, props.position.y,
                    props.color.r, props.color.g, props.color.b, props.color.a,props.radius)) elseif layer.type == "DrawCircle" and props.position and props.color and props.radius then
                file:write(string.format("    -- META: pos=(%d,%d) radius=%d thickness=%d rotation=%.1f\n", 
                    props.position.x, props.position.y, props.radius, props.thickness or 2, props.rotation or 0))
                file:write(string.format("    -- Circle outline using DrawLine (32 segments)\n"))
                file:write(string.format("    local centerX, centerY, radius = %d, %d, %d\n", props.position.x, props.position.y, props.radius))
                file:write(string.format("    local segments = 32\n"))
                file:write(string.format("    local angleStep = (2 * math.pi) / segments\n"))
                file:write(string.format("    for i = 0, segments - 1 do\n"))
                file:write(string.format("        local angle1 = i * angleStep\n"))
                file:write(string.format("        local angle2 = (i + 1) * angleStep\n"))
                file:write(string.format("        local x1 = centerX + math.cos(angle1) * radius\n"))
                file:write(string.format("        local y1 = centerY + math.sin(angle1) * radius\n"))
                file:write(string.format("        local x2 = centerX + math.cos(angle2) * radius\n"))
                file:write(string.format("        local y2 = centerY + math.sin(angle2) * radius\n"))
                file:write(string.format("        Renderer.DrawLine(Vector2D(x1, y1), Vector2D(x2, y2), Color(%d, %d, %d, %d), %d)\n",
                    props.color.r, props.color.g, props.color.b, props.color.a, props.thickness or 2))
                file:write(string.format("    end\n"))
            elseif layer.type == "DrawLine" and props.start and props.endPos and props.color then
                file:write(string.format("    -- META: start=(%d,%d) end=(%d,%d) thickness=%d rotation=%.1f\n", 
                    props.start.x, props.start.y, props.endPos.x, props.endPos.y, 
                    props.thickness or 2, props.rotation or 0))
                if props.rotation and props.rotation ~= 0 then
                    local startX, startY, endX, endY = GetRotatedLinePoints(props)
                    file:write(string.format("    Renderer.DrawLine(Vector2D(%.1f, %.1f), Vector2D(%.1f, %.1f), Color(%d, %d, %d, %d), %d)\n",
                        startX, startY, endX, endY,
                        props.color.r, props.color.g, props.color.b, props.color.a,
                        props.thickness or 2))
                else
                    file:write(string.format("    Renderer.DrawLine(Vector2D(%d, %d), Vector2D(%d, %d), Color(%d, %d, %d, %d), %d)\n",
                        props.start.x, props.start.y, props.endPos.x, props.endPos.y,
                        props.color.r, props.color.g, props.color.b, props.color.a,                        props.thickness or 2))
                end
            end
            file:write(string.format("    -- ELEMENT_%d_END\n", i))
        end
        file:write("end\n\n")
        file:write("-- To use: Call RenderGUI() in your OnRenderer callback\n")
        file:write("-- Cheat.RegisterCallback(\"OnRenderer\", RenderGUI)\n")
        file:close()
        print("Export completed successfully! " .. #layers .. " elements exported.")
    else
        print("Error: Could not create export file.")
    end
end

local function LoadGUI()
    local file = io.open("C:\\plaguecheat.cc\\exported_gui.lua", "r")
    if not file then
        print("Error: exported_gui.lua file not found")
        return
    end
    
    local content = file:read("*all")
    file:close()
    
    if not content or content == "" then
        print("Error: exported_gui.lua is empty or unreadable")
        return
    end
    
    if not content:find("Generated GUI Layout") then
        print("Error: File doesn't appear to be a valid exported GUI")
        return
    end
    
    layers = {}
    selectedLayerIndex = nil
    
    local loadedCount = 0
    
    for elementNum, elementType in content:gmatch("-- ELEMENT_(%d+)_START: (%w+)") do
        local elementPattern = "-- ELEMENT_" .. elementNum .. "_START: " .. elementType .. "(.-)\n%s*-- ELEMENT_"
        local elementBlock = content:match(elementPattern) or content:match("-- ELEMENT_" .. elementNum .. "_START: " .. elementType .. "(.*)")
        
        if elementBlock then
            local layer = { type = elementType, properties = {} }
            if elementType == "DrawRect" then
                local x, y, w, h, rounding = elementBlock:match("-- META: pos=%((%d+),(%d+)%) size=%((%d+),(%d+)%) rounding=(%d+)")
                local rotation = elementBlock:match("rotation=([%d%.%-]+)") or "0"
                local r, g, b, a = elementBlock:match("Color%((%d+), (%d+), (%d+), (%d+)%)")
                
                if x and r then
                    layer.properties = {
                        position = { x = tonumber(x), y = tonumber(y) },
                        size = { width = tonumber(w), height = tonumber(h) },
                        color = { r = tonumber(r), g = tonumber(g), b = tonumber(b), a = tonumber(a) },
                        rounding = tonumber(rounding),
                        rotation = tonumber(rotation) or 0
                    }
                    table.insert(layers, layer)
                    loadedCount = loadedCount + 1
                end                  elseif elementType == "DrawLineRect" then
                local x, y, w, h, thickness = elementBlock:match("-- META: pos=%((%d+),(%d+)%) size=%((%d+),(%d+)%) thickness=(%d+)")
                local rotation = elementBlock:match("rotation=([%d%.%-]+)") or "0"
                local r, g, b, a = elementBlock:match("Color%((%d+), (%d+), (%d+), (%d+)%)")
                
                if x and r then
                    layer.properties = {
                        position = { x = tonumber(x), y = tonumber(y) },
                        size = { width = tonumber(w), height = tonumber(h) },
                        color = { r = tonumber(r), g = tonumber(g), b = tonumber(b), a = tonumber(a) },
                        thickness = tonumber(thickness),
                        rotation = tonumber(rotation) or 0,
                        rounding = 0
                    }
                    table.insert(layers, layer)
                    loadedCount = loadedCount + 1
                end                  elseif elementType == "DrawRectFilled" then
                local x, y, w, h, rounding = elementBlock:match("-- META: pos=%((%d+),(%d+)%) size=%((%d+),(%d+)%) rounding=(%d+)")
                local rotation = elementBlock:match("rotation=([%d%.%-]+)") or "0"
                local r, g, b, a = elementBlock:match("Color%((%d+), (%d+), (%d+), (%d+)%)")
                
                if x and r then
                    layer.properties = {
                        position = { x = tonumber(x), y = tonumber(y) },
                        size = { width = tonumber(w), height = tonumber(h) },
                        color = { r = tonumber(r), g = tonumber(g), b = tonumber(b), a = tonumber(a) },
                        rounding = tonumber(rounding),
                        rotation = tonumber(rotation) or 0
                    }
                    table.insert(layers, layer)
                    loadedCount = loadedCount + 1
                end            elseif elementType == "DrawText" then
                local x, y, fontSize = elementBlock:match("-- META: pos=%((%d+),(%d+)%) fontSize=(%d+)")
                local rotation = elementBlock:match("rotation=([%d%.%-]+)") or "0"
                local font, text = elementBlock:match('Renderer%.DrawText%(([^,]+), "([^"]+)"')
                local r, g, b, a = elementBlock:match("Color%((%d+), (%d+), (%d+), (%d+)%)")
                local centered = elementBlock:match("Vector2D%([^%)]+%), ([^,]+),") == "true"
                local outlined = elementBlock:match("Vector2D%([^%)]+%), [^,]+, ([^,]+),") == "true"                if x and r and font and text then
                    layer.properties = {
                        position = { x = tonumber(x), y = tonumber(y) },
                        text = text,
                        font = font,
                        fontSize = tonumber(fontSize) or 16,
                        centered = centered,
                        outlined = outlined,
                        color = { r = tonumber(r), g = tonumber(g), b = tonumber(b), a = tonumber(a) },
                        rotation = tonumber(rotation) or 0
                    }
                    table.insert(layers, layer)
                    loadedCount = loadedCount + 1
                    print("Loaded DrawText: " .. text .. " (" .. font .. ")")                else
                    print("Failed to parse DrawText element: x=" .. tostring(x) .. ", r=" .. tostring(r) .. ", font=" .. tostring(font) .. ", text=" .. tostring(text) .. ", rotation=" .. tostring(rotation))
                end
            elseif elementType == "DrawCircleFilled" then
                local x, y, radius = elementBlock:match("-- META: pos=%((%d+),(%d+)%) radius=(%d+)")
                local rotation = elementBlock:match("rotation=([%d%.%-]+)") or "0"
                local r, g, b, a = elementBlock:match("Color%((%d+), (%d+), (%d+), (%d+)%)")
                
                if x and r then
                    layer.properties = {
                        position = { x = tonumber(x), y = tonumber(y) },
                        radius = tonumber(radius),
                        color = { r = tonumber(r), g = tonumber(g), b = tonumber(b), a = tonumber(a) },
                        rotation = tonumber(rotation) or 0
                    }
                    table.insert(layers, layer)
                    loadedCount = loadedCount + 1
                end                  elseif elementType == "DrawCircle" then
                local x, y, radius, thickness = elementBlock:match("-- META: pos=%((%d+),(%d+)%) radius=(%d+) thickness=(%d+)")
                local rotation = elementBlock:match("rotation=([%d%.%-]+)") or "0"
                local r, g, b, a = elementBlock:match("Color%((%d+), (%d+), (%d+), (%d+)%)")
                
                if x and r then
                    layer.properties = {
                        position = { x = tonumber(x), y = tonumber(y) },
                        radius = tonumber(radius),
                        color = { r = tonumber(r), g = tonumber(g), b = tonumber(b), a = tonumber(a) },
                        thickness = tonumber(thickness),
                        rotation = tonumber(rotation) or 0
                    }
                    table.insert(layers, layer)
                    loadedCount = loadedCount + 1
                end                  elseif elementType == "DrawLine" then
                local x1, y1, x2, y2, thickness = elementBlock:match("-- META: start=%((%d+),(%d+)%) end=%((%d+),(%d+)%) thickness=(%d+)")
                local rotation = elementBlock:match("rotation=([%d%.%-]+)") or "0"
                local r, g, b, a = elementBlock:match("Color%((%d+), (%d+), (%d+), (%d+)%)")
                
                if x1 and r then
                    layer.properties = {
                        start = { x = tonumber(x1), y = tonumber(y1) },
                        endPos = { x = tonumber(x2), y = tonumber(y2) },
                        color = { r = tonumber(r), g = tonumber(g), b = tonumber(b), a = tonumber(a) },
                        thickness = tonumber(thickness),
                        rotation = tonumber(rotation) or 0
                    }
                    table.insert(layers, layer)
                    loadedCount = loadedCount + 1
                end
            end
        end
    end
    
    print("GUI loaded successfully! " .. loadedCount .. " elements restored.")
    if loadedCount > 0 then
        selectedLayerIndex = 1
    end
end

-- Main render function
local function RenderGUIBuilder()
    local isVisible = menuToggle:GetBool()
    if not isVisible then
        draggingElementIndex = nil
        resizingElementIndex = nil
        editingTextElement = nil
        textEditMode = false
        return
    end
    
    if textEditMode and editingTextElement then
        ProcessTextInput(editingTextElement)
    end
    local guiX, guiY = 50, 50
    local guiWidth, guiHeight = 360, 550
    
    Renderer.DrawRectFilled(Vector2D(guiX, guiY), Vector2D(guiX + guiWidth, guiY + guiHeight), Color(30, 30, 30, 240), 5)
    
    Renderer.DrawText("Arial", "GUI Builder", Vector2D(guiX + 10, guiY + 10), false, true, Color(255, 255, 255, 255))
    
    local yOffset = 40
    local buttonTypes = {"DrawRect", "DrawLineRect", "DrawRectFilled", "DrawText", "DrawCircleFilled", "DrawCircle", "DrawLine"}
    local mousePos = Input.GetCursorPos()
    local leftClick = Input.GetKeyDown(0x01)
    local currentTime = Globals.GetCurrentTime()
    
    local canClick = not leftClick or (currentTime - lastClickTime) > clickCooldown
    
    for i, buttonType in ipairs(buttonTypes) do
        local col = (i - 1) % 2
        local row = math.floor((i - 1) / 2)
        
        local btnX = guiX + 10 + col * 170
        local btnY = guiY + yOffset + row * 30
        local btnW, btnH = 160, 25
        
        local isHover = mousePos.x >= btnX and mousePos.x <= btnX + btnW and 
                       mousePos.y >= btnY and mousePos.y <= btnY + btnH
        
        local btnColor = isHover and Color(60, 60, 60, 255) or Color(40, 40, 40, 255)
        Renderer.DrawRectFilled(Vector2D(btnX, btnY), Vector2D(btnX + btnW, btnY + btnH), btnColor, 3)
        
        Renderer.DrawText("Arial", "Add " .. buttonType, Vector2D(btnX + 8, btnY + 5), false, true, Color(255, 255, 255, 255))
        
        if isHover and leftClick and canClick then
            lastClickTime = currentTime
            local newLayer = { type = buttonType, properties = {} }
            local x, y = guiX + guiWidth + 80 + (#layers * 30), guiY + 120 + (#layers * 40)
            if buttonType == "DrawRect" or buttonType == "DrawLineRect" or buttonType == "DrawRectFilled" then
                newLayer.properties = {
                    position = { x = x, y = y },
                    size = { width = 100, height = 50 },
                    color = { r = 255, g = 100, b = 100, a = 200 },
                    rounding = 0,
                    rotation = 0,
                    thickness = (buttonType == "DrawLineRect") and 2 or nil -- Only for line-based rect
                }
            elseif buttonType == "DrawText" then
                newLayer.properties = {
                    font = fontInstances[16] or defaultFontName,
                    fontSize = 16,
                    text = "Text " .. (#layers + 1),
                    position = { x = x, y = y },
                    centered = false,
                    outlined = true,
                    color = { r = 255, g = 255, b = 255, a = 255 },
                    rotation = 0,
                    editable = true -- Mark as editable
                }
            elseif buttonType == "DrawCircleFilled" or buttonType == "DrawCircle" then
                newLayer.properties = {
                    position = { x = x, y = y },
                    radius = 25,
                    color = { r = 100, g = 255, b = 100, a = 200 },
                    rotation = 0,
                    thickness = (buttonType == "DrawCircle") and 2 or nil -- Only for unfilled circle
                }
            elseif buttonType == "DrawLine" then
                newLayer.properties = {
                    start = { x = x, y = y },
                    endPos = { x = x + 100, y = y + 50 },
                    color = { r = 255, g = 255, b = 100, a = 255 },
                    thickness = 2,
                    rotation = 0
                }
            end
            
            table.insert(layers, newLayer)
            selectedLayerIndex = #layers
        end
    end

    yOffset = yOffset + math.ceil(#buttonTypes / 2) * 30 + 20
    
    Renderer.DrawText("Arial", "Layers: " .. #layers, Vector2D(guiX + 10, guiY + yOffset), false, true, Color(200, 200, 200, 255))
    yOffset = yOffset + 20
    
    if selectedLayerIndex and layers[selectedLayerIndex] then
        local selectedText = "Selected: Layer " .. selectedLayerIndex .. " (" .. layers[selectedLayerIndex].type .. ")"
        Renderer.DrawText("Arial", selectedText, Vector2D(guiX + 10, guiY + yOffset), false, true, Color(200, 255, 200, 255))
    end
    yOffset = yOffset + 25
    local controlButtons = {
        {text = "Move Up", w = 80, action = function()
            if selectedLayerIndex and selectedLayerIndex > 1 then
                local temp = layers[selectedLayerIndex]
                layers[selectedLayerIndex] = layers[selectedLayerIndex - 1]
                layers[selectedLayerIndex - 1] = temp
                selectedLayerIndex = selectedLayerIndex - 1
            end
        end},
        {text = "Move Down", w = 80, action = function()
            if selectedLayerIndex and selectedLayerIndex < #layers then
                local temp = layers[selectedLayerIndex]
                layers[selectedLayerIndex] = layers[selectedLayerIndex + 1]
                layers[selectedLayerIndex + 1] = temp
                selectedLayerIndex = selectedLayerIndex + 1
            end
        end},
        {text = "Delete", w = 80, action = function()
            if selectedLayerIndex and layers[selectedLayerIndex] and canClick then
                lastClickTime = currentTime
                table.remove(layers, selectedLayerIndex)
                selectedLayerIndex = math.min(selectedLayerIndex, #layers)
                if selectedLayerIndex == 0 then selectedLayerIndex = nil end
            end
        end},
        {text = "Export", w = 80, action = function()
            if canClick then
                lastClickTime = currentTime
                ExportGUI()
            end
        end},
        {text = "Load", w = 80, action = function()
            if canClick then
                lastClickTime = currentTime
                LoadGUI()
            end
        end}
    }
    
    for i, btn in ipairs(controlButtons) do
        local col = (i - 1) % 2
        local row = math.floor((i - 1) / 2)
        
        local btnX = guiX + 20 + col * 160
        local btnY = yOffset + guiY + row * 30
        local btnW, btnH = btn.w, 25
        
        local isHover = mousePos.x >= btnX and mousePos.x <= btnX + btnW and mousePos.y >= btnY and mousePos.y <= btnY + btnH
        
        local btnColor = isHover and Color(70, 70, 70, 255) or Color(50, 50, 50, 255)
        Renderer.DrawRectFilled(Vector2D(btnX, btnY), Vector2D(btnX + btnW, btnY + btnH), btnColor, 3)
        Renderer.DrawText("Arial", btn.text, Vector2D(btnX + 8, btnY + 5), false, true, Color(255, 255, 255, 255))
        
        if isHover and leftClick and canClick then
            lastClickTime = currentTime
            btn.action()
        end
    end

    yOffset = yOffset + math.ceil(#controlButtons / 2) * 30 + 20
    
    if selectedLayerIndex and layers[selectedLayerIndex] then
        local layer = layers[selectedLayerIndex]
        local props = layer.properties
        
        if props.color then
            Renderer.DrawText("Arial", "Color Controls:", Vector2D(guiX + 10, guiY + yOffset), false, true, Color(255, 255, 255, 255))
            yOffset = yOffset + 20
            
            local colorLabels = {"R", "G", "B", "A"}
            local colorValues = {props.color.r, props.color.g, props.color.b, props.color.a}
            
            for i, label in ipairs(colorLabels) do
                local sliderX, sliderY = guiX + 10, guiY + yOffset + (i - 1) * 15
                local sliderW, sliderH = 100, 10
                
                Renderer.DrawRectFilled(Vector2D(sliderX, sliderY), Vector2D(sliderX + sliderW, sliderY + sliderH), 
                                       Color(60, 60, 60, 255), 2)

                local valuePercent = colorValues[i] / 255
                local valueWidth = sliderW * valuePercent
                Renderer.DrawRectFilled(Vector2D(sliderX, sliderY), Vector2D(sliderX + valueWidth, sliderY + sliderH), 
                                       Color(100, 150, 100, 255), 2)
                
                Renderer.DrawText("Arial", label .. ": " .. colorValues[i], Vector2D(sliderX + sliderW + 10, sliderY - 2), false, true, Color(200, 200, 200, 255))

                local isHoverSlider = mousePos.x >= sliderX and mousePos.x <= sliderX + sliderW and 
                                     mousePos.y >= sliderY and mousePos.y <= sliderY + sliderH
                
                if isHoverSlider and leftClick then
                    local newValue = math.floor(((mousePos.x - sliderX) / sliderW) * 255)
                    newValue = math.max(0, math.min(255, newValue))
                    
                    if label == "R" then props.color.r = newValue
                    elseif label == "G" then props.color.g = newValue
                    elseif label == "B" then props.color.b = newValue
                    elseif label == "A" then props.color.a = newValue
                    end
                end
            end
            yOffset = yOffset + 65
        end
        if (layer.type == "DrawRect" or layer.type == "DrawLineRect" or layer.type == "DrawRectFilled") and props.rounding ~= nil then
            Renderer.DrawText("Arial", "Rounding:", Vector2D(guiX + 10, guiY + yOffset), false, true, Color(255, 255, 255, 255))
            
            local roundSliderX, roundSliderY = guiX + 70, guiY + yOffset
            local roundSliderW, roundSliderH = 80, 10
            
            Renderer.DrawRectFilled(Vector2D(roundSliderX, roundSliderY), 
                                   Vector2D(roundSliderX + roundSliderW, roundSliderY + roundSliderH), 
                                   Color(60, 60, 60, 255), 2)
            
            local roundPercent = props.rounding / 20
            local roundWidth = roundSliderW * roundPercent
            Renderer.DrawRectFilled(Vector2D(roundSliderX, roundSliderY), 
                                   Vector2D(roundSliderX + roundWidth, roundSliderY + roundSliderH), 
                                   Color(150, 100, 100, 255), 2)
            
            Renderer.DrawText("Arial", tostring(props.rounding), Vector2D(roundSliderX + roundSliderW + 10, roundSliderY - 2), false, true, Color(200, 200, 200, 255))
            
            local isHoverRound = mousePos.x >= roundSliderX and mousePos.x <= roundSliderX + roundSliderW and 
                                mousePos.y >= roundSliderY and mousePos.y <= roundSliderY + roundSliderH
              if isHoverRound and leftClick then
                local newRounding = math.floor(((mousePos.x - roundSliderX) / roundSliderW) * 20)
                props.rounding = math.max(0, math.min(20, newRounding))
            end
            
            yOffset = yOffset + 20
        end
        
        if props.rotation ~= nil then
            Renderer.DrawText("Arial", "Rotation:", Vector2D(guiX + 10, guiY + yOffset), false, true, Color(255, 255, 255, 255))
            
            local rotSliderX, rotSliderY = guiX + 70, guiY + yOffset
            local rotSliderW, rotSliderH = 80, 10
            
            Renderer.DrawRectFilled(Vector2D(rotSliderX, rotSliderY), 
                                   Vector2D(rotSliderX + rotSliderW, rotSliderY + rotSliderH), 
                                   Color(60, 60, 60, 255), 2)
            
            local rotPercent = (props.rotation + 180) / 360 -- Mappingz -180 to 180 to 0 to 1
            local rotWidth = rotSliderW * rotPercent
            Renderer.DrawRectFilled(Vector2D(rotSliderX, rotSliderY), 
                                   Vector2D(rotSliderX + rotWidth, rotSliderY + rotSliderH), 
                                   Color(100, 100, 150, 255), 2)
            
            Renderer.DrawText("Arial", string.format("%.1f°", props.rotation), Vector2D(rotSliderX + rotSliderW + 10, rotSliderY - 2), false, true, Color(200, 200, 200, 255))
            
            local isHoverRot = mousePos.x >= rotSliderX and mousePos.x <= rotSliderX + rotSliderW and 
                              mousePos.y >= rotSliderY and mousePos.y <= rotSliderY + rotSliderH
              if isHoverRot and leftClick then
                local newRotation = (((mousePos.x - rotSliderX) / rotSliderW) * 360) - 180
                props.rotation = math.max(-180, math.min(180, newRotation))
            end
            
            yOffset = yOffset + 20
        end
        if (layer.type == "DrawLine" or layer.type == "DrawLineRect" or layer.type == "DrawCircle") and props.thickness ~= nil then
            Renderer.DrawText("Arial", "Thickness:", Vector2D(guiX + 10, guiY + yOffset), false, true, Color(255, 255, 255, 255))
            
            local thickSliderX, thickSliderY = guiX + 70, guiY + yOffset
            local thickSliderW, thickSliderH = 80, 10
            
            Renderer.DrawRectFilled(Vector2D(thickSliderX, thickSliderY), 
                                   Vector2D(thickSliderX + thickSliderW, thickSliderY + thickSliderH), 
                                   Color(60, 60, 60, 255), 2)
            
            local thickPercent = (props.thickness - 1) / 99
            local thickWidth = thickSliderW * thickPercent
            Renderer.DrawRectFilled(Vector2D(thickSliderX, thickSliderY), 
                                   Vector2D(thickSliderX + thickWidth, thickSliderY + thickSliderH), 
                                   Color(150, 150, 100, 255), 2)
            
            Renderer.DrawText("Arial", tostring(props.thickness), Vector2D(thickSliderX + thickSliderW + 10, thickSliderY - 2), 
                             false, true, Color(200, 200, 200, 255))
            
            local isHoverThick = mousePos.x >= thickSliderX and mousePos.x <= thickSliderX + thickSliderW and 
                                mousePos.y >= thickSliderY and mousePos.y <= thickSliderY + thickSliderH
            
            if isHoverThick and leftClick then
                local newThickness = math.floor(((mousePos.x - thickSliderX) / thickSliderW) * 99) + 1
                props.thickness = math.max(1, math.min(100, newThickness))
            end
            
            yOffset = yOffset + 20
        end
        
        if layer.type == "DrawText" and props.fontSize ~= nil then
            Renderer.DrawText("Arial", "Font Size:", Vector2D(guiX + 10, guiY + yOffset), false, true, Color(255, 255, 255, 255))
            
            local fontSliderX, fontSliderY = guiX + 70, guiY + yOffset
            local fontSliderW, fontSliderH = 80, 10
            
            Renderer.DrawRectFilled(Vector2D(fontSliderX, fontSliderY), 
                                   Vector2D(fontSliderX + fontSliderW, fontSliderY + fontSliderH), 
                                   Color(60, 60, 60, 255), 2)
              local fontPercent = (props.fontSize - 8) / 23 -- Mappingz 8-31 to 0-1
            local fontWidth = fontSliderW * fontPercent
            Renderer.DrawRectFilled(Vector2D(fontSliderX, fontSliderY), 
                                   Vector2D(fontSliderX + fontWidth, fontSliderY + fontSliderH), 
                                   Color(150, 100, 150, 255), 2)
            
            Renderer.DrawText("Arial", tostring(props.fontSize), Vector2D(fontSliderX + fontSliderW + 10, fontSliderY - 2), false, true, Color(200, 200, 200, 255))
            
            local isHoverFont = mousePos.x >= fontSliderX and mousePos.x <= fontSliderX + fontSliderW and mousePos.y >= fontSliderY and mousePos.y <= fontSliderY + fontSliderH
            if isHoverFont and leftClick then
                local newFontSize = math.floor(((mousePos.x - fontSliderX) / fontSliderW) * 23) + 8
                props.fontSize = math.max(8, math.min(31, newFontSize))
                
                local fontName, actualSize = GetFontForSize(props.fontSize)
                props.font = fontName
                props.fontSize = actualSize
            end
            
            yOffset = yOffset + 25
            
            local editBtnX, editBtnY = guiX + 10, guiY + yOffset
            local editBtnW, editBtnH = 100, 25
            
            local isHoverEdit = mousePos.x >= editBtnX and mousePos.x <= editBtnX + editBtnW and 
                               mousePos.y >= editBtnY and mousePos.y <= editBtnY + editBtnH
            
            local editBtnColor = isHoverEdit and Color(80, 80, 80, 255) or Color(60, 60, 60, 255)
            if textEditMode and editingTextElement == layer then
                editBtnColor = Color(100, 150, 255, 255)
            end
            
            Renderer.DrawRectFilled(Vector2D(editBtnX, editBtnY), Vector2D(editBtnX + editBtnW, editBtnY + editBtnH), editBtnColor, 3)
            
            local buttonText = (textEditMode and editingTextElement == layer) and "Stop Editing" or "Edit Text"
            Renderer.DrawText("Arial", buttonText, Vector2D(editBtnX + editBtnW/2, editBtnY + 6), true, true, Color(255, 255, 255, 255))
            
            if isHoverEdit and leftClick and canClick then
                lastClickTime = currentTime
                if textEditMode and editingTextElement == layer then
                    textEditMode = false
                    editingTextElement = nil
                    textCursor = 0
                else
                    textEditMode = true
                    editingTextElement = layer
                    textCursor = #props.text
                end
            end

            if textEditMode and editingTextElement == layer then
                yOffset = yOffset + 30
                Renderer.DrawText("Arial", "Editing: \"" .. props.text .. "\"", Vector2D(guiX + 10, guiY + yOffset), 
                                 false, true, Color(150, 255, 150, 255))
                yOffset = yOffset + 15
                Renderer.DrawText("Arial", "Press Enter to finish, Esc to cancel", Vector2D(guiX + 10, guiY + yOffset), 
                                 false, true, Color(200, 200, 200, 255))
            end
            
            yOffset = yOffset + 20
        end
    end
    
    guiHeight = yOffset + 50
    
    local cursorPos = Input.GetCursorPos()
    local mousePressed = Input.GetKeyDown(0x01)
    local shiftDown = Input.GetKeyDown(0x10)
    
    local outsideGUI = not (cursorPos.x >= guiX and cursorPos.x <= guiX + guiWidth and cursorPos.y >= guiY and cursorPos.y <= guiY + guiHeight)
    
    if mousePressed and outsideGUI and canClick then
        if not draggingElementIndex and not resizingElementIndex then
            for i = #layers, 1, -1 do
                local layer = layers[i]
                local props = layer.properties
                local hit = false
                
                if (layer.type == "DrawRect" or layer.type == "DrawLineRect" or layer.type == "DrawRectFilled") and props.position and props.size then
                    hit = IsPointInRect(cursorPos.x, cursorPos.y, 
                        props.position.x, props.position.y, 
                        props.size.width, props.size.height)
                elseif (layer.type == "DrawCircleFilled" or layer.type == "DrawCircle") and props.position and props.radius then
                    hit = IsPointInCircle(cursorPos.x, cursorPos.y, 
                        props.position.x, props.position.y, props.radius)
                elseif layer.type == "DrawText" and props.position and props.text then
                    local textLen = string.len(props.text)
                    local estimatedWidth = textLen * 8
                    local estimatedHeight = 16
                    hit = IsPointInRect(cursorPos.x, cursorPos.y,
                        props.position.x, props.position.y, estimatedWidth, estimatedHeight)
                      -- double-click for text editing
                    if hit then
                        local currentTime = Globals.GetCurrentTime()
                        if layer.lastClickTime and (currentTime - layer.lastClickTime) < 0.5 then
                            textEditMode = true
                            editingTextElement = layer
                            textCursor = #props.text
                            layer.lastClickTime = nil
                            break
                        else
                            layer.lastClickTime = currentTime
                        end
                    end
                elseif layer.type == "DrawLine" and props.start and props.endPos then
                    local startX, startY, endX, endY = GetRotatedLinePoints(props)
                    local endpointTolerance = 10
                    
                    if IsPointNearEndpoint(cursorPos.x, cursorPos.y, startX, startY, endpointTolerance) then
                        hit = true
                        draggingLineEndpoint = "start"
                    elseif IsPointNearEndpoint(cursorPos.x, cursorPos.y, endX, endY, endpointTolerance) then
                        hit = true
                        draggingLineEndpoint = "end"
                    else
                        local lineThickness = (props.thickness or 2) + 5
                        local dx = endX - startX
                        local dy = endY - startY
                        local lineLength = math.sqrt(dx * dx + dy * dy)
                        
                        if lineLength > 0 then
                            -- dist from point to line
                            local t = math.max(0, math.min(1, ((cursorPos.x - startX) * dx + (cursorPos.y - startY) * dy) / (lineLength * lineLength)))
                            local projX = startX + t * dx
                            local projY = startY + t * dy
                            local distSq = (cursorPos.x - projX) * (cursorPos.x - projX) + (cursorPos.y - projY) * (cursorPos.y - projY)
                            hit = distSq <= (lineThickness * lineThickness)
                            draggingLineEndpoint = nil
                        end
                    end
                end
                if hit then
                    if selectedLayerIndex ~= i and textEditMode then
                        textEditMode = false
                        editingTextElement = nil
                        textCursor = 0
                    end
                    
                    selectedLayerIndex = i
                    if shiftDown and (layer.type == "DrawRect" or layer.type == "DrawLineRect" or layer.type == "DrawRectFilled" or 
                                     layer.type == "DrawCircleFilled" or layer.type == "DrawCircle" or 
                                     layer.type == "DrawText") then
                        resizingElementIndex = i
                    else
                        draggingElementIndex = i
                        if layer.type == "DrawLine" then
                            dragOffsetX = cursorPos.x - props.start.x
                            dragOffsetY = cursorPos.y - props.start.y
                        else
                            dragOffsetX = cursorPos.x - (props.position and props.position.x or 0)
                            dragOffsetY = cursorPos.y - (props.position and props.position.y or 0)
                        end
                    end
                    break
                end
            end
        end    elseif not mousePressed then
        draggingElementIndex = nil
        resizingElementIndex = nil
        draggingLineEndpoint = nil
    end
    if draggingElementIndex and layers[draggingElementIndex] then
        local layer = layers[draggingElementIndex]
        local props = layer.properties
          if layer.type == "DrawLine" then
            if draggingLineEndpoint == "start" then
                props.start.x = cursorPos.x
                props.start.y = cursorPos.y
            elseif draggingLineEndpoint == "end" then
                props.endPos.x = cursorPos.x
                props.endPos.y = cursorPos.y
            else
                local deltaX = cursorPos.x - dragOffsetX - props.start.x
                local deltaY = cursorPos.y - dragOffsetY - props.start.y
                props.start.x = props.start.x + deltaX
                props.start.y = props.start.y + deltaY
                props.endPos.x = props.endPos.x + deltaX
                props.endPos.y = props.endPos.y + deltaY
            end
        elseif props.position then
            props.position.x = cursorPos.x - dragOffsetX
            props.position.y = cursorPos.y - dragOffsetY
        end
    end
    if resizingElementIndex and layers[resizingElementIndex] then
        local layer = layers[resizingElementIndex]
        local props = layer.properties
        
        if (layer.type == "DrawRect" or layer.type == "DrawLineRect" or layer.type == "DrawRectFilled") then
            if props.position and props.size then
                local newWidth = cursorPos.x - props.position.x
                local newHeight = cursorPos.y - props.position.y
                props.size.width = math.max(2, newWidth)
                props.size.height = math.max(2, newHeight)
            end
        elseif (layer.type == "DrawCircleFilled" or layer.type == "DrawCircle") then
            if props.position then
                local dx = cursorPos.x - props.position.x
                local dy = cursorPos.y - props.position.y
                local newRadius = math.sqrt(dx * dx + dy * dy)
                props.radius = math.max(5, math.floor(newRadius))
            end
        elseif layer.type == "DrawText" then
            if props.position and props.fontSize then
                local startY = props.position.y
                local dragDistance = cursorPos.y - startY
                local newFontSize = math.max(8, math.min(31, props.fontSize + math.floor(dragDistance / 5)))
                props.fontSize = newFontSize
                local fontName, actualSize = GetFontForSize(newFontSize)
                props.font = fontName
                props.fontSize = actualSize
            end
        end
    end
    for i, layer in ipairs(layers) do
        local props = layer.properties
        
        local color = Color(
            (props.color and props.color.r) or 255,
            (props.color and props.color.g) or 255,
            (props.color and props.color.b) or 255,
            (props.color and props.color.a) or 255
        )
        if i == selectedLayerIndex then
            if (layer.type == "DrawRect" or layer.type == "DrawLineRect" or layer.type == "DrawRectFilled") and props.position and props.size then
                local pos = Vector2D(props.position.x, props.position.y)
                local outline = Vector2D(pos.x + props.size.width, pos.y + props.size.height)
                Renderer.DrawRect(pos, outline, Color(255, 255, 0, 200), 0)
                if props.rotation and props.rotation ~= 0 then
                    local centerX = props.position.x + props.size.width / 2
                    local centerY = props.position.y + props.size.height / 2
                    local rad = math.rad(props.rotation)
                    local lineLen = 30
                    local endX = centerX + math.cos(rad) * lineLen
                    local endY = centerY + math.sin(rad) * lineLen
                    Renderer.DrawLine(Vector2D(centerX, centerY), Vector2D(endX, endY), Color(255, 100, 100, 255), 2)
                end
            elseif (layer.type == "DrawCircleFilled" or layer.type == "DrawCircle") and props.position and props.radius then
                local pos = Vector2D(props.position.x, props.position.y)
                Renderer.DrawCircleFilled(pos, Color(255, 255, 0, 100), props.radius + 3)
                if props.rotation and props.rotation ~= 0 then
                    local rad = math.rad(props.rotation)
                    local lineLen = props.radius + 15
                    local endX = props.position.x + math.cos(rad) * lineLen
                    local endY = props.position.y + math.sin(rad) * lineLen
                    Renderer.DrawLine(Vector2D(props.position.x, props.position.y), Vector2D(endX, endY), Color(255, 100, 100, 255), 2)
                end
            elseif layer.type == "DrawText" and props.position then
                local pos = Vector2D(props.position.x, props.position.y)
                local textLen = string.len(props.text or "")
                Renderer.DrawRect(pos, Vector2D(pos.x + textLen * 8, pos.y + 16), Color(255, 255, 0, 150), 0)
                if props.rotation and props.rotation ~= 0 then
                    local rad = math.rad(props.rotation)
                    local lineLen = 25
                    local endX = props.position.x + math.cos(rad) * lineLen
                    local endY = props.position.y + math.sin(rad) * lineLen
                    Renderer.DrawLine(Vector2D(props.position.x, props.position.y), Vector2D(endX, endY), Color(255, 100, 100, 255), 2)
                end
            elseif layer.type == "DrawLine" and props.start and props.endPos then
                local startX, startY, endX, endY = GetRotatedLinePoints(props)
                Renderer.DrawLine(Vector2D(startX, startY), Vector2D(endX, endY), Color(255, 255, 0, 200), (props.thickness or 2) + 2)
                
                Renderer.DrawCircleFilled(Vector2D(startX, startY), Color(0, 255, 0, 200), 8)
                Renderer.DrawCircleFilled(Vector2D(endX, endY), Color(255, 0, 0, 200), 8)
                
                if props.rotation and props.rotation ~= 0 then
                    local centerX = (props.start.x + props.endPos.x) / 2
                    local centerY = (props.start.y + props.endPos.y) / 2
                    local rad = math.rad(props.rotation)
                    local lineLen = 20
                    local endX = centerX + math.cos(rad) * lineLen
                    local endY = centerY + math.sin(rad) * lineLen
                    Renderer.DrawLine(Vector2D(centerX, centerY), Vector2D(endX, endY), Color(255, 100, 100, 255), 2)
                end
            end
        end
        if layer.type == "DrawRect" and props.position and props.size then
            local pos = Vector2D(props.position.x, props.position.y)
            local endPos = Vector2D(pos.x + props.size.width, pos.y + props.size.height)
            Renderer.DrawRect(pos, endPos, color, props.rounding or 0)
        elseif layer.type == "DrawLineRect" and props.position and props.size then
            if props.rotation and props.rotation ~= 0 then
                local centerX = props.position.x + props.size.width / 2
                local centerY = props.position.y + props.size.height / 2
                local halfW = props.size.width / 2
                local halfH = props.size.height / 2
                local corners = {
                    {-halfW, -halfH}, {halfW, -halfH}, {halfW, halfH}, {-halfW, halfH}
                }
                local rotatedCorners = {}
                for i, corner in ipairs(corners) do
                    local rotX, rotY = RotatePoint(centerX + corner[1], centerY + corner[2], centerX, centerY, props.rotation)
                    rotatedCorners[i] = {rotX, rotY}
                end
                for i = 1, 4 do
                    local nextI = (i % 4) + 1
                    Renderer.DrawLine(Vector2D(rotatedCorners[i][1], rotatedCorners[i][2]), 
                                     Vector2D(rotatedCorners[nextI][1], rotatedCorners[nextI][2]), 
                                     color, props.thickness or 2)
                end
            else
                DrawRectOutline(props.position.x, props.position.y, props.size.width, props.size.height, 
                               color, props.thickness or 2)
            end
        elseif layer.type == "DrawRectFilled" and props.position and props.size then
            if props.rotation and props.rotation ~= 0 then
                local centerX = props.position.x + props.size.width / 2
                local centerY = props.position.y + props.size.height / 2
                local halfW = props.size.width / 2
                local halfH = props.size.height / 2
                
                local corners = {
                    {-halfW, -halfH}, {halfW, -halfH}, {halfW, halfH}, {-halfW, halfH}
                }
                
                local rotatedCorners = {}
                for i, corner in ipairs(corners) do
                    local rotX, rotY = RotatePoint(centerX + corner[1], centerY + corner[2], centerX, centerY, props.rotation)
                    rotatedCorners[i] = {rotX, rotY}
                end
                
                for i = 1, 4 do
                    local nextI = (i % 4) + 1
                    Renderer.DrawLine(Vector2D(rotatedCorners[i][1], rotatedCorners[i][2]), Vector2D(rotatedCorners[nextI][1], rotatedCorners[nextI][2]), color, 3)
                end
            else
                local pos = Vector2D(props.position.x, props.position.y)
                local endPos = Vector2D(pos.x + props.size.width, pos.y + props.size.height)
                Renderer.DrawRectFilled(pos, endPos, color, props.rounding or 0)
            end
        elseif layer.type == "DrawText" and props.position and props.text then
            local pos = Vector2D(props.position.x, props.position.y)
            local textColor = color
            
            if textEditMode and editingTextElement == layer then
                local textLen = string.len(props.text)
                local estimatedWidth = textLen * 8 + 20
                local estimatedHeight = 20
                Renderer.DrawRectFilled(Vector2D(props.position.x - 2, props.position.y - 2), 
                                       Vector2D(props.position.x + estimatedWidth, props.position.y + estimatedHeight), 
                                       Color(0, 100, 255, 80), 3)
                
                local blinkTime = Globals.GetCurrentTime() * 2
                if math.floor(blinkTime) % 2 == 0 then
                    local cursorX = props.position.x + textCursor * 8
                    Renderer.DrawLine(Vector2D(cursorX, props.position.y), 
                                     Vector2D(cursorX, props.position.y + 16), 
                                     Color(255, 255, 255, 255), 2)
                end
                
                textColor = Color(255, 255, 255, 255)
            elseif editingTextElement == layer then
                local textLen = string.len(props.text)
                local estimatedWidth = textLen * 8 + 10
                local estimatedHeight = 20
                Renderer.DrawRectFilled(Vector2D(props.position.x - 2, props.position.y - 2), 
                                       Vector2D(props.position.x + estimatedWidth, props.position.y + estimatedHeight), 
                                       Color(0, 100, 255, 80), 3)
                
                local cursorX = props.position.x + textCursor * 8
                Renderer.DrawLine(Vector2D(cursorX, props.position.y), 
                                 Vector2D(cursorX, props.position.y + 16), 
                                 Color(255, 255, 255, 255), 2)
                
                textColor = Color(255, 255, 255, 255)
            end
            
            Renderer.DrawText(props.font or defaultFontName, props.text, 
                pos, props.centered or false, props.outlined or true, textColor)
        elseif layer.type == "DrawCircleFilled" and props.position and props.radius then
            local pos = Vector2D(props.position.x, props.position.y)
            Renderer.DrawCircleFilled(pos, color, props.radius)
        elseif layer.type == "DrawCircle" and props.position and props.radius then
            DrawCircleOutline(props.position.x, props.position.y, props.radius, color, 32, props.thickness or 2)
        elseif layer.type == "DrawLine" and props.start and props.endPos then

            local startX, startY, endX, endY = GetRotatedLinePoints(props)
            Renderer.DrawLine(Vector2D(startX, startY), Vector2D(endX, endY), color, props.thickness or 2)
        end
    end
    Renderer.DrawText("Arial", "[ACTIVE] GUI Builder V1 by fate", Vector2D(10, 10), false, true, Color(200, 200, 200, 255))
end

-- Register the callback
Cheat.RegisterCallback("OnRenderer", RenderGUIBuilder)

return {}