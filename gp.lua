package.path = "C:\\plaguecheat.cc\\lib\\?.lua;" .. package.path

local UI = require("gpui") 
local cfg = require("configMenager") 
local ffi = require("ffi")

local function lerp(a, b, t)
    return a + (b - a) * t
end


Renderer.LoadFontFromFile('font', 'verdana', 14, true)
Renderer.LoadFontFromFile('font_large', 'verdana', 20, true)
Renderer.LoadFontFromFile('load_screen', 'verdanab', 95, true)



local widthmap_loadscreen = {
   [' '] = 32,
  ['!'] = 38,
  ['"'] = 56,
  ['#'] = 82,
  ['$'] = 68,
  ['%'] = 121,
  ['&'] = 82,
  ['\''] = 32,
  ['('] = 52,
  [')'] = 52,
  ['*'] = 68,
  ['+'] = 82,
  [','] = 34,
  ['-'] = 46,
  ['.'] = 34,
  ['/'] = 66,
  ['0'] = 68,
  ['1'] = 68,
  ['2'] = 68,
  ['3'] = 68,
  ['4'] = 68,
  ['5'] = 68,
  ['6'] = 68,
  ['7'] = 68,
  ['8'] = 68,
  ['9'] = 68,
  [':'] = 38,
  [';'] = 38,
  ['<'] = 82,
  ['='] = 82,
  ['>'] = 82,
  ['?'] = 59,
  ['@'] = 92,
  ['A'] = 74,
  ['B'] = 72,
  ['C'] = 69,
  ['D'] = 79,
  ['E'] = 65,
  ['F'] = 62,
  ['G'] = 77,
  ['H'] = 80,
  ['I'] = 52,
  ['J'] = 53,
  ['K'] = 73,
  ['L'] = 61,
  ['M'] = 90,
  ['N'] = 80,
  ['O'] = 81,
  ['P'] = 70,
  ['Q'] = 81,
  ['R'] = 74,
  ['S'] = 68,
  ['T'] = 65,
  ['U'] = 77,
  ['V'] = 73,
  ['W'] = 107,
  ['X'] = 73,
  ['Y'] = 70,
  ['Z'] = 66,
  ['['] = 52,
  ['\\'] = 66,
  [']'] = 52,
  ['^'] = 82,
  ['_'] = 68,
  ['`'] = 68,
  ['a'] = 63,
  ['b'] = 66,
  ['c'] = 56,
  ['d'] = 66,
  ['e'] = 63,
  ['f'] = 40,
  ['g'] = 66,
  ['h'] = 68,
  ['i'] = 32,
  ['j'] = 38,
  ['k'] = 64,
  ['l'] = 32,
  ['m'] = 101,
  ['n'] = 68,
  ['o'] = 65,
  ['p'] = 66,
  ['q'] = 66,
  ['r'] = 47,
  ['s'] = 56,
  ['t'] = 43,
  ['u'] = 68,
  ['v'] = 62,
  ['w'] = 93,
  ['x'] = 64,
  ['y'] = 62,
  ['z'] = 57,
  ['{'] = 68,
  ['|'] = 52,
  ['}'] = 68,
  ['~'] = 82,
}

local load_time 
local status_text
local function load_screen()
    if not load_time then 
        load_time = Globals.GetCurrentTime()
    end

    if load_time == -1 then return end

    local elapsed = Globals.GetCurrentTime() - load_time
    
    if elapsed > 5 then
        load_time = -1
        return
    end

    
    local fade_progress = 1
    if elapsed > 4 then
        fade_progress = math.max(0, 1 - (elapsed - 4))
    end
    
    local center = Renderer.GetScreenSize()
    center.x = center.x / 2
    center.y = center.y / 2 - 50
    
    
    Renderer.DrawRectFilled(Vector2D(0,0), Renderer.GetScreenSize(), Color(0, 0, 0, 100 * fade_progress), 0)
    
    
    local textsize_1 = UI.GetTextSize("g.p", widthmap_loadscreen)
    local textsize_2 = UI.GetTextSize("1.7", widthmap_loadscreen)
    

    local text_pos = Vector2D(center.x - textsize_1/2, center.y - 95/2)
    Renderer.DrawText('load_screen', "gp", text_pos, true, false, Color(150, 150, 255, 255 * fade_progress))
    local text_pos = Vector2D(center.x + textsize_2/2, center.y - 95/2)
    Renderer.DrawText('load_screen', "1.7", text_pos, true, false, Color(200, 200, 200, 255 * fade_progress))

    
    Renderer.DrawLine(Vector2D(center.x, center.y  + 50), Vector2D(center.x, center.y - 40), Color(150, 150, 255, 255 * fade_progress), 5)

    

    
    local circle_center = Vector2D(center.x, center.y + 150)
    local circle_radius = 20
    local spin_speed = 0.95  
    local rotation = (elapsed * spin_speed * 360) % 360  
    
    
    local segments = 12
    local segment_angle = 360 / segments
    
    for i = 0, segments - 1 do
        local angle = math.rad(rotation + (i * segment_angle))
        local alpha = (i / segments) * 255  
        
        local start_x = circle_center.x + math.cos(angle) * (circle_radius - 5)
        local start_y = circle_center.y + math.sin(angle) * (circle_radius - 5)
        local end_x = circle_center.x + math.cos(angle) * circle_radius
        local end_y = circle_center.y + math.sin(angle) * circle_radius
        
        Renderer.DrawLine(
            Vector2D(start_x, start_y), 
            Vector2D(end_x, end_y), 
            Color(150, 150, 255, alpha * fade_progress), 
            3
        )
    end
end


local mainWindow = UI.CreateWindow("GP.lua v1.7", Vector2D(100, 100), Vector2D(665, 445), true)



local j_tick, yj_side, spin_ang, sw_dir, last_sw, way_idx, mv_tick, mv_dir = 0, 0, 0, 1, 0, 0, 0, 1
local yaw_mode = 0
local base_yaw = 0 
local current_yaw_change = 0 
local current_yaw = 0 
local local_player
local dir_angle = 0
local saved_pos = Vector(0,0,0)

local msgs = {
    {"nice iq", "u sell"}, {"1", "nice iq"}, {"mad cuz bad?", "hhhhhh retardw"}, {"iq?"}, {"ez 1"}, {"xD"}, {"L"},
    {"nice iq bot", "１"}, {"l2p", "𝟷"}, {"𝟭"}, {"𝟏", "botik"}, {"１", "dog"}, {"retard", "just bot"},
    {"✴.·´¯`·.·★  🎀1🎀  ★·.·`¯´·.✴"}, {"(☞ ͡° ͜ʖ ͡°)☞ 1 ♥♥"}, {"kd rate is good HHHHH"},
    {"why are you so ez?"}, {"ez"}, {"gg wp ez mid", "16-0 owned"}
}

local sounds = {
    {name="Thunder", path="\\sounds\\ambient\\playonce\\weather\\thunder4.vsnd_c"},
    {name="Metal Hit", path="\\sounds\\physics\\metal\\chain_impact_hard1.vsnd_c"},
    {name="Shooting metal", path="\\sounds\\physics\\metal\\bullet_metal_solid_06.vsnd_c"},
    {name="Water Spalsh", path="\\sounds\\physics\\water\\water_splash_03.vsnd_c"},
    {name="Droping Nade", path="\\sounds\\physics\\metal\\metal_grenade_impact_hard2.vsnd_c"},
}

local base64_legit_bot_icon = "iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAXhJREFUSEvtVslVQzEMlDpJKiGpBFIJUAnpBFIJoZLhD8/KE7Ykmwun6JLFy2hGm1X+yXQFB8BORB7b3oOI8PeHiFxE5Kqq/F7aFAjAk4i8Te55UdXXak8JBOB985wMaNfGgp9fIvLQmPn1U8YuBQJAFmRDSz0GQCDupZx04qiq/PxlIVA7TDY0HrzFoMXroKpnu6n9x/0EO6vqaRXo0wKuqkd/CICt9Q6QWegcz2eM0C7f9zJkQNzvYjpIPQD5LNsyKVoPGTWgZ8aTSdMrEV1km0OtJ4xMPtbW3kseAVm2ZUB0hNk4ZJdPol6NPwNVRXkHKmN0l84UqOpoKLrJqGBnsJGyVLBp0S0ApfGNGLEDs82U4yECBWA9knPp1t2rpmptKJ0vPZBrqEP7qYDIijJQxinYxsQcG+ZXmgzdMDMJCUYpLjYEbQBu04EgdIwW9seUUQdmzKpcoCPpe2EK5ADpNWW0h4gt/TCdvYCWgTyVJtlu5S3nz30D5OH+Gwa0VqcAAAAASUVORK5CYII="

local base64_antiaim_icon = "iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAACJElEQVR4AczVjVXbQBAEYCWVQCWBSgKVYFcCnUAqgU6U+c5aIcnn2JC8PPQ0vvP+ze7e6e778J+er0c0juNd8BA8Bs+B+U3GKzjXmFZRDDkIcLV1iE7QMfLHYBfcBTeB+XPG14ANWab9txFFJYgAM1EIkCNYBniKLSB5yfwt8PJDpkpzshWKaCUMiUyBXLDbb4fnPgPsM5JdxwCQZzpIAOER2RFRSFRXVTwl4HUg+6H3RPcW3EcHkkLyM/9X75aIgRYy2k0BzM8itqoqsl0Sflg6bYmKRCX7peEl85CpHCFzZKozH7ZEhIz3yahtWyPhpQiZBLWQSy1Bl4jSlp0RslUbRDgDyTL54QeqolKQ9YC8Jz8l+zUpZr9GlHItom3aRfS3k+OlQ7WutZ9TIzJJMNu0C/oPoogGcfnORP78Q9Rumwlnoiy4MjOMH134Xn7zJijlTBRBZbELW80j/qu3vqn37Z1e2nlVqhPiUwxJ0k5z5vGv3fdORBr42DIMn6oqJDpRh/HLlLx4a6IolFpVdU/h5nX6x4FMa/euPonlGjGwHX1LyFp2yVIrmu7UD5vASVK2vsuV+RHRpJXNkkx1LkKQwJDArnZQhXaR83FPWe8p1GHoEqWFSleZReUsU8HgNSRuXgRQJ741OXl3dYkOOQza6CYtQlkiHRaPNQVV6MJCtZ7+kahMUyFCwZA2ROZ1rYMkyrw7XkS09Ex0bd1WtjTpzn8DAAD//8PMBj8AAAAGSURBVAMAoa31Na1tCuYAAAAASUVORK5CYII="

local base64_misc_icon = "iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAABlElEQVR4AeSVi1ECMRCGwUqgE6hErASpBDoBO6GT8/syt8CRzZ3KjDqDs/9tsq9/8zC8zH7p7++Iuq5bgD1YZIvFvgFH0PKbu7nPzVb0SpCBW3Qm2lc4KqKe3Nw9/oFkRFWBQcb4pJmbEY2X+qH3/xGx/yvBgmJ7vDTFhj1suHOZXJFFQEf6sQeqiAceNi9QMbY+k0Tz+fxM8gmoW/jAPyqTRGZDtgbLEdiIoU1kRNH1ZJfNqomjIqLrHXGu4ICuhPMafRmqhN5QEWmHzFU5zNB8GQg2L8D0KoWILr2mvl8t+KxcsxqjvsE33GswkEKExavq+9XC5PWlRhHITsBVlXl8gsgu3jGKuEEGOxeeG+5c2BHPbYvOUHajENGBXezQFpTAihcb9iDXPgDFLeSO2FAGfzYWhWiQ+f2JTdwiKoTtQKPnh4ksAvx38AK4I4UobGiPZfYwUan6hc9zEMWtC32/MWEPffFzHl4A7eJid1BtHcEeqC+12pgB8Hvo+qtiBuLXt3R8i4pIJ8FpEX1iym/MPT4BAAD//z8PCN0AAAAGSURBVAMAc83ENa+6t2YAAAAASUVORK5CYII="

local base64_weapon_model_chngr = "iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAACrElEQVR4AbzW4VXcQAwEYDuNBCoJVBKo5KASSCWQSqCTy3zCWnycD+5HHjyPd1fSaiStvMeP6Zv+ziLa7/cXwW7BU8aX4CG4Ca7OifVTIk6CfRy9BHcLOL7I/CZ4CBAz22V+8tkkyq7KILuegn5eM3kMEN5mNH/O2M9d9slUIC0b4xFRjBki4JDh4/z2d5nhNrgPyMyvM59j1LYyVdKj7I6Iskk5bJABR6KfEkCdR0a6mL0/IbvP6jJASC87pY3o7TkgihOZMJyyWQZVmsidkQDojW+7845OmW9i/xogRBbNpHnKl8UgygYlA/JrL4icMVjC1SKbMrKvIDLv8/kTI9WwZwQ1iKJsofpXJpHJzCawBJH3+jfBAo53yYquyh25oKqERZRoRMaQ4zaK3XiUBLlOW+tFP4wyQcIHW4ho+ulVRJkUScZWZvr+JEpZVmNkvrbhGDlj8jWxNbkkpib6RRLYmGFS/10yVXcfZAdSun6FVBm1eaazQNo5k79eQe1tomKNsJQhUFfdw4iuzy8mZz8dtK68aqIW9vjR2yn5R7uxnud5vee1idqgS7guAV1lanIulqoM8yZqx8qka0TjS9dhPtw+8LFxPYlT5XGm4Aqqb2uxuZNdE3XEzqT0lIFuQ1qyrVdIBMexMwXn236e48OnMbqunYmM4ZbPUzLXUutkDghVYtwwlVFYEVHaIP2OyPokks06KI61Orjh+Rx7i2hZ+dicFZJz27mb58sSD6IlK4eP1x21M/lfGEQcLmRdQr8pbgaHTb2F1h2UacvwgIhByHSJzGxWRleQlq0fPjaQ80FCb9lda76JIyJWIdM5Oqazc+jODWk49v5h6W7Tws7W1pPYJGIdMhdmZ4d4y5mPUUC2fIqTRL0rhDpKy7qd58jdGDWPTiARff38AwAA///rl71/AAAABklEQVQDAF1yOZ7BQzPWAAAAAElFTkSuQmCC"

local base64_config_icon = "iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAgZJREFUSEu9VtF1AjEMsydpmaRlkpZJCpMUJimdpHQSF+VZeY4vyR0/zQ+PO2zZluSg8k9HH8Exs2cReRORdxE5qep5a/xmIDN7FZGvlPisqoctYEMgVK+qNyYxs0/v5CoiFxHB95uq7vAb7xYxeL84CyAPQOUYE87RE/PZDgWYGb6jS7zn7/CJ4tDpKaL1gJigO5F7ghJjZuAJXY3OPnbXACUeMBJ09eGVo9IDg5MwAFa6iJ3GrjIQEv94iaWizNUa8QEIRVVVzkZ3VdV9TjwjPY2zcMn4HlCVMfkIqooiWZAelHkciiHMnCqqHQ08xGKrl0JH7KSamgrKZszEU4nwCGSL94ih6iqfLh4osimEQNGMqKIxnZlZFAgzjBTm02EhxdQEqubLs/WgRokBiAUuVlGKq0A0H2fbODt0FPmIVqhSdj7pPdRUuGZHNOZitp0tgGLIUVk5Yd8Nue4ZFtcAlJcXJp9Ha2XRoBPEgmN0OfVR3GGN6QLJT/dEv/k+ivJmlzPDUmEL021YP5G3Jn64VMOWxtwxtu9OB3j34ssXtsD10VXibKliIaJCJOOZbQvwAW4oqHFHk3sGoEzALRBNjtBYUBXSkCMH47hQJUaG64KmBijWEBcsgVEIRCLZ9OXZGsFhC/T+nCwqH+XbDOSd0ifFqPHGXSv4ISAHK39aohnXQPD+D0BNVEegcDb7AAAAAElFTkSuQmCC"

local base64_lambda_logo = "iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAMAAAAM7l6QAAAASFBMVEVHcEzzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyHzeyG3q9W8AAAAF3RSTlMAA/Qp0a4J++Q0HoSgFuvadlePYj9Iv7JsRtEAAAFnSURBVCjPbVNZFoQgDJN9BxG197/plE2cN8OXdknaELZtHRLMeRpLtn+HFcF1jJqLnf0k7aHhOfr4LiCZY5Ry4b3gFD9TeVGQndaWM5A6wFmBqJMrq0AJ8xrDY8DN/kxbcch4bItIhKNlTMV7KYug4FiAqc/nAEQlYolirLdv0gMcFd4m0I1XGuNAlcmvQdf2AhOxxcSc+ADYkcUDvV7pODc4aS0N/OGrpQBu/PQENtwDj1xVMB7Gn6hIJoIYAoQbYgKVRzuynq802RE5q6e6pRe40QiMjGO4Dv6MJgXQ3ERyczRu12JFgUdYJOOWsb4YabLUMCrfREYQxSNGuixVVOQgx9QTUep2U9R+JdaLYQDL431cwU+t24VKQrax32mwbl1otYNa3qkV0tG2xmM1NNMynxFopp18WTH6yyKFDFez4i5/jJxuIe7UjJy/34p1qb0A9e8ZtILixyMq7O8rI4EZw8Ib9gMjeRh53xVj2gAAAABJRU5ErkJggg=="

local base64_pl_logo = "iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAMAAAAp4XiDAAAATlBMVEUAAAAiJjV+kd+Vo+McIC97jdaCleGKmuESFB5uhtuQnuFRVnN1jOGap+NTYJUrL0U1O1WDkdCDj8NnfcsKCxCMmdaFlNVibaFASWt2hcUMIiBCAAAAx0lEQVRIx+3Tyw6EIAxAUagItSoPxdf//+iAjnFpcTWTeDeyORJIEeLt7ffD9mz1yCOS+qOqrwarWGSqrvrBMwkNud0GxyFEk4uIKNu0rJZ4T2ai+XuEloiCYhBjTqKCMcaxyPln3BK5vwAZLpLXM2OXWuv6INFqrTfkES9TfoFErOAQgDqXAOiRMTSqBoCkYP8snImRmRzbbIuPnBlTTdeBdSmJkTfJmXRr0Xt5QsameUAaX0xsLCUWRSEpFUIVCxFRvL39Xx/a4wnC88V8bQAAAABJRU5ErkJggg=="

local base64_gs_logo = "iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAMAAAAp4XiDAAAAllBMVEUREREODhBbowsSExERExEVGRMUFxMYHRUTFBISFhEZIRj///9aoAsYJhIMCREfKR0dLRh71iIqOChhrA5epwxEcxR2zSEjMR9zySA2WRP5+fgjNg/ByL8vThBMgxAsRRhUlQw7ZRBtvB9VZVJ3hXPQ1c9RixmDkX9ufGuNmYlGVUPg5N5icV9/3SN7iXicppru8O2G5CkpLiXCAAADYklEQVRIx4WWCXOyMBCGA4YEQhJCOYQq4FGt2vP7/3/u2xxQDm3f6YzjNI+77ybZDfJH4jzjDUZ3tJKbnLtVaAzkeSEkuissi4zPEZ5n7UZ64QPEE/4sCvezXefJEKMHCuXehUGDiZpIHKJfFOysHTQy8SsAYZqsR343MZLXmjBIm2jQYGLlhO+FsYVGmb8PXE6rmTDGM8YUGhXK5WRWhYOmdF9oE8XzbE52fUScwjlsw9QQBtng2ACEBE6ERFE4w2AViqDQg2kHUCtDARaZsBrXDI5klw0IEGY9s7KUxnpYQ5CS12ZTgrHEaoDMBw00o1PThR4TGmBMKCUotRClGEMU5JHAMSu5d4gjhDp+bU+n7ddRwFfKAlaf88w/19QjFoGjZomIWOJ2ip22N4hHN1lqdagx1H013DgdBHwk4j2Ony3xHF+DhKlDWqVVVabVRyuDaIRoJ5o46qXbz+Pxso3fVJIExUdVFptNVxwqGgZ9ZqMgT29AfCqQEO/vTCWMp+kZgSe0aTAlc4Ql7EUT4slIiDVUzwck0r9AI13nBSK2cXxaw/K1/VOKndOy2kPthGCwO2SBQF7xVWkkSeCHRbJmTVqWaVZ0NJgh1r3ovuP4Asj69vkC+rwBU6Qp1Kv0G0JtZvcRdbF1voq1YjVsTFmlaRuMzcwTs8hz/Cq0HdGcDwB97BENFoixr6t1+7peATcIVJqqApLLgpGZSZFfjf+12hok0WdUJVGRlgdB5ki/la8AAGSQpDgr+IeIao1MSxaNDszp9f14+frWXp4OH4e27rr6UKYcLbffHMvnn2MZv0TFP9gWXTD46ObIcPi3/eH/flEJ+HZnv6o9Soa8Qrcz/RW7mit2edI3lKi65bm/2wtvvC21DBcXWTFq7j+jKwRNBu6xIyzChbdatgsLDNJ9LRycZIUMl03Jrv9phGMC8XzTM2TofD9tjPSt74cApPWWDXZollrQqgcfWj7PGhku2jiJFh3ZTQwPkHwX9MMinGk6LAwghR582V7eHUl6Jk2mUohksOdmIkOhMZoOvhVejD4cSq/ZZXYim0L/Jchp02YwLW0UU+jfASRFkeec+z1y/gPAktTwnOCj14Ut9ANpEx2YmDx7uL8LPPyXiemzZyj0HQ0mFu8x9SAMbnoTTv8BeAJSlHpDVMAAAAAASUVORK5CYII="

local base64_nl_logo = "/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/2wBDAQMDAwQDBAgEBAgQCwkLEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBD/wAARCAAyADIDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAgFBgcJCgEC/8QANxAAAAUEAQIEAgUNAAAAAAAAAQIDBAUABgcRCBIhCRMxQWFyGCIzUbEmNThSVmJkcXWBlLTT/8QAGAEBAQEBAQAAAAAAAAAAAAAAAAIBAwX/xAAmEQACAgECBgEFAAAAAAAAAAAAAQIREgMhMUFRkbHBoQQTImKS/9oADAMBAAIRAxEAPwDWPSvQATCBShsRHQBV3SGHstRUMpcUni+7WsUiiDhR8tCuStyJa35gqCTpAmhAerete9etTqzznJJ02WhSrntnF+Sb0ZHkrPx/cc40SU8o68bFrOUyH/VEyZRAB+A18Q2NMi3FJv4SBsK4pGRizCR8zaxa6q7UwDoQVTKXqIO+31gDvW4voZnHjaLbpVSuG2rjtKTPC3VASUNIJlKc7SQaKN1ilN3KYSKAA6H2HWhqm1JQpSlAfaP2pPmD8a6UrCh424MNW9BzLNN6wkbZaNHbZYOpNdFRoQp0zB7lMURAQ+Nc1qP2pPmD8a6HLtyW5w5xMTyg1ZldHtm02EiZAR15hCJI9Zf5iUTa+NTrLLRr9l4ZEHj9QnV/jLzEsrg5gOQ42M8l4uXFVSNb3WZ9COTiO3EasgQyBhH3MUAFM37yZh9wrC/B7f08uU3cfzy49/40anfZ12QN92tFXnbD0jyKmmaT1o4IPY6Sheov9++hD2EBqCHB79PLlN/WXP8AujXOE5TnPJU1Guyr0JQjDShg7Tkn/Ur9kR/FmER5gSGxEfydifX5VKhrW3bm54c2YeSud3WULLu20o+NXimLEqMks4Kt1olOBhECJmDQ9Qa71rNz/g+6eOmUpPE15SMY+lYpJsuqvHHOdAxV0gUJoTlKbfSYNhr19x9a3RaxSs7am8nt8GO6UpXUg9ARKIGKOhDuA/cNS1vvxLc5ZAwzIYPlrXs1CDkoZODVcINHAOgQKQpeophVEvWIEDv067j29KiTStvbHkS4pyUuaJVcf/Eezxx2xy2xha0bbUvEMnCqzQZhBdRVuVQeoUiCRQodAG2IAIdhMNUTFPO7LmIctX9mS3oK2HU1kVwd1JovG6wtkTmVFUfJKVQDAGx19Yw9vjUcKVuTu/S+epP2o1jy48X1vbfbfoTyHxkOTH7F49/wnf8A3qJ+e83XZyIyfJ5XvVlGtJaUSbIqoxyR00ClQSKmTpA5jG30lDex9furHtKml0XZFpY833b8sUpShopSlAKUpQClKUApSlAf/9k="

local base64_ft_logo = "iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAMAAAAp4XiDAAAASFBMVEUiG0UgGUMeFz8bFjoXEzLeDGvXEHXtBlzmCmQTDiaPC0jEGIvPE373A1RlE1UmEjc4DC94DUTHEHJQDj+kGICzEGmpFHM/EkF+gnGgAAABD0lEQVRIx+3WYROCIAwGYGUDtUitLP//P20DNayE4V3Xl97vzw3cblgU//wg5aekhHpN3BAAAAwDwCgmrucl7RQutE0UHE6UqqprrY0x1h6PQwdpUs3EeoLbZehceAiEKxInxQuxWUTnkvp8XTKOEL8+ESqi+8YHG+6MSpOaibSVawIcFRVvVVgkxpKJ+8J90/nES4TkcqPc78PQRu8ekqUpLYKAhMOSRWxAym8QEu7ydPtBdDAucvEDg9ih5IstxI9LqjGO0LmI+HFRolbOBKZ9VBZioiRrbyaGSeJDrYjeQQwTzCR2DxkzCOwgo9vcXWpOVm8F8iJKzklI1LRWhOL5iJViEbyVmS/s/ydjOw8LqRQDnldTZgAAAABJRU5ErkJggg==" 

local base64_gp_logo = "iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAMAAAAp4XiDAAAAe1BMVEVHcEw1MkU2OEEnKTM2N0AyL0EmKDIpKjUpKzVAOFEwMjs2OEEvMDo1Nj80Nj8xMjwxMz0sLjgtLzkuMDkzNT4qLDY3OUF6XJiodc8yND2HYqgsLTd6XJi+geu2feKvedicbsCTareOaLGCYKKye928gek4OUI4OkLBg/AHu0sYAAAAJnRSTlMAAe8Z6AUQIiwKhfd22MmQnlFcabk6/Bqbq0RHEfXbrnNXJS7JxALCKUAAAALfSURBVEjHjVaHtqsgEAwIIvZe82LaRf//Cx/ViyXe7DkxFoaZXXYXLpcjo17RdYVHweVLw07eIcZQWRD6FQC4Q4omabDLnC+IqOdHbNLGUOLRPymyMpgsC8rMPSUCTvFLoS0qzsRh4qNpZ8gn4DMigYsXFhlMPmEASaQbbGLyqn7SoU8Yx4cLAWMWDZug7xxGt0BiWqY1LRD5AhUHscZVbGa2IeZdXO2luX4tPzPtBjNk8o7Vvrvz3Yv1V8sXrVM+xN5OVw7lbGZ8HceBHq1oYI63uhJmG0qqiufaguDoZKvM6JJEPLEcAJysW4j4/1YZzpBZPcaiUKUv9cJIRUFcUIYP4iUNdoPJXV46HTQs25iR0iDikGA77YrYTFZ6G13a7bRalwdwqxTqbytlWlcdFwTvOwEnEmFYK+PxmmRk3aPsc6tEirBjpnTBkPR9s2dp+p4UYgCyVlOtY0ea9tY+1iAO4C+vTipGWKsp15HP0YzzfGv7ZgkAuHLAPI+N0hF7YJVfHbk07cztdn9pIvy8v//xN+31ImlgbqrGNY/0LgbM/8b+KqbDfaue71S7m+riBLK4SsLv+tss7f248nx5vNXTrecTkI4PinSh0YLrgrJSGzWtUH/RMjlJ24hRQn2g61nKLKVn4KUwt7tkuSnECyw51TlKF0/XIHSNw+P7PT6kL9fH+PMz3p9aTBgYZTjnt0Giuyigz9fLLA1uns9GbzLA8QOjDA+iKFD4ofOaOnBCKwHc/BRjI6JC57mbiSjDk25tEPmSMXQ4x2hEnFmJToeylhh8hhhWTZZWncAkRxiNKIdNW8ZeKmO9x/Do8nWvu2rXyLGXwCMMIAIRpB4+2sME//abQsDEO/bSzXkDWmMUIvq8ZlQ0IBsj/Tjfx7m4yMJIBPLPTwuypdYaw6ML6zL/80wiWmqQyri5IfriQKJbaiR2bZ6uX1D8Hq4KFw+d79FvD2R8Z8kcmn04Wf0HL2Z3LYl1oZ4AAAAASUVORK5CYII="

local base64_hitmarker = "iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAMAAAAp4XiDAAAAV1BMVEVHcExOTk4hISEAAABqamoAAACdnZ0wMDAYGBgAAACQkJAAAACqqqpfX19DQ0MFBQW7u7uIiIikpKQuLi6ZmZn////7+/vPz8/k5OT39/fIyMjw8PDh4eGgPgKqAAAAFXRSTlMAnlwRwSXtcE0I5BnzsIkz/Nzwa+yk3fOBAAABH0lEQVRIx+3Uy5KDIBAFUEENIIPio9Fg/v87J+hoTQEtZJPKIr0T6lhK96UovvW+El0n8N2a8tJfk2QYVI0J2lutfDNagEeFGHozAJp7q+0CAEvcbAIs9Zc1YGYXpve3BJkR8yduNHhXiRhcYOZKxM21iJmUCE1a/DPSPfEpLU4zM3fuas0Qh1mJI5XJEc48p82ybYisWSeakYKy7RuyTa1sK8XzoiPlkRwhPivTL38OVxXbfr9khMkcwYbV6K2VxK5zVWeIn2cr78odXINnOxCwuO7LHnLMLu5Nt3c/wxyCX2YbFznGF2kTipSJiWszRsWV6aa4OM1D+ZPHZkScZuj8m9di4jABkUrbnqP3gV4sCSIhOcWbLGg7yuJb76tfn5Qd8ovtZW8AAAAASUVORK5CYII="

local widthMap = {
  [' '] = 5, 
  ['!'] = 5, 
  ['"'] = 6, 
  ['#'] = 11,
  ['$'] = 8, 
  ['%'] = 14,
  ['&'] = 9,
  ['\''] = 3,
  ['('] = 6,
  [')'] = 6,
  ['*'] = 8,
  ['+'] = 11,
  [','] = 5,
  ['-'] = 6,
  ['.'] = 5,
  ['/'] = 6,
  ['0'] = 8,
  ['1'] = 8,
  ['2'] = 8,
  ['3'] = 8,
  ['4'] = 8,
  ['5'] = 8,
  ['6'] = 8,
  ['7'] = 8,
  ['8'] = 8,
  ['9'] = 8,
  [':'] = 6,
  [';'] = 6,
  ['<'] = 11,
  ['='] = 11,
  ['>'] = 11,
  ['?'] = 7,
  ['@'] = 13,
  ['A'] = 9,
  ['B'] = 9,
  ['C'] = 9,
  ['D'] = 10,
  ['E'] = 8,
  ['F'] = 7,
  ['G'] = 10,
  ['H'] = 10,
  ['I'] = 5,
  ['J'] = 6,
  ['K'] = 9,
  ['L'] = 7,
  ['M'] = 11,
  ['N'] = 10,
  ['O'] = 10,
  ['P'] = 8,
  ['Q'] = 10,
  ['R'] = 9,
  ['S'] = 9,
  ['T'] = 8,
  ['U'] = 10,
  ['V'] = 9,
  ['W'] = 13,
  ['X'] = 9,
  ['Y'] = 8,
  ['Z'] = 9,
  ['['] = 6,
  ['\\'] = 6,
  [']'] = 6,
  ['^'] = 11,
  ['_'] = 8,
  ['`'] = 8,
  ['a'] = 8,
  ['b'] = 8,
  ['c'] = 7,
  ['d'] = 8,
  ['e'] = 8,
  ['f'] = 5,
  ['g'] = 8,
  ['h'] = 8,
  ['i'] = 4,
  ['j'] = 4,
  ['k'] = 8,
  ['l'] = 4,
  ['m'] = 13,
  ['n'] = 8,
  ['o'] = 8,
  ['p'] = 8,
  ['q'] = 8,
  ['r'] = 6,
  ['s'] = 7,
  ['t'] = 5,
  ['u'] = 8,
  ['v'] = 8,
  ['w'] = 11,
  ['x'] = 8,
  ['y'] = 8,
  ['z'] = 7,
  ['{'] = 8,
  ['|'] = 6,
  ['}'] = 8,
  ['~'] = 11,
}



local pending, last_msg = {}, 1
local is_on_ground = false

local weapon_config = {}
for i = 1, 64 do
    weapon_config[i] = 0
end

local old_weapon_index = 0
local option = UI.GetValue("Weapon_model") or 1

local once = false

local nade_names = {
    ["weapon_molotov"] = true,
    ["weapon_incgrenade"] = true,
    ["weapon_decoy"] = true,
    ["weapon_flashbang"] = true,
    ["weapon_hegrenade"] = true,
    ["weapon_smokegrenade"] = true,
    ["weapon_fire_grenade"] = true,
}
local weapon_names = {
    "weapon_glock",
    "weapon_hkp2000",
    "weapon_usp_silencer",
    "weapon_elite",
    "weapon_p250",
    "weapon_tec9",
    "weapon_fn57",
    "weapon_deagle",
    "weapon_galilar",
    "weapon_famas",
    "weapon_ak47",
    "weapon_m4a1",
    "weapon_m4a1_silencer",
    "weapon_ssg08",
    "weapon_aug",
    "weapon_sg556",
    "weapon_awp",
    "weapon_scar20",
    "weapon_g3sg1",
    "weapon_nova",
    "weapon_xm1014",
    "weapon_mag7",
    "weapon_m249",
    "weapon_negev",
    "weapon_mac10",
    "weapon_mp9",
    "weapon_mp7",
    "weapon_ump45",
    "weapon_p90",
    "weapon_bizon",
    "weapon_knife",
    "weapon_taser",
    "weapon_defuser",
    "weapon_heavyarmor",
    "weapon_molotov",
    "weapon_incgrenade",
    "weapon_decoy",
    "weapon_flashbang",
    "weapon_hegrenade",
    "weapon_smokegrenade",
    "weapon_healthshot",
    "weapon_c4",
    "weapon_revolver",
    "weapon_fire_grenade",
}

local weapon_index_map = {}
for i, name in ipairs(weapon_names) do
    weapon_index_map[name] = i
end
local model_names = {}


local file_names = {}

local hitmarker                 = Renderer.CreateTextureFromBase64(base64_hitmarker, Vector2D(26, 26))
local Legit_bot_icon            = Renderer.CreateTextureFromBase64(base64_legit_bot_icon, Vector2D(26,26))
local Anti_aim_icon             = Renderer.CreateTextureFromBase64(base64_antiaim_icon, Vector2D(26, 26))
local Misc_icon                 = Renderer.CreateTextureFromBase64(base64_misc_icon, Vector2D(26, 26))
local Weapon_model_chngr_icon   = Renderer.CreateTextureFromBase64(base64_weapon_model_chngr, Vector2D(26, 26))
local Config_icon               = Renderer.CreateTextureFromBase64(base64_config_icon, Vector2D(26,26))


local lambda_logo               = Renderer.CreateTextureFromBase64(base64_lambda_logo, Vector2D(30,30))
local pl_logo                   = Renderer.CreateTextureFromBase64(base64_pl_logo, Vector2D(30,30))
local gs_logo                   = Renderer.CreateTextureFromBase64(base64_gs_logo, Vector2D(30,30))
local nl_logo                   = Renderer.CreateTextureFromBase64(base64_nl_logo, Vector2D(30,30))
local ft_logo                   = Renderer.CreateTextureFromBase64(base64_ft_logo, Vector2D(30,30))
local gp_logo                   = Renderer.CreateTextureFromBase64(base64_gp_logo, Vector2D(30,30))
local logo_list = {
    lambda_logo,
    pl_logo,
    gs_logo,
    nl_logo,
    ft_logo,
    gp_logo
}



local shots = {}
local shot_timeout = 0.0001
local missed = false



local tab_tracker = 2

local forward_keybind, forward_keyname, forward_listening = 0, "", false
local backward_keybind, backward_keyname, backward_listening = 0, "", false
local right_keybind, right_keyname, right_listening = 0, "", false
local left_keybind, left_keyname, left_listening = 0, "", false

local right_was_down = false
local left_was_down = false
local forward_was_down = false
local backward_was_down = false

local function KeyBindOn(id)
    if not UI.GetValue(id) then 
        return false
    end
    if UI.GetKeybindType(id) == "always" then return true end

    return UI.GetKeybindEnabled(id)
end


local fake_pitch_zeus_keybind = KeyBindOn("fake_pitch_zeus")
local pitch_modification_keybind   =  KeyBindOn("pitch_modification")
local yaw_modification_keybind   =  KeyBindOn("yaw_modification")
local look_at_nearest_player_keybind   =  KeyBindOn("look_at_nearest_player")
local micro_move_keybind   =  KeyBindOn("micro_move")
local freestanding_keybind   =  KeyBindOn("freestanding")
local autopeek_keybind = KeyBindOn("autopeek")

local autopeek_returning = false
 


local keybind_pos = Vector2D(100, 100)
local spectlist_pos = Vector2D(100, 200)


UI.SetValue("keybind_pos_x", 100)
UI.SetValue("keybind_pos_y", 100)

UI.SetValue("spectlist_pos_x", 100)
UI.SetValue("spectlist_pos_y", 200)


UI.SetValue("antiaim_sidebar_button", false)
UI.SetValue("misc_sidebar_button", false)




UI.SetValue("fake_pitch_zeus", false)
UI.SetValue("trashtalk", false)


UI.SetValue("pitch_modification", false)
UI.SetValue("pitch_mode", 1)
UI.SetValue("custom_pitch", 0)



UI.SetValue("pitch_jitter_mode", 1)
UI.SetValue("pitch_jitter_speed", 5)

UI.SetValue("yaw_modification", false)
UI.SetValue("fix auto-strafer", true)
UI.SetValue("look_at_nearest_player", true)

UI.SetValue("base_yaw_offset", 0) 
UI.SetValue("yaw_mode", 1)
UI.SetValue("yaw_jitter_range", 45)
UI.SetValue("yaw_jitter_speed", 5)
UI.SetValue("spinbot_speed", 10)
UI.SetValue("switch_angle", 90)
UI.SetValue("switch_ticks", 30)
UI.SetValue("x_way_range", 60)


UI.SetValue("micro_move", false)
UI.SetValue("move_jitter_intensity", 50)
UI.SetValue("move_jitter_speed", 3)

UI.SetValue("show_indicators", false)
UI.SetValue("show_speedometer", false)
UI.SetValue("show_killeffect", true)
UI.SetValue("show_hitmarker", false)
UI.SetValue("show_hitdmg", false)

UI.SetValue("killeffect", 1)

UI.SetValue("Weapon_model", 0)
UI.SetValue("change_model", false)
UI.SetValue("theme", 17)
UI.SetValue("indicators_pos", 2)
UI.SetValue("play_hitsound", false)
UI.SetValue("anti_back_stab", true)
UI.SetValue("show_anti_back_stab_target", true)
UI.SetValue("randomize_angles_on_miss", false)

UI.SetValue("freestanding", false)
UI.SetValue("autopeek", false)

UI.SetValue("hitsound", 1)
UI.SetValue("show_movement_indicators", false)
UI.SetValue("indicators_sound", false)


UI.SetValue("movement_indicator_sound", 2)
UI.SetValue("Manual_dir_indicator", true)

UI.SetValue("keybindlist_enabled", true)
UI.SetValue("show_spect_list", true)

UI.SetValue("border_less", false)
UI.SetValue("Logo", 6)


local current_logo = logo_list[UI.GetValue("Logo")]


local function ProcessShots()
    if not shots or #shots == 0 then return end
    
    local current_time = Globals.GetCurrentTime()
    
    for i = #shots, 1, -1 do
        local shot = shots[i]
        
      
        if current_time - shot.time > shot_timeout then
            if not shot.hit then
         
                missed = true
            end
         
            table.remove(shots, i)
        end
    end
end





local colors = {
    background = Color(0, 0, 0, 180),
    text = Color(255, 255, 255, 255),
    accent = Color(150, 200, 255, 255),
    antiaim = Color(100, 255, 150, 255),
    pitch = Color(255, 180, 100, 255),
    freestand = Color(255, 255, 100, 255),
    movement = Color(255, 150, 200, 255)
}


local indicators = {}
local indicator_positions = {}
local animation_speed = 8
local base_y_offset = 25
local indicator_height = 20
local indicator_spacing = 10

local function listFilesRecursive(base_path)
    local files = {}
    local command = 'dir "' .. base_path .. '" /s /b /a-d' 
    local p = io.popen(command)
    if not p then
        return files
    end
    for file in p:lines() do
        local filename = file:match("([^\\]+)$") 
        table.insert(files, {path = file, file = filename})
    end
    p:close()
    return files
end


local function GetWeaponIndex(name)
    return weapon_index_map[name]
end

local weapons_path = FileSystem.GetGameDirectory() .. "\\csgo\\weapons"
local weapons_path_len = #weapons_path

function calc_yaw(from, to)
    if not to or not from then 
        return 0
    end

    local delta_x = to.x - from.x
    local delta_y = to.y - from.y
    
    local yaw = math.atan2(delta_y, delta_x) * (180 / math.pi)
    
    return yaw 
end

local files = listFilesRecursive(weapons_path)

for _, entry in ipairs(files) do
    local full_path = entry.path
    local file = entry.file

    local relative_path = full_path:sub(weapons_path_len - 6):gsub("\\", "/")

    if file:sub(-7) == ".vmdl_c" then
        local base_name = file:sub(1, -8) 
        local relative_name = relative_path:sub(1, -8) 

        if base_name:sub(-2) == "_w" then
            
        elseif base_name:sub(-2) == "_v" then
            
            base_name = base_name:sub(1, -3)
            table.insert(model_names, base_name)
            table.insert(file_names, relative_name)
        else
            table.insert(model_names, base_name)
            table.insert(file_names, relative_name)
        end
    end
end


local function OnFrameStageNotify(stage)
    if not Globals.IsConnected() or not UI.GetValue("change_model") then return end
    
    option = UI.GetValue("Weapon_model") or 1
    if stage ~= 6 then
        return
    end

    if not once then
        for _, entry in ipairs(files) do
            local full_path = entry.path

            local relative_path = full_path:sub(weapons_path_len - 6):gsub("\\", "/")

            FileSystem.FindOrAddFileName(relative_path)
            FileSystem.PrecacheResource(relative_path:sub(1, -3))
        end
        once = true
    end

    local pawn = local_player or nil

    

    if pawn ~= nil then
        local wpn_services = pawn.m_pWeaponServices
        if wpn_services == nil then
            return
        end

        local viewmodel_services = pawn.m_pViewModelServices
        if viewmodel_services == nil then
            return
        end

        local weapon = Entities.GetEntityFromIndex(wpn_services.m_hActiveWeapon)
        if weapon == nil then
            return
        end

        local weapon_index = GetWeaponIndex(Entities.GetDesignerName(weapon))

        if old_weapon_index ~= weapon_index then
            option = UI.SetValue("Weapon_model", weapon_config[weapon_index])
            old_weapon_index = weapon_index
        end

        weapon_config[weapon_index] = option

        if option == 0 or option == nil then
            return
        end

        

        Entities.SetModel(weapon, file_names[option] .. ".vmdl")

        local viewmodel = viewmodel_services:GetViewModel()
        if viewmodel ~= nil then
            local viewmodel_wpn = Entities.GetEntityFromIndex(viewmodel.m_hWeapon)
            if viewmodel_wpn ~= nil then
                if Entities.GetDesignerName(viewmodel_wpn) == Entities.GetDesignerName(weapon) then
                    Entities.SetModel(viewmodel, file_names[option] .. ".vmdl")
                end
            end
        end
    end
end



local bullet_traces = {}
local current_trace_impacts = {}
local listening_for_impacts = false
local weapon_fire_tick = 0

local function UpdateBulletTraces()
    if listening_for_impacts then
        local current_tick = Globals.GetTickCount()
        
       
        if current_tick > weapon_fire_tick then
            
            if #current_trace_impacts > 0 then
                table.insert(bullet_traces, {
                    fire_tick = weapon_fire_tick,
                    impacts = current_trace_impacts,
                    time = Globals.GetCurrentTime()
                })
                
              
                if #bullet_traces > 10 then
                    table.remove(bullet_traces, 1)
                end
            end
            
           
            current_trace_impacts = {}
            listening_for_impacts = false
        end
    end
end


local function is_melee(w) return w == "weapon_taser" or w == "weapon_knife" end



local function is_nade(w)
    return nade_names[w] == true
end


local function norm_yaw(y)
    while y > 180 do y = y - 360 end
    while y < -180 do y = y + 360 end
    return y
end


local hitdmgarray = {}
local hitmarker_time = 0

local current_weapon = nil
local local_player = nil
local nearest_player = nil
local backstab_target = nil
local spectator_list = {}

function ScanEntities()
    
    current_weapon = nil
    local_player = nil
    nearest_player = nil
    backstab_target = nil
    spectator_list = {}
    
    local nearest_distance = math.huge
    local backstab_distance = 192 
    local nearest_backstab_distance = backstab_distance
    local spectatedTarget = nil
 
    for i = 1, 64 do
        local e = Entities.GetEntityFromIndex(i)
        if e then
            
            if e.m_bIsLocalPlayerController then
                local p = e.m_hPawn
                if p then
                    local_player = p
                    
                    
                    if p.m_iHealth ~= 0 then
                        spectatedTarget = p
                    else
                        local observerServices = p.m_pObserverServices
                        if observerServices then
                            spectatedTarget = Entities.GetEntityFromHandle(observerServices.m_hObserverTarget)
                        end
                    end
                    
                    
                    local ws = p.m_pWeaponServices
                    if ws then
                        local w = Entities.GetEntityFromIndex(ws.m_hActiveWeapon)
                        if w then
                            current_weapon = Entities.GetDesignerName(w)
                        end
                    end
                end
            else
                local enemy_pawn = e.m_hPawn

                
                if enemy_pawn and local_player then
                    local local_node = local_player.m_pGameSceneNode
                    local enemy_node = enemy_pawn.m_pGameSceneNode
                    
                    if local_node and enemy_node then
                        local local_pos = local_node.m_vecAbsOrigin
                        local enemy_pos = enemy_node.m_vecAbsOrigin
                        
                        if local_pos and enemy_pos and enemy_pawn.m_iTeamNum ~= local_player.m_iTeamNum then
                            local distance = local_pos:DistTo(enemy_pos)
                            
                            if distance < nearest_distance then
                                nearest_distance = distance
                                nearest_player = e
                            end
                            
                            if distance < nearest_backstab_distance and enemy_pawn.m_iHealth > 0 then
                                nearest_backstab_distance = distance
                                local WeaponServices = e.m_hPawn.m_pWeaponServices
                                if WeaponServices then
                                    local w = Entities.GetEntityFromIndex(WeaponServices.m_hActiveWeapon)
                                    if w then
                                        local ent_weapon = Entities.GetDesignerName(w)

                                        if is_melee(ent_weapon) then
                                            backstab_target = e
                                        end
                                    end
                                end

                            end
                        end
                    end
                end
                
                
                if spectatedTarget and not e.m_bIsHLTV and not e.m_bPawnIsAlive then
                    local pawn = e.m_hObserverPawn
                    if pawn then
                        local observer_services = pawn.m_pObserverServices
                        if observer_services then
                            local targetEntity = Entities.GetEntityFromHandle(observer_services.m_hObserverTarget)
                            if targetEntity ~= nil and targetEntity == spectatedTarget then
                                local targetController = targetEntity.m_hController
                                if targetController ~= nil then
                                    table.insert(spectator_list, e.m_sSanitizedPlayerName)
                                else
                                    table.insert(spectator_list, e.m_sSanitizedPlayerName .. " (unknown)")
                                end
                            end
                        end
                    end
                end
                
              
            end
        end
    end
end

local function any_wasd_keys_active()
    return Input.GetKeyDown(0x57) or 
           Input.GetKeyDown(0x41) or 
           Input.GetKeyDown(0x53) or 
           Input.GetKeyDown(0x44)
end

function GetWeapon()
    return current_weapon
end

function GetLocalPlayerPawn()
    return local_player
end

function GetNearestPlayer()
    return nearest_player
end

function GetAntiBackstabTarget()
    return backstab_target
end





local function get_movement_angle(forward, side)
    if math.abs(forward) < 0.01 and math.abs(side) < 0.01 then
        return 0
    end
    return math.deg(math.atan2(side, forward))
end


local function get_world_movement_angle(yaw, forward, side)
    local movement_angle = get_movement_angle(forward, side)
    return norm_yaw(yaw + movement_angle)
end






local function fix_move(cmd, original_yaw, new_yaw)
    local yaw_delta = math.rad(new_yaw - original_yaw)
    local cos_yaw = math.cos(yaw_delta)
    local sin_yaw = math.sin(yaw_delta)

    local old_forward = cmd.m_flForwardMove
    local old_side = cmd.m_flLeftMove

    if autopeek_returning and autopeek_keybind and saved_pos then
        local pGameSceneNode = local_player.m_pGameSceneNode
        local local_pos = pGameSceneNode.m_vecAbsOrigin
        

      

        
        local distance = saved_pos:DistTo(local_pos)
        
   
        if distance <= 10 then
            autopeek_returning = false
        else
           
            local angle = calc_yaw(local_pos, saved_pos)
            
            local yaw_delta = math.rad(angle - new_yaw)
            local cos_yaw = math.cos(yaw_delta)
            local sin_yaw = math.sin(yaw_delta)



            cmd.m_flForwardMove = 1.0 * cos_yaw + old_side * sin_yaw
            cmd.m_flLeftMove = 0
            
            
      
            
        end
    elseif not autopeek_keybind and autopeek_returning then
        autopeek_returning = false
    end

    
    
    
end








local function send_msg(m) CVar.ExecuteClientCmd("say " .. m) end

local function get_jitter_pitch()
    j_tick = j_tick + 1
    local speed = math.floor(UI.GetValue("pitch_jitter_speed") or 5)
    local jitter = (j_tick % speed) < (speed / 2)
    local mode = math.floor(UI.GetValue("pitch_jitter_mode") or 1)
    local fake = -2182423346297399750336966557899
    local up, down = -89, 89
    
    if mode == 1 then return jitter and 0 or fake
    elseif mode == 2 then return jitter and 0 or -fake
    elseif mode == 3 then return jitter and -fake or fake
    elseif mode == 4 then return jitter and fake or up
    elseif mode == 5 then return jitter and fake or down
    elseif mode == 6 then return jitter and -fake or up
    elseif mode == 7 then return jitter and -fake or down
    else return jitter and up or down end
end


local true_original_yaw = 0
local corrected_orginal_angle = false


local function get_mod_yaw(orig, tick)
    if not orig or not tick then return orig end

    if Input.GetKeyDown(0x45) then return orig end

    local mode = math.floor(UI.GetValue("yaw_mode") or 1)
    local nearest_ent = GetNearestPlayer()
    local abs_target = GetAntiBackstabTarget()
    if UI.GetValue("anti_back_stab") and abs_target then 
        local p_gameSceneNode = local_player.m_pGameSceneNode
        local local_pos = p_gameSceneNode.m_vecAbsOrigin
        local abs_target_pawn = abs_target.m_hPawn
        local abs_target_gameSceneNode = abs_target_pawn.m_pGameSceneNode
        local abs_target_pos = abs_target_gameSceneNode.m_vecAbsOrigin
        base_yaw = norm_yaw(calc_yaw(local_pos, abs_target_pos))
      
    elseif (saved_pos and freestanding_keybind) or autopeek_returning then
        
        if local_player and local_player.m_pGameSceneNode and local_player.m_pGameSceneNode.m_vecAbsOrigin then
            local p_gameSceneNode = local_player.m_pGameSceneNode
            local local_pos = p_gameSceneNode.m_vecAbsOrigin
            base_yaw = norm_yaw(calc_yaw(local_pos, saved_pos))
            if base_yaw == 0 then
                base_yaw = norm_yaw(orig + (UI.GetValue("base_yaw_offset") or 0))
            end     
        else
            base_yaw = norm_yaw(orig + (UI.GetValue("base_yaw_offset") or 0))
        end
    elseif look_at_nearest_player_keybind and nearest_ent and nearest_ent.m_hPawn and nearest_ent.m_hPawn.m_pGameSceneNode and nearest_ent.m_hPawn.m_pGameSceneNode.m_vecAbsOrigin then
        
        local nearest_ent_pawn = nearest_ent.m_hPawn
        local nearest_ent_gameSceneNode = nearest_ent_pawn.m_pGameSceneNode
        local nearest_ent_pos = nearest_ent_gameSceneNode.m_vecAbsOrigin

        if local_player and local_player.m_pGameSceneNode and local_player.m_pGameSceneNode.m_vecAbsOrigin then
            local p_gameSceneNode = local_player.m_pGameSceneNode
            local local_pos = p_gameSceneNode.m_vecAbsOrigin
            
            base_yaw = norm_yaw(calc_yaw(local_pos, nearest_ent_pos) + (UI.GetValue("base_yaw_offset") or 0))
        else
            base_yaw = norm_yaw(orig + (UI.GetValue("base_yaw_offset") or 0))
        end
    else
        
        base_yaw = norm_yaw(orig + (UI.GetValue("base_yaw_offset") or 0))
    end

    if not base_yaw then
        base_yaw = orig or 0
    end

    if UI.GetValue("manual_dir") then
        if forward_keybind and Input.GetKeyDown(forward_keybind) then
            if dir_angle == 0 and not forward_was_down then 
                dir_angle = 0 
            else
                if not forward_was_down then
                    dir_angle = 0
                end
            end
            forward_was_down = true
        else
            forward_was_down = false
        end
        
        if backward_keybind and Input.GetKeyDown(backward_keybind) then
            if dir_angle == 180 and not backward_was_down then 
                dir_angle = 0 
            else
                if not backward_was_down then
                    dir_angle = 180
                end
            end
            backward_was_down = true
        else
            backward_was_down = false
        end
        
        if left_keybind and Input.GetKeyDown(left_keybind) then
            if dir_angle == -90 and not left_was_down then 
                dir_angle = 0 
            else
                if not left_was_down then
                    dir_angle = -90
                end
            end
            left_was_down = true
        else
            left_was_down = false
        end
        
        if right_keybind and Input.GetKeyDown(right_keybind) then
            if dir_angle == 90 and not right_was_down then 
                dir_angle = 0 
            else
                if not right_was_down then
                    dir_angle = 90
                end
            end
            right_was_down = true
        else
            right_was_down = false
        end
        
        if dir_angle then
            base_yaw = norm_yaw(base_yaw + dir_angle)
        end
    end

    if mode == 1 then 
        current_yaw_change = base_yaw
        return base_yaw
    elseif mode == 2 then 
        local switch_ticks = math.floor(UI.GetValue("yaw_jitter_speed") or 30)
        if last_sw and tick - last_sw >= switch_ticks then
            sw_dir = (sw_dir and sw_dir * -1) or 1
            last_sw = tick
        elseif not last_sw then
            last_sw = tick
            sw_dir = sw_dir or 1
        end
        local offset = (sw_dir or 1) * math.floor(UI.GetValue("yaw_jitter_range") or 90)
        current_yaw_change = norm_yaw(base_yaw + offset)
        if corrected_orginal_angle then 
            corrected_orginal_angle = false 
        end
        return current_yaw_change
    elseif mode == 3 then 
        local speed = math.floor(UI.GetValue("spinbot_speed") or 10)
        spin_ang = (spin_ang or 0) + speed
        if spin_ang >= 360 then spin_ang = spin_ang - 360 end
        current_yaw_change = norm_yaw(base_yaw + spin_ang)
        return current_yaw_change
    elseif mode == 4 then 
        local random_offset = math.random(-180, 180)
        current_yaw_change = norm_yaw(base_yaw + random_offset)
        return current_yaw_change
    elseif mode == 5 then 
        local switch_ticks = math.floor(UI.GetValue("switch_ticks") or 30)
        if last_sw and tick - last_sw >= switch_ticks then
            way_idx = ((way_idx or 0) + 1) % 3
            last_sw = tick
        elseif not last_sw then
            last_sw = tick
            way_idx = way_idx or 0
        end
        local range = UI.GetValue("x_way_range") or 60
        local angles = {-range/2, 0, range/2}
        local idx = (way_idx or 0) + 1
        if angles[idx] then
            current_yaw_change = norm_yaw(base_yaw + angles[idx])
        else
            current_yaw_change = base_yaw
        end
        return current_yaw_change
    elseif mode == 6 then 
        local switch_ticks = math.floor(UI.GetValue("switch_ticks") or 30)
        if last_sw and tick - last_sw >= switch_ticks then
            way_idx = ((way_idx or 0) + 1) % 4
            last_sw = tick
        elseif not last_sw then
            last_sw = tick
            way_idx = way_idx or 0
        end
        local range = UI.GetValue("x_way_range") or 60
        local angles = {-range/2, -range/6, range/6, range/2}
        local idx = (way_idx or 0) + 1
        if angles[idx] then
            current_yaw_change = norm_yaw(base_yaw + angles[idx])
        else
            current_yaw_change = base_yaw
        end
        return current_yaw_change
    else 
        local switch_ticks = math.floor(UI.GetValue("switch_ticks") or 30)
        if last_sw and tick - last_sw >= switch_ticks then
            way_idx = ((way_idx or 0) + 1) % 5
            last_sw = tick
        elseif not last_sw then
            last_sw = tick
            way_idx = way_idx or 0
        end
        local range = UI.GetValue("x_way_range") or 60
        local angles = {-range/2, -range/4, 0, range/4, range/2}
        local idx = (way_idx or 0) + 1
        if angles[idx] then
            current_yaw_change = norm_yaw(base_yaw + angles[idx])
        else
            current_yaw_change = base_yaw
        end
        return current_yaw_change
    end
end
local tick = 0
local random_after_miss_timer = -1
local function OnPreCreateMove(cmd)
    if not Globals.IsConnected() then 
        tick = 0
        return
     end
    ScanEntities()
    local_player = GetLocalPlayerPawn()
    ProcessShots()
    autopeek_keybind = KeyBindOn("autopeek")

    freestanding_keybind = KeyBindOn("freestanding")
    fake_pitch_zeus_keybind = KeyBindOn("fake_pitch_zeus")
    pitch_modification_keybind   =  KeyBindOn("pitch_modification")
    yaw_modification_keybind   =  KeyBindOn("yaw_modification")
    look_at_nearest_player_keybind   =  KeyBindOn("look_at_nearest_player")
    micro_move_keybind   =  KeyBindOn("micro_move")



    if local_player then 
        is_on_ground = bit.band(local_player.m_fFlags, 0x1) ~= 0
    end
    
    true_original_yaw = local_player.v_angle.y 
    if true_original_yaw == nil then return end

    local new_yaw = cmd.m_angViewAngles.y
    local new_pitch = cmd.m_angViewAngles.x

    tick = Globals.GetTickCount()
    
    
    if pitch_modification_keybind and not is_nade(GetWeapon()) and not Input.GetKeyDown(0x45) then
        if UI.GetValue("pitch_mode") == 1 then
            new_pitch = -89
        elseif UI.GetValue("pitch_mode") == 2 then
            new_pitch = 89
        elseif UI.GetValue("pitch_mode") == 3 then
            new_pitch = 0
        elseif UI.GetValue("pitch_mode") == 4 then
            new_pitch = UI.GetValue("custom_pitch")
        elseif UI.GetValue("pitch_mode") == 5 then
            new_pitch = get_jitter_pitch()
        elseif UI.GetValue("pitch_mode") == 6 then
            new_pitch = -2182423346297399750336966557899
        end
    end

   
    if fake_pitch_zeus_keybind and is_melee(GetWeapon()) then
        new_pitch = -2182423346297399750336966557899
    end
    if UI.GetValue("0_pitch_free") and (freestanding_keybind or dir_angle ~= 0) then
        new_pitch = 0
    end
   
    if micro_move_keybind then
        mv_tick = mv_tick + 1
        local move_speed = math.floor(UI.GetValue("move_jitter_speed") or 3)
        if mv_tick % move_speed == 0 then mv_dir = mv_dir * -1 end
        local intensity = math.floor(UI.GetValue("move_jitter_intensity") or 50)
        local val = (intensity / 100.0) * -1.0
        cmd.m_flLeftMove = math.max(-1.0, math.min(1.0, cmd.m_flLeftMove + (mv_dir * math.abs(val))))
    end
    
   
    

    if yaw_modification_keybind and not is_nade(GetWeapon()) then
        
            
       
        if not autopeek_returning  then
            new_yaw = get_mod_yaw(true_original_yaw, tick)
        else
             
            local local_pos = local_player.m_pGameSceneNode.m_vecAbsOrigin
            new_yaw = calc_yaw(local_pos, saved_pos)
        end

        
       
            
       
    end

    
    if missed then
       
        if random_after_miss_timer == -1 then
            random_after_miss_timer = Globals.GetTickCount()
        end
        
       
        if Globals.GetTickCount() - random_after_miss_timer <= 10 and UI.GetValue("randomize_angles_on_miss") then
            new_pitch = math.random(-89, 89)
            new_yaw = math.random(-180, 180)
        else
            
            missed = false
            random_after_miss_timer = -1
        end
    elseif random_after_miss_timer ~= -1 then
       
        random_after_miss_timer = -1
    end

    
    cmd.m_angViewAngles = Vector(new_pitch, new_yaw,  cmd.m_angViewAngles.z)

    current_yaw = cmd.m_angViewAngles.y
    
 
    

end

local movement = {
    
    config = {
        jumpbug_height_min = 46,
        bhop_speed_threshold = 5,
        surf_z_tolerance = 2,
        indicator_duration = 1000,
        sound_enabled = true
    },
    
    
    prev_on_ground = false,
    prev_velocity = Vector(0, 0, 0),
    prev_position = Vector(0, 0, 0),
    ground_leave_position = Vector(0, 0, 0),
    ground_leave_time = 0,
    apex_reached = false,
    apex_pos = Vector(0, 0, 0),
    prev_speed_2d = 0,
    air_time = 0,
    z_history = {},
    z_history_size = 35,
    consecutive_bhops = 0,
    frames_since_land = 0,
    last_surf_time = 0,
    surf_cooldown = 0.2,
    
    
    active_indicators = {}
}

local function get_2d_speed(velocity)
    return math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y)
end

local function add_indicator(text)
    if not UI.GetValue("show_movement_indicators") then return end
    table.insert(movement.active_indicators, {
        text = text,
        start_time = Globals.GetCurrentTime() * 1000,
        duration = movement.config.indicator_duration
    })
    
    if movement.config.sound_enabled and UI.GetValue("indicators_sound") then
        local sound = sounds[UI.GetValue("movement_indicator_sound")].path
        CVar.ExecuteClientCmd("play ".. sound)
    end
end

local function detect_jumpbug()
    if not local_player then return false end
    
    local current_pos = local_player.m_pGameSceneNode.m_vecAbsOrigin
    local current_vel = local_player.m_vecAbsVelocity
    
    if movement.prev_velocity.z <= 0 and current_vel.z > 0 and not is_on_ground and not movement.prev_on_ground then
        movement.ground_leave_position = Vector(current_pos.x, current_pos.y, current_pos.z)
        movement.ground_leave_time = Globals.GetCurrentTime()
        
        if movement.apex_reached then
            local jump_height = movement.apex_pos.z - movement.ground_leave_position.z
            
            if jump_height > 30 then
                movement.prev_velocity = Vector(current_vel.x, current_vel.y, current_vel.z)
                movement.apex_reached = false
                return true
            end
            movement.apex_reached = false
        end
    end
    
    if movement.prev_velocity.z > 0 and current_vel.z <= 0 and not movement.apex_reached then
        movement.apex_reached = true
        movement.apex_pos = Vector(current_pos.x, current_pos.y, current_pos.z)
    end
    
    movement.prev_velocity = Vector(current_vel.x, current_vel.y, current_vel.z)
    
    return false
end

local function detect_bhop()
    if not local_player then return false end
    
    local current_vel = local_player.m_vecAbsVelocity
    local current_speed_2d = get_2d_speed(current_vel)
    
    if is_on_ground then
        movement.frames_since_land = movement.frames_since_land + 1
        
        if movement.frames_since_land > 100 then
            movement.consecutive_bhops = 0
        end
    else
        movement.frames_since_land = 0
    end
    
    if not movement.prev_on_ground and is_on_ground then
        local speed_diff = current_speed_2d - movement.prev_speed_2d
        
        if movement.prev_speed_2d > movement.config.bhop_speed_threshold and speed_diff >= -2 then
            movement.consecutive_bhops = movement.consecutive_bhops + 1
            
            if movement.consecutive_bhops > 1 then
                return true
            end
        else
            movement.consecutive_bhops = 0
        end
    end
    
    return false
end

local function detect_surf()
    if not local_player then return false end
    
    local current_pos = local_player.m_pGameSceneNode.m_vecAbsOrigin
    local current_vel = local_player.m_vecAbsVelocity
    
    if not is_on_ground then
        movement.air_time = movement.air_time + Globals.GetFrameTime()
        
        table.insert(movement.z_history, current_pos.z)
        if #movement.z_history > movement.z_history_size then
            table.remove(movement.z_history, 1)
        end
        
        if #movement.z_history >= movement.z_history_size and movement.air_time > 0.6 then
            local z_variance = 0
            local z_sum = 0
            local min_z = movement.z_history[1]
            local max_z = movement.z_history[1]
            
            for i = 1, #movement.z_history do
                z_sum = z_sum + movement.z_history[i]
                if movement.z_history[i] < min_z then min_z = movement.z_history[i] end
                if movement.z_history[i] > max_z then max_z = movement.z_history[i] end
            end
            
            local z_range = max_z - min_z
            local z_avg = z_sum / #movement.z_history
            
            for i = 1, #movement.z_history do
                z_variance = z_variance + (movement.z_history[i] - z_avg) ^ 2
            end
            z_variance = z_variance / #movement.z_history
            
            local is_surfing = z_range < movement.config.surf_z_tolerance and z_variance < 1.0
            
            if is_surfing and z_range < 0.1 then
                return "pixel"
            elseif is_surfing then
                return "surf"
            end
        end
    else
        movement.air_time = 0
        movement.z_history = {}
    end
    
    return false
end

local function update_movement_indicators()
    if not local_player then return end
    
    local current_time = Globals.GetCurrentTime()
    
    if detect_jumpbug() then
        add_indicator("+jb")
    end
   
    local bhop_type = detect_bhop()
    if bhop_type then
        local text = "+bhop"
        
        if movement.consecutive_bhops > 1 then
            text = text .. " x" .. movement.consecutive_bhops
        end
        
        add_indicator(text)
    end
    
    local surf_type = detect_surf()
    if surf_type then
        if current_time - movement.last_surf_time >= movement.surf_cooldown then
            if surf_type == "pixel" then
                add_indicator("+px")
            else
                add_indicator("+surf")
            end
            movement.last_surf_time = current_time
        end
    end
    
    if local_player then
        movement.prev_position = local_player.m_pGameSceneNode.m_vecAbsOrigin
        movement.prev_velocity = local_player.m_vecAbsVelocity
        movement.prev_speed_2d = get_2d_speed(movement.prev_velocity)
    end
    movement.prev_on_ground = is_on_ground
end

local function draw_movement_indicators()
    local current_time = Globals.GetCurrentTime() * 1000
    
    for i = #movement.active_indicators, 1, -1 do
        if current_time - movement.active_indicators[i].start_time > movement.active_indicators[i].duration then
            table.remove(movement.active_indicators, i)
        end
    end
    
    for i, indicator in ipairs(movement.active_indicators) do
        local alpha = 255
        local time_left = indicator.duration - (current_time - indicator.start_time)
        
        if time_left < 200 then
            alpha = math.floor(255 * (time_left / 200))
        end
        
        local color = Color(255,255,255,255)
        
        local x = Renderer.GetScreenSize().x / 2
        local y = Renderer.GetScreenSize().y / 2 + 50
        
        Renderer.DrawText('font', indicator.text, Vector2D(x, y + 20*i), true, true, color)
    end
end







local function OnPostCreateMove(cmd)
    if not Globals.IsConnected() then 
        return
    end
    UpdateBulletTraces()
    update_movement_indicators()
   
  
    if yaw_modification_keybind and not is_nade(GetWeapon()) then
        local new_yaw = cmd.m_angViewAngles.y
     
        
        if UI.GetValue("yaw_modification") and is_on_ground then
            fix_move(cmd, true_original_yaw, get_mod_yaw(new_yaw, tick))
        end
      
        cmd.m_angViewAngles = Vector(cmd.m_angViewAngles.x, new_yaw, cmd.m_angViewAngles.z)
    end
end


local function OnMapLoad()
        tick = 0 
        hitdmgarray = {}
        hitmarker_time = 0
        true_original_yaw = 0
        active_movement_indicators = {}
        consecutive_bhops = 0
        air_time = 0
        current_yaw_change = 0
        j_tick, yj_side, spin_ang, sw_dir, last_sw, way_idx, mv_tick, mv_dir = 0, 0, 0, 1, 0, 0, 0, 1
        yaw_mode = 0
        base_yaw = 0 
        current_yaw_change = 0 
        current_yaw = 0 
        dir_angle = 0
        saved_pos = Vector(0,0,0)
end







local effects = {}
local screen_size = Renderer.GetScreenSize()

function append_effect(type)
    if type == 1 then
        local current_time = Globals.GetCurrentTime()
        
        table.insert(effects, {
            start_time = current_time,
            duration = 2.0  
        })
    end
end

function render_effects()
    if not Globals.IsConnected() then return end
    
    local current_time = Globals.GetCurrentTime()
    
    
    for i = #effects, 1, -1 do
        local effect = effects[i]
        local elapsed = current_time - effect.start_time
        local progress = elapsed / effect.duration
        
        if progress >= 1.0 then
            table.remove(effects, i)
        else
            
            local alpha = math.floor((1.0 - progress) * 190)
            
            
            for y = 0, screen_size.y, 10 do
                local gradient_alpha = math.floor(alpha * (y / screen_size.y) * 0.8)
                local color = Color(173, 216, 230, gradient_alpha)
                Renderer.DrawRectFilled(Vector2D(0, y), Vector2D(screen_size.x, y + 10), color)
            end
        end
    end
end





local function OnFireGameEvent(e)
    if not Globals.IsConnected() then return end
    
    if e:GetName() == "bullet_impact" then
        
        if listening_for_impacts then
            local impact_data = {
                x = e:GetInt("x"),
                y = e:GetInt("y"),
                z = e:GetInt("z"),
                
                tick = Globals.GetTickCount(),
                time = Globals.GetCurrentTime()
            }
            
            
            if e:GetPlayerController("userid") then
                print("impact")
                print(impact_data.x)
                print(impact_data.y)
                print(impact_data.z)

                
                table.insert(current_trace_impacts, impact_data)
                
            end
        end
    end
   
    if e:GetName() == "player_hurt" then
        local playerControllerAttacker = e:GetPlayerController("attacker")
        if playerControllerAttacker ~= nil then
            if playerControllerAttacker.m_bIsLocalPlayerController then
            
                local current_time = Globals.GetCurrentTime()
                for i = #shots, 1, -1 do
                    local shot = shots[i]
                    if current_time - shot.time <= shot_timeout then
                        shot.hit = true
                        break
                    end
                end
                
                if UI.GetValue("play_hitsound") then
                    CVar.ExecuteClientCmd("snd_toolvolume 0.5")
                    local sound = sounds[UI.GetValue("hitsound")].path
                    CVar.ExecuteClientCmd("play ".. sound)
                end

                if UI.GetValue("show_hitmarker") then
                    hitmarker_time = Globals.GetCurrentTime()
                end
                
                if UI.GetValue("show_hitdmg") then
                    local kill = false

                    if e:GetInt("health") <= 0 then
                        kill = true
                    end 

                    table.insert(hitdmgarray, {
                        dmg = e:GetInt("dmg_health"),
                        player = e:GetPlayerController("userid"),
                        time = Globals.GetCurrentTime(),
                        kill = kill
                    })
                end
            end
        end
    end
    
    if e:GetName() == "weapon_fire" then
        local player = e:GetPlayerController("userid")
        if player.m_bIsLocalPlayerController then
            
            if listening_for_impacts and #current_trace_impacts > 0 then
                table.insert(bullet_traces, {
                    fire_tick = weapon_fire_tick,
                    impacts = current_trace_impacts,
                    time = Globals.GetCurrentTime()
                })
                
                
                if #bullet_traces > 10 then
                    table.remove(bullet_traces, 1)
                end
            end
            
            
            current_trace_impacts = {}
            listening_for_impacts = true
            weapon_fire_tick = Globals.GetTickCount()
            
            
            if player.m_hPawn and player.m_hPawn.m_pGameSceneNode then
                local head_pos = player.m_hPawn.m_pGameSceneNode.m_vecAbsOrigin


                local head_impact = {
                    x = head_pos.x,
                    y = head_pos.y,
                    z = head_pos.z + 50,
                    tick = Globals.GetTickCount(),
                    time = Globals.GetCurrentTime()
                }
                table.insert(current_trace_impacts, head_impact)
            end
            
            local shot_data = {
                time = Globals.GetCurrentTime(),
                hit = false 
            }
            table.insert(shots, shot_data)
          
            if autopeek_keybind then
                autopeek_returning = true
            end
        end
    end
    
    if e:GetName() == "player_death" then
        local att = e:GetPlayerController("attacker")
        if att and att.m_bIsLocalPlayerController then
            if UI.GetValue("killeffect") == 1 and UI.GetValue("show_killeffect") then
                append_effect(1)
            end
            
            local time = Globals.GetCurrentTime()
            if time >= last_msg + math.random(2, 4) then
                local set = msgs[math.random(1, #msgs)]
                last_msg = time
                
                if UI.GetValue("trashtalk") then
                    for i, msg in ipairs(set) do
                        local delay = (i == 1) and 0 or math.random(2, 3)
                        table.insert(pending, {time = time + delay, message = msg})
                    end
                end
            end
        end
    end
end




local function GetBulletTracesForDisplay()
    local current_time = Globals.GetCurrentTime()
    local display_traces = {}
    
    
    for i, trace in ipairs(bullet_traces) do
        if current_time - trace.time <= 2.0 then
            table.insert(display_traces, trace)
        end
    end
    
    return display_traces
end







local watermark_config = {

    enabled = true,
    position = 1, 
    logo = true,
    show_username = true,
    show_fps = true,

    show_time = true,
    show_server = true,
    custom_text = "plaguecheat.cc",
    background_alpha = 180,

}
local watermark_colors = {
        background = UI.Colors.WindowBg,
        text = UI.Colors.Text,
        accent = UI.Colors.WindowBorder,
        separator = UI.Colors.WindowBorder
    }



local function get_watermark_info()

    
    watermark_colors = {
        background = UI.Colors.WindowBg,
        text = UI.Colors.Text,
        accent = UI.Colors.WindowBorder,
        separator = UI.Colors.WindowBorder
    }
    local info = {}
    
    
    if watermark_config.custom_text and watermark_config.custom_text ~= "" then
        table.insert(info, watermark_config.custom_text)
    end
    
    
    if watermark_config.show_username then
        local username = Cheat.GetUserName()
        table.insert(info, username)
    end
    
    
    if watermark_config.show_fps then
        local fps = math.floor(1 / Globals.GetFrameTime())
        table.insert(info, fps .. " fps")
    end

    
    
    if watermark_config.show_time then
        local time = os.date("%H:%M:%S")
        table.insert(info, time)
    end
    
    
    if watermark_config.show_server and Globals.IsConnected() then
        table.insert(info, "Connected")
    end
    
    return info
end

local function get_watermark_position(width, height)
    local screen = Renderer.GetScreenSize()
    local margin = 10
    local x, y
    
    if watermark_config.position == 1 then 
        x, y = margin, margin
    elseif watermark_config.position == 2 then 
        x, y = screen.x - width - margin, margin
    elseif watermark_config.position == 3 then 
        x, y = margin, screen.y - height - margin
    else 
        x, y = screen.x - width - margin, screen.y - height - margin
    end
    
    return x, y
end

local function draw_watermark()
    if not watermark_config.enabled then return end
    
    local info = get_watermark_info()
    if #info == 0 then return end
    
    
    local watermark_text = table.concat(info, " | ")
    
    
    local text_size = UI.GetTextSize(watermark_text, widthMap)
    local padding = 8
    local width = text_size + (padding * 2)
    local height = 30
    
    
    local x, y = get_watermark_position(width, height)
    
    
  
    
    
    Renderer.DrawRectFilled(
        Vector2D(x, y),
        Vector2D(x + width, y + height),
        watermark_colors.background,
        4
    )
    
    
    Renderer.DrawRect(
        Vector2D(x, y),
        Vector2D(x + width, y + height),
        watermark_colors.accent,
        1,
        4
    )
    local t = 0
    if watermark_config.logo then
        t = 28
        Renderer.DrawTexture(current_logo, Vector2D(x + padding, y + 6), Vector2D(20,20))

    end
    
    Renderer.DrawText(
        'font',
        watermark_text,
        Vector2D(x + padding + t, y + 8),
        false,
        false,
        watermark_colors.text
    )
end







local function update_indicator_positions(frametime)
    local pos_setting = UI.GetValue("indicators_pos")
    local target_y = base_y_offset
    
    
    
    local active_movement_indicators = {}
    
    for i, indicator in ipairs(indicators) do
        if indicator.active then
            table.insert(active_movement_indicators, indicator)
        end
    end
    
    for i, indicator in ipairs(active_movement_indicators) do
        local target_pos = target_y + (i - 1) * (indicator_height + indicator_spacing)
        
        if not indicator_positions[indicator.id] then
            indicator_positions[indicator.id] = target_pos
        end
        
        indicator_positions[indicator.id] = lerp(
            indicator_positions[indicator.id], 
            target_pos, 
            frametime * animation_speed
        )
    end
    
    
    for id, pos in pairs(indicator_positions) do
        local found = false
        for _, indicator in ipairs(active_movement_indicators) do
            if indicator.id == id then
                found = true
                break
            end
        end
        if not found then
            indicator_positions[id] = nil
        end
    end
end

local function draw_indicator(text, value, color, y_pos, x_pos, align_center)
    local full_text = text .. ": " .. value
    local text_size = UI.GetTextSize(full_text, widthMap)
    local width = math.max(text_size + 24, 120)
    local height = 24
    
    local x
    if align_center then
        x = x_pos - width / 2  
    else
        x = x_pos  
    end
    
    local time = Globals.GetCurrentTime()
    
   
    
    local label_size = UI.GetTextSize(text, widthMap)
    local value_size = UI.GetTextSize(value, widthMap)
    
    local label_color = Color(200, 200, 200, 255)
    local shadow_color = Color(0, 0, 0, 150)
    
    Renderer.DrawText('font', text, Vector2D(x + 11, y_pos + 7), false, true, shadow_color)
    Renderer.DrawText('font', text, Vector2D(x + 10, y_pos + 6), false, true, label_color)
    
    local value_x = x + width - value_size - 10
    Renderer.DrawText('font', value, Vector2D(value_x + 1, y_pos + 7), false, true, shadow_color)
    Renderer.DrawText('font', value, Vector2D(value_x, y_pos + 6), false, true, color)
end

local hitmark_lifetime = 1 
local function draw_hits()
    for i, hit in ipairs(hitdmgarray) do
        if Globals.GetCurrentTime() - hit.time > hitmark_lifetime*2 then
            table.remove(hitdmgarray, i)
        end
        if not hit.pos then
             local pawn = hit.player.m_hPawn
            local gamescenenode = pawn.m_pGameSceneNode
            local abs = gamescenenode.m_vecAbsOrigin
            abs.z = abs.z + 70

            hit.pos = abs
        end
       

        local screen = Renderer.WorldToScreen(hit.pos)
        local color = Color(255,255,255,255)
        if hit.kill then
            color = Color(255,215,0,255)
        end
        Renderer.DrawText('font_large', tostring(hit.dmg), screen, true, true, color)
        
        
    end

    if hitmarker_time ~= 0 then
        if Globals.GetCurrentTime() - hitmarker_time > hitmark_lifetime then
            hitmarker_time = 0
        end

        local center = Renderer.GetScreenSize()
        center.x = center.x/2 -13
        center.y = center.y/2 -13

        Renderer.DrawTexture(hitmarker, center, Vector2D(26,26))

    end
end


local function draw_bullet_traces()
    local bullet_traces = GetBulletTracesForDisplay()
    
    if not local_player then return end
    
    for i, trace in ipairs(bullet_traces) do
        local positions = trace.impacts
        if #positions == 0 then goto continue end
        
        
        local current_time = Globals.GetCurrentTime()
        local age = current_time - trace.time
        local alpha = math.max(0, 255 * (1 - age / 2.0))
        
        local prev_screen_pos = nil
        
        
        local first_impact_pos = Vector(positions[1].x, positions[1].y, positions[1].z)
        local first_screen_pos = Renderer.WorldToScreen(first_impact_pos)
        
        if first_screen_pos then
            prev_screen_pos = first_screen_pos
        end
        
      
        for j = 2, #positions do
            local current_impact_pos = Vector(positions[j].x, positions[j].y, positions[j].z)
            local current_screen_pos = Renderer.WorldToScreen(current_impact_pos)
            
            if current_screen_pos and prev_screen_pos then
                Renderer.DrawLine(prev_screen_pos, current_screen_pos, Color(255, 255, 0, alpha), 1)
            end
            
            prev_screen_pos = current_screen_pos
        end
        
        ::continue::
    end
end

function DrawRoundedEndCaps(centerX, centerY, radius, startAngle, endAngle, color, thickness, bigArcStartAngle, bigArcEndAngle)
    local capRadius = thickness * 0.15
    
    
    
    local adjustedRadius = radius - thickness/2 + ((thickness - 1) * 0.3) / 2
    
    
    local startX = centerX + math.cos(startAngle) * adjustedRadius
    local startY = centerY + math.sin(startAngle) * adjustedRadius
    local endX = centerX + math.cos(endAngle) * adjustedRadius
    local endY = centerY + math.sin(endAngle) * adjustedRadius
    
    
    if bigArcStartAngle and bigArcEndAngle then
        local bigArcRadius = radius - thickness/2 + ((thickness - 1) * 0.3) / 2
        
        
        if math.abs(math.deg(startAngle) - math.deg(bigArcStartAngle)) < 1 then
            startX = centerX + math.cos(bigArcStartAngle) * bigArcRadius
            startY = centerY + math.sin(bigArcStartAngle) * bigArcRadius
        end
        
        
        if math.abs(math.deg(endAngle) - math.deg(bigArcEndAngle)) < 1 then
            endX = centerX + math.cos(bigArcEndAngle) * bigArcRadius
            endY = centerY + math.sin(bigArcEndAngle) * bigArcRadius
        end
    end
    
    
    local outlineColor = Color(0, 0, 0, color.a - 195) 
    local outlineThickness = 1.2
    
    
    Renderer.DrawCircleFilled(Vector2D(startX, startY), outlineColor, capRadius + outlineThickness)
    Renderer.DrawCircleFilled(Vector2D(endX, endY), outlineColor, capRadius + outlineThickness)
    
    
    Renderer.DrawCircleFilled(Vector2D(startX, startY), color, capRadius)
    Renderer.DrawCircleFilled(Vector2D(endX, endY), color, capRadius)
end

function DrawThickArc(centerX, centerY, radius, startAngle, endAngle, color, thickness, bigArcStartAngle, bigArcEndAngle)
    local arcSegments = 33 
    DrawRoundedEndCaps(centerX, centerY, radius, startAngle, endAngle, color, thickness, bigArcStartAngle, bigArcEndAngle)
    
    
    for t = 0, thickness - 1 do
        local currentRadius = radius - thickness/2 + t * 0.3
        
        for i = 0, arcSegments - 1 do
            local angle1 = startAngle + (endAngle - startAngle) * (i / arcSegments)
            local angle2 = startAngle + (endAngle - startAngle) * ((i + 1) / arcSegments)
            
            local x1 = centerX + math.cos(angle1) * currentRadius
            local y1 = centerY + math.sin(angle1) * currentRadius
            local x2 = centerX + math.cos(angle2) * currentRadius
            local y2 = centerY + math.sin(angle2) * currentRadius
            
            Renderer.DrawLine(Vector2D(x1, y1), Vector2D(x2, y2), color, 1)
        end
    end
    
    
    local innerRadius = radius - thickness/2
    local outerRadius = radius - thickness/2 + (thickness - 1) * 0.3
    local outlineColor = Color(0, 0, 0, color.a - 155) 
    
    
    for i = 0, arcSegments - 1 do
        local angle1 = startAngle + (endAngle - startAngle) * (i / arcSegments)
        local angle2 = startAngle + (endAngle - startAngle) * ((i + 1) / arcSegments)
        
        local x1 = centerX + math.cos(angle1) * innerRadius
        local y1 = centerY + math.sin(angle1) * innerRadius
        local x2 = centerX + math.cos(angle2) * innerRadius
        local y2 = centerY + math.sin(angle2) * innerRadius
        
        Renderer.DrawLine(Vector2D(x1, y1), Vector2D(x2, y2), outlineColor, 1)
    end
    
    
    for i = 0, arcSegments - 1 do
        local angle1 = startAngle + (endAngle - startAngle) * (i / arcSegments)
        local angle2 = startAngle + (endAngle - startAngle) * ((i + 1) / arcSegments)
        
        local x1 = centerX + math.cos(angle1) * outerRadius
        local y1 = centerY + math.sin(angle1) * outerRadius
        local x2 = centerX + math.cos(angle2) * outerRadius
        local y2 = centerY + math.sin(angle2) * outerRadius
        
        Renderer.DrawLine(Vector2D(x1, y1), Vector2D(x2, y2), outlineColor, 1)
    end
    
    
end


local currentIndicatorAngle = 0 
local targetIndicatorAngle = 0
local fadeAlpha = 255 
local targetFadeAlpha = 255 
local lastAngleChangeTime = 0 
local isAtZeroAngle = false 
local fadeOutDelay = 3.0 
local fadeSpeed = 0.1 

function DrawAngleIndicator(angle)
    if not UI.GetValue("Manual_dir_indicator") or not  UI.GetValue("manual_dir") then return end
    
    local currentTime = Globals.GetCurrentTime() 
    
    
    local screenSize = Renderer.GetScreenSize()
    local centerX = screenSize.x / 2
    local centerY = screenSize.y / 2
    local radius = 40
    local arcThickness = 18 

    
    local normalizedAngle = angle
    if angle >= 180 or angle <= -180 then
        normalizedAngle = 0
    elseif angle >= 90 then
        normalizedAngle = 67
    elseif angle <= -90 then
        normalizedAngle = -67
    end

    
    local wasAtZero = isAtZeroAngle
    isAtZeroAngle = (normalizedAngle == 0)
    
    
    if isAtZeroAngle then
        
        if not wasAtZero then
            lastAngleChangeTime = currentTime
            targetFadeAlpha = 255 
        else
            
            if currentTime - lastAngleChangeTime >= fadeOutDelay then
                targetFadeAlpha = 0 
            end
        end
    else
        
        targetFadeAlpha = 255
        lastAngleChangeTime = currentTime 
    end

    
    fadeAlpha = fadeAlpha + (targetFadeAlpha - fadeAlpha) * fadeSpeed
    
    
    fadeAlpha = math.max(0, math.min(255, fadeAlpha))
    
    
    if fadeAlpha <= 1 then
        return
    end

    
    targetIndicatorAngle = normalizedAngle
    local animationSpeed = 0.15
    currentIndicatorAngle = currentIndicatorAngle + (targetIndicatorAngle - currentIndicatorAngle) * animationSpeed

    
    local startAngle = math.rad(0)
    local endAngle = math.rad(180)
    
    local grayColor = Color(60, 60, 76, fadeAlpha)
    DrawThickArc(centerX, centerY, radius, startAngle, endAngle, grayColor, arcThickness)

    
    local indicatorAngleRad = math.rad(90 - currentIndicatorAngle)

    
    local indicatorArcSize = math.rad(45)
    local indicatorStartAngle = indicatorAngleRad - indicatorArcSize / 2
    local indicatorEndAngle = indicatorAngleRad + indicatorArcSize / 2

    
    local blueColor = Color(80, 160, 255, fadeAlpha)
    DrawThickArc(centerX, centerY, radius, indicatorStartAngle, indicatorEndAngle, blueColor, arcThickness, startAngle, endAngle)
end








local function draw_indicators()
    if not Globals.IsConnected() then return end

    draw_movement_indicators()
    draw_hits()
    DrawAngleIndicator(dir_angle)
    
    local screen = Renderer.GetScreenSize()
    local center_x = screen.x / 2
    local center_y = screen.y / 2

    if autopeek_keybind or freestanding_keybind or autopeek_returning then
        
        if not saved_pos then
            local pGameSceneNode = local_player.m_pGameSceneNode
            saved_pos = pGameSceneNode.m_vecAbsOrigin
        end
        
        
        Renderer.DrawCircleGradient3D(saved_pos, Color(100, 100, 255, 255), Color(100, 100, 255, 125), 18)
        

    else
        
        saved_pos = nil
    end

    if UI.GetValue("show_anti_back_stab_target") and backstab_target then
        
        local pawn = backstab_target.m_hPawn
        local pGameSceneNode = pawn.m_pGameSceneNode
        local pos = pGameSceneNode.m_vecAbsOrigin
        
        
        Renderer.DrawCircleGradient3D(pos, Color(100, 100, 255, 255), Color(100, 100, 255, 125), 18) 
       


    end
    
    if UI.GetValue("show_speedometer") and local_player then 
        local vel = local_player.m_vecAbsVelocity  
        
        local speed = vel:Length()  
        local speed_text = string.format("%.1f", speed)
        local text_size = UI.GetTextSize(speed_text, widthMap)
        local width = text_size 
        
        local x = Renderer.GetScreenSize().x - width / 2
        local y = Renderer.GetScreenSize().y + 1500
        
        local speed_color = Color(255, 255, 255, 255)
        
    
        Renderer.DrawText('font', speed_text, Vector2D(x + 16, y + 8), false, true, speed_color)
        
    end

    if not UI.GetValue("show_indicators") then return end
    
    local frametime = Globals.GetFrameTime()
    indicators = {}
    
    if UI.GetValue("yaw_modification") then
        local yaw_modes = {"STATIC", "JITTER", "SPIN", "RANDOM", "3-WAY", "4-WAY", "5-WAY"}
        local mode_name = yaw_modes[UI.GetValue("yaw_mode")] or "UNKNOWN"
        
        table.insert(indicators, {
            id = "antiaim",
            text = "Anti-Aim",
            value = mode_name,
            color = Color(120, 200, 255, 255),
            active = true
        })
        
        if math.abs(current_yaw) > 1 then
            table.insert(indicators, {
                id = "yaw_angle",
                text = "Yaw",
                value = string.format("%d°", math.floor(current_yaw)),
                color = Color(255, 180, 120, 255),
                active = true
            })
        end
    end
    
    if UI.GetValue("manual_dir") then
        local dir_text = "CENTER"
        local dir_color = Color(150, 150, 150, 255)
        
        if dir_angle == 0 then
            dir_text = "FORWARD"
            dir_color = Color(100, 255, 100, 255)
        elseif dir_angle == 180 then
            dir_text = "BACKWARD"
            dir_color = Color(255, 100, 100, 255)
        elseif dir_angle == -90 then
            dir_text = "LEFT"
            dir_color = Color(255, 255, 100, 255)
        elseif dir_angle == 90 then
            dir_text = "RIGHT"
            dir_color = Color(100, 255, 255, 255)
        end
        
        table.insert(indicators, {
            id = "manual_dir",
            text = "Manual",
            value = dir_text,
            color = dir_color,
            active = true
        })
    end
    
    local pitch_active = UI.GetValue("pitch_modification")
    if pitch_active then
        local pitch_value = "ON"
        if UI.GetValue("pitch_mode") == 1 then
            pitch_value = "UP"
        elseif UI.GetValue("pitch_mode") == 2 then
            pitch_value = "DOWN"
        elseif UI.GetValue("pitch_mode") == 3 then
            pitch_value = "ZERO"
        elseif UI.GetValue("pitch_mode") == 4 then
            pitch_value = "CUSTOM"
        elseif UI.GetValue("pitch_mode") == 5 then
            pitch_value = "JITTER"
        elseif UI.GetValue("pitch_mode") == 6 then
            pitch_value = "FAKE"
        end
        
        table.insert(indicators, {
            id = "pitch",
            text = "Pitch",
            value = pitch_value,
            color = Color(255, 150, 200, 255),
            active = true
        })
    end
    
    
    
    if UI.GetValue("micro_move") then
        table.insert(indicators, {
            id = "movement",
            text = "Movement",
            value = "MICRO",
            color = Color(255, 200, 100, 255),
            active = true
        })
    end

    update_indicator_positions(frametime)

    for _, indicator in ipairs(indicators) do
        if indicator.active and indicator_positions[indicator.id] then
            local pos_setting = UI.GetValue("indicators_pos")
            local x_pos = center_x  
            local y_pos = center_y + indicator_positions[indicator.id]
            local should_center = true
            
            if pos_setting == 1 then
                
                x_pos = 50  
                should_center = false
            elseif pos_setting == 2 then
                
                x_pos = center_x
                y_pos = center_y  + 50 + indicator_positions[indicator.id]
                should_center = true
            elseif pos_setting == 3 then
                
                x_pos = screen.x - 170  
                should_center = false
            end
            
            draw_indicator(indicator.text, indicator.value, indicator.color, y_pos, x_pos, should_center)
        end
    end
end
    
    





local function keyBindList()
   
    if not UI.GetValue("keybindlist_enabled") then return end
    local keybinds = {
        
        {var = autopeek_keybind, name = "Autopeek"},

        {var = freestanding_keybind, name = "Freestanding"},
        {var = fake_pitch_zeus_keybind, name = "Fake Pitch Zeus"},
        {var = pitch_modification_keybind, name = "Pitch Modification"},
        {var = yaw_modification_keybind, name = "Yaw Modification"},
        {var = look_at_nearest_player_keybind, name = "Look At Nearest Player"},
        {var = micro_move_keybind, name = "Micro Move"}
        

    }
    

    local activeCount = 0
    for _, keybind in pairs(keybinds) do
        if keybind.var then
            activeCount = activeCount + 1
        end
    end
    
   
    
   
    local baseHeight = 40
    local lineHeight = 20
    local padding = 10
    local windowHeight = baseHeight + (activeCount * lineHeight) + padding
    
    local keybindWindow = UI.CreateWindow("Active Keybinds", keybind_pos , Vector2D(200, windowHeight), true)
    
    UI.SetValue("keybind_pos_x", keybind_pos.x)
    UI.SetValue("keybind_pos_y", keybind_pos.y)



    if UI.BeginWindow(keybindWindow) then
        
        UI.currWin.nextY = 35
        for _, keybind in pairs(keybinds) do
            if keybind.var then
                UI.Text(keybind.name)
            end
        end
    end
    UI.EndWindow()
    
end


local function Spectlist()
   
    if not UI.GetValue("show_spect_list") then return end
   

   
    local baseHeight = 40
    local lineHeight = 20
    local padding = 10
    local windowHeight = baseHeight + (#spectator_list * lineHeight) + padding
    
    local Spectwindow = UI.CreateWindow("Spectators", spectlist_pos , Vector2D(200, windowHeight), true)
    
    UI.SetValue("spectlist_pos_x", spectlist_pos.x)
    UI.SetValue("spectlist_pos_y", spectlist_pos.y)

    if UI.BeginWindow(Spectwindow) then
        
        UI.currWin.nextY = 35
        for _, name in pairs(spectator_list) do
            
            UI.Text(name)
            
        end
    end
    UI.EndWindow()
    
end

local function legitbot_content()
    local text = "[legitbot] Adding soon . . ."
    local text_size = UI.GetTextSize(text , widthMap)

    UI.currWin.nextX = 615/2 - text_size/2 + 50
    UI.currWin.nextY = 445/2 - 7

    UI.Text(text)
end


local function antiaim_content()


      


        UI.currWin.nextY = 35
        UI.currWin.nextX = 60  
        
        if UI.BeginChild("left_column_aa", Vector2D(285, 400), true) then  
            UI.Dummy(Vector2D(0, 20))
            UI.Text("Pitch")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))

 

            UI.Checkbox("pitch_modification", "Pitch Modification", false, true, "always")
             UI.ComboBox("pitch_mode", "Pitch Mode", {
                "Up", "Down", "Zero", "Custom", "Jitter", "Fake"
            }, 1)
            if UI.GetValue("pitch_mode") == 4 then
                UI.Slider("custom_pitch", "Custom Pitch", 0, -89, 89, false)
            end

            if UI.GetValue("pitch_mode") == 5 then
                UI.ComboBox("pitch_jitter_mode", "Pitch Jitter Mode", {
                    "0 to Negative Fake", "0 to Positive Fake", "Positive to Negative", 
                    "Negative Fake to Up", "Negative Fake to Down", "Positive Fake to Up", 
                    "Positive Fake to Down", "Up to Down"
                }, 1)
                UI.Slider("pitch_jitter_speed", "Pitch Jitter Speed", 5, 1, 20, false)
            end
        end
        UI.EndChild()

        
        UI.currWin.nextY = 35  
        UI.currWin.nextX = 355

        if UI.BeginChild("right_column_aa", Vector2D(285, 400), true) then  
            UI.Dummy(Vector2D(0, 20))
            UI.Text("Yaw")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))

            UI.Checkbox("yaw_modification", "Yaw Modification", false, true, "always")
            UI.Checkbox("look_at_nearest_player", "Look at nearest player", true, true, "hold")

  
            UI.Slider("base_yaw_offset", "Base Yaw Offset", 0, -180, 180, false)
           
            local yaw_mode_combo = UI.ComboBox("yaw_mode", "Yaw Mode", {
                "Static", "Jitter", "Spinbot", "Random", "3-Way", "4-Way", "5-Way"
            }, 1)
            
            if UI.HasChanged(yaw_mode_combo) then
                yaw_mode = UI.GetValue(yaw_mode_combo)
            end

            if yaw_mode == 2 then
                UI.Slider("yaw_jitter_range", "Yaw Jitter Range", 45, 1, 90, false)
                UI.Slider("yaw_jitter_speed", "Jitter Speed (ticks)", 5, 1, 20, false)
            elseif yaw_mode == 3 then
                UI.Slider("spinbot_speed", "Spinbot Speed", 10, 1, 50, false)
            elseif yaw_mode >= 5 and yaw_mode <= 7 then
                UI.Slider("switch_ticks", "Switch Interval (ticks)", 30, 1, 100, false)
                UI.Slider("x_way_range", "Yaw Range", 60, 0, 180, false)
            end
            UI.Dummy(Vector2D(0, 20))
            UI.Text("Move jitter")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))
          
            UI.Checkbox("micro_move", "Micro Move / Move Jitter", false, true, "toggle")
            UI.Slider("move_jitter_intensity", "Move Jitter Intensity", 50, 0, 100, false)
            UI.Slider("move_jitter_speed", "Move Jitter Speed (ticks)", 3, 1, 20, false)

            UI.Dummy(Vector2D(0, 20))
            UI.Text("Other")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))

            UI.Checkbox("manual_dir", "Manual Direction", false)

            if UI.GetValue("manual_dir") then
                UI.Button("manual_forward", "forward: " .. forward_keyname, Vector2D(80, 25))
                UI.Button("manual_backward", "backward: " .. backward_keyname, Vector2D(80, 25))
                UI.Button("manual_right", "right: " .. right_keyname, Vector2D(80, 25))
                UI.Button("manual_left", "left: " .. left_keyname, Vector2D(80, 25))
            end

            
            if UI.GetValue("manual_forward") or forward_listening then
                forward_listening = true
                
                local keyCode, keyName = UI.GetKeyInput()
                if keyCode == nil and keyName == nil then
                    forward_keybind, forward_keyname = 0, ""
                else
                    forward_keybind, forward_keyname = keyCode, keyName
                    forward_listening = false
                end
            end

            
            if UI.GetValue("manual_backward") or backward_listening then
                backward_listening = true
                
                local keyCode, keyName = UI.GetKeyInput()
                if keyCode == nil and keyName == nil then
                    backward_keybind, backward_keyname = 0, ""
                else
                    backward_keybind, backward_keyname = keyCode, keyName
                    backward_listening = false
                end
            end

            
            if UI.GetValue("manual_right") or right_listening then
                right_listening = true
                
                local keyCode, keyName = UI.GetKeyInput()
                if keyCode == nil and keyName == nil then
                    right_keybind, right_keyname = 0, ""
                else
                    right_keybind, right_keyname = keyCode, keyName
                    right_listening = false
                end
            end

            
            if UI.GetValue("manual_left") or left_listening then
                left_listening = true
                
                local keyCode, keyName = UI.GetKeyInput()
                if keyCode == nil and keyName == nil then
                    left_keybind, left_keyname = 0, ""
                else
                    left_keybind, left_keyname = keyCode, keyName
                    left_listening = false
                end
            end



            UI.Checkbox("freestanding", "Freestanding", false, true, "hold")
            UI.Checkbox("autopeek", "Autopeek", false, true, "hold")


     
          
        end
        UI.EndChild()

end

local function misc_content()

        UI.currWin.nextY = 35
        UI.currWin.nextX = 60  
        
        if UI.BeginChild("left_column", Vector2D(285, 400), true) then  
            UI.Dummy(Vector2D(0, 20))
            UI.Text("Visual")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))


            UI.Checkbox("show_indicators", "Show Indicators", false)
            UI.ComboBox("indicators_pos", "Indicators Position", {
                "Left", "Middle", "Right"
            }, 2)

            UI.Checkbox("show_speedometer", "Show Speedometer", false)
            UI.Checkbox("Manual_dir_indicator", "Better manual dir indicator", true)



            UI.Dummy(Vector2D(0, 20))
            UI.Text("Effects & sounds")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))

            UI.Checkbox("trashtalk", "Trashtalk", false)

            UI.Checkbox("show_killeffect", "Show kill effects", true)
            if UI.GetValue("show_killeffect") then
                local themeCombo = UI.ComboBox("killeffect", "Kill effects", {
                    "Screen lightup" 
                }, 1)
            end
            UI.Checkbox("show_hitmarker", "Show Hit marker", false)
            UI.Checkbox("show_hitdmg", "Show Hit dmg text", false)

            
            
            UI.Checkbox("play_hitsound", "Play hitsound", false)
            if UI.GetValue("play_hitsound") then
                UI.ComboBox("hitsound", "Hitsound", {
                    "Thunder",
                    "Metal Hit", 
                    "Shooting metal",
                    "Water Spalsh",
                    "Droping Nade"
                }, 1)
            end
            UI.Dummy(Vector2D(0, 20))
            UI.Text("Movement")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))

           
            UI.Checkbox("show_movement_indicators", " Show movement indicators", false)
            UI.Checkbox("indicators_sound", " Play sound on indicator popup", false)

            if UI.GetValue("indicators_sound") then
                UI.ComboBox("movement_indicator_sound", "Movement indicators sound", {
                    "Thunder",
                    "Metal Hit", 
                    "Shooting metal",
                    "Water Spalsh",
                    "Droping Nade"
                }, 1)
            end

            UI.Dummy(Vector2D(0, 20))
            UI.Text("Anti-aim Extra")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))

            UI.Checkbox("anti_back_stab", "Anti back-stab", true)
            UI.Checkbox("show_anti_back_stab_target", "Visualize anti back-stab target", true)
            UI.Checkbox("randomize_angles_on_miss", "Randomize angles on miss", false)
            UI.Checkbox("fake_pitch_zeus", "Fake Pitch with Zeus/Knife", false, true, "toggle")
            UI.Checkbox("0_pitch_free", "0 pitch when freestanding / manual dir", false)

            
        end
        UI.EndChild()

        
        UI.currWin.nextY = 35  
        UI.currWin.nextX = 355

        if UI.BeginChild("right_column_misc", Vector2D(285, 400), true) then  
           
            UI.Dummy(Vector2D(0, 20))
            UI.Text("Menu")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))

            local themeCombo = UI.ComboBox("theme", "Change Theme", {
                "Legacy", "Midnight", "Forest", "Crimson", "Purple Haze", "Orange Sunset", 
                "Cyber Cyan", "Golden Sand", "Ice Blue", "Volcano", "Old Windows colors", 
                "CS 1.6 Classic", "Abyss", "Shadow", "Obsidian", "Fagality !!!", "Reworked"
            }, 17)
            if UI.HasChanged(themeCombo) then
                UI.ChangeTheme(UI.GetValue("theme"))
                if UI.GetValue("border_less") then
                    UI.RemoveBorders() 
                end
            end

            UI.ComboBox("Logo", "Custom Logo", {"Lambda", "Plaguecheat", "Gamesense", "Neverlose", "Fagality", "fuck ass gp logo"}, 6)

             if UI.HasChanged("Logo")  then
                current_logo = logo_list[UI.GetValue("Logo")]

            end


            UI.Checkbox("border_less", "Remove borders from ui elements", false)

            if UI.HasChanged("border_less") and UI.GetValue("border_less")  then
                UI.RemoveBorders()
            end
           
           

            UI.Checkbox("keybindlist_enabled", "Show gp keybinds", false)
            UI.Checkbox("show_spect_list", "Show spectator list", false)
        

            
            UI.Checkbox("watermark_enabled", "Enable Watermark", watermark_config.enabled)
            watermark_config.enabled = UI.GetValue("watermark_enabled")
            
            if watermark_config.enabled then
                UI.ComboBox("watermark_position", "Position", {
                    "Top Left", "Top Right", "Bottom Left", "Bottom Right"
                }, watermark_config.position)
                watermark_config.position = UI.GetValue("watermark_position")
                
                UI.TextInput("watermark_custom_text", "Custom Text", watermark_config.custom_text, Vector2D(150, 25))
                watermark_config.custom_text = UI.GetValue("watermark_custom_text")
                
                UI.Checkbox("watermark_show_username", "Show Username", watermark_config.show_username)
                watermark_config.show_username = UI.GetValue("watermark_show_username")
                
                UI.Checkbox("watermark_show_fps", "Show FPS", watermark_config.show_fps)
                watermark_config.show_fps = UI.GetValue("watermark_show_fps")
                
         
                
                UI.Checkbox("watermark_show_time", "Show Time", watermark_config.show_time)
                watermark_config.show_time = UI.GetValue("watermark_show_time")
                
                UI.Checkbox("watermark_show_server", "Show Server Status", watermark_config.show_server)
                watermark_config.show_server = UI.GetValue("watermark_show_server")
                

                
       
            end
        end
        UI.EndChild()

end

local function wpn_mdl_chngr_content()

        UI.currWin.nextY = 35
        UI.currWin.nextX = 60  
        
        if UI.BeginChild("left_column_model", Vector2D(285, 400), true) then  
            UI.Dummy(Vector2D(0, 20))
            UI.Text("Model Changer")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))

            UI.Text("Credits to Soar for his weapon_models.lua")

            local curr_weapon_name = GetWeapon() or "none"

            UI.Checkbox("change_model", "Custom weapon models", false)
            if UI.GetValue("change_model") then
                UI.Text("Current Weapon: " .. curr_weapon_name)
                UI.List("Weapon_model", "Weapon model", model_names, 0)
            end
        end
        UI.EndChild()

        
        UI.currWin.nextY = 35  
        UI.currWin.nextX = 355

        if UI.BeginChild("right_column_model", Vector2D(285, 400), true) then  
    
           
            
        end
        UI.EndChild()

end
local configs, config_names = cfg.ListConfigs("c:\\plaguecheat.cc")
local function config_content( ... )
     UI.currWin.nextY = 35
        UI.currWin.nextX = 60  
        
        if UI.BeginChild("left_column_model", Vector2D(285, 400), true) then  
            UI.Dummy(Vector2D(0, 20))
            UI.Text("Config system")
            UI.Separator()
            UI.Dummy(Vector2D(0, 15))

        

            local curr_weapon_name = GetWeapon() or "none"
            
            UI.Button("refresh_cfgs", "Refresh config list", Vector2D(110, 25))

            if UI.HasChanged("refresh_cfgs") then
                configs, config_names = cfg.ListConfigs("c:\\plaguecheat.cc")
            end

            UI.List("config_list", "Config list", config_names, 0)

            UI.TextInput("file_name", "Config name", "p_cfg", Vector2D(150, 25))

            UI.Button("create_cfg", "Create config", Vector2D(110, 25))

            UI.Button("load_cfg", "Load config", Vector2D(110, 25))
            UI.Button("save_cfg", "Save config", Vector2D(110, 25))

            
            if UI.HasChanged("create_cfg") then
                cfg.CreateNewFile("c:\\plaguecheat.cc\\".. UI.GetValue("file_name")..".gp", UI)
            end 

            if UI.HasChanged("load_cfg") then
                cfg.LoadConfigFromFile("c:\\plaguecheat.cc\\".. config_names[UI.GetValue("config_list")], UI)
                UI.ChangeTheme(UI.GetValue("theme"))
                if UI.GetValue("border_less") then
                    UI.RemoveBorders() 
                end
                current_logo = logo_list[UI.GetValue("Logo")]

                spectlist_pos.x = UI.GetValue("spectlist_pos_x")
                spectlist_pos.y = UI.GetValue("spectlist_pos_y")

                keybind_pos.x = UI.GetValue("keybind_pos_x")
                keybind_pos.y = UI.GetValue("keybind_pos_y")


                
                
            end 


            if UI.HasChanged("save_cfg") and UI.GetValue("config_list")  then
                cfg.SaveConfigToFile("c:\\plaguecheat.cc\\".. config_names[UI.GetValue("config_list")], UI)
            end 



        end
        UI.EndChild()

        
        UI.currWin.nextY = 35  
        UI.currWin.nextX = 355

        if UI.BeginChild("right_column_model", Vector2D(285, 400), true) then  
    
           
            
        end
        UI.EndChild()
end

local function OnRenderer()
    load_screen()
    
    if load_time ~= -1 then return end
    render_effects()        

    if UI.GetValue("trashtalk") and #pending > 0 then
        local time = Globals.GetCurrentTime()
        for i = #pending, 1, -1 do
            if time >= pending[i].time then
                send_msg(pending[i].message)
                table.remove(pending, i)
            end
        end
    elseif not UI.GetValue("trashtalk") then
        pending = {}
    end

    draw_indicators()

    draw_watermark()

    if Input.IsMenuOpen() ~= true then
        UI.HandleKeybinds()
    end
    Spectlist()

    keyBindList()
    if Input.IsMenuOpen() ~= true then return end

    if UI.BeginWindow(mainWindow) then
     
        
        if UI.BeginChild("sidebar", Vector2D(50, 400), true) then
            local sidebarHeight = 420
            local buttonSize = 40
            local spacing = 10
            local totalButtonHeight = (buttonSize * 2) + spacing
            local startY = 5
            
            

            local sidebar = UI.currWin
            Renderer.DrawTexture(current_logo, Vector2D(sidebar.pos.x + 10, sidebar.pos.y + 10), Vector2D(30,30))

            UI.currWin.nextY = startY
            UI.currWin.nextX = spacing/2

            UI.IconButton("legit_bot_sidebar_button", Legit_bot_icon, Vector2D(30,30), Vector2D(40,40), 10) 

            if UI.GetValue("legit_bot_sidebar_button") == true then
                tab_tracker = 1
            end


            UI.currWin.nextY = startY + buttonSize + spacing
            UI.currWin.nextX = spacing/2
            
            UI.IconButton("antiaim_sidebar_button", Anti_aim_icon, Vector2D(30,30), Vector2D(40,40), 10) 

            if UI.GetValue("antiaim_sidebar_button") == true then
                tab_tracker = 2
            end
            
            UI.currWin.nextY = startY + (buttonSize + spacing)*2
            UI.currWin.nextX = spacing/2
            
            UI.IconButton("misc_sidebar_button", Misc_icon, Vector2D(30,30), Vector2D(40,40), 10) 
                

            if UI.GetValue("misc_sidebar_button") == true then
                tab_tracker = 3
            end

            UI.currWin.nextY = startY + (buttonSize + spacing)*3
            UI.currWin.nextX = spacing/2

            UI.IconButton("Weapon_model_chngr_button", Weapon_model_chngr_icon, Vector2D(30,30), Vector2D(40,40), 10) 
        

            if UI.GetValue("Weapon_model_chngr_button") == true then
                tab_tracker = 4
            end
            
            UI.currWin.nextY = startY + (buttonSize + spacing)*4
            UI.currWin.nextX = spacing/2

            UI.IconButton("Config_button", Config_icon, Vector2D(30,30), Vector2D(40,40), 10) 
        

            if UI.GetValue("Config_button") == true then
                tab_tracker = 5
            end

        end
        UI.EndChild()
        if tab_tracker == 1 then
            legitbot_content()
        elseif tab_tracker == 2 then
            antiaim_content()
        elseif tab_tracker == 3 then
            misc_content()
        elseif tab_tracker == 4 then
            wpn_mdl_chngr_content()
        elseif tab_tracker == 5 then
            config_content()
        end
        
        UI.EndWindow()
    end

    UI.Update()
end



Cheat.RegisterCallback("OnPostCreateMove", OnPostCreateMove)
Cheat.RegisterCallback("OnPreCreateMove", OnPreCreateMove)
Cheat.RegisterCallback("OnMapLoad", OnMapLoad)

Cheat.RegisterCallback("OnFireGameEvent", OnFireGameEvent)
Cheat.RegisterCallback("OnRenderer", OnRenderer)
Cheat.RegisterCallback("OnFrameStageNotify", OnFrameStageNotify)