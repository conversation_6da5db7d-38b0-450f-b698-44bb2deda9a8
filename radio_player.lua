local ffi = require("ffi")
package.path = "C:\\plaguecheat.cc\\lib\\?.lua;" .. package.path

ffi.cdef[[
    typedef void* HANDLE;

    typedef struct _STARTUPINFOA {
        unsigned long cb;
        char* lpReserved;
        char* lpDesktop;
        char* lpTitle;
        unsigned long dwX;
        unsigned long dwY;
        unsigned long dwXSize;
        unsigned long dwYSize;
        unsigned long dwXCountChars;
        unsigned long dwYCountChars;
        unsigned long dwFillAttribute;
        unsigned long dwFlags;
        unsigned short wShowWindow;
        unsigned short cbReserved2;
        unsigned char* lpReserved2;
        void* hStdInput;
        void* hStdOutput;
        void* hStdError;
    } STARTUPINFOA;

    typedef struct _PROCESS_INFORMATION {
        void* hProcess;
        void* hThread;
        unsigned long dwProcessId;
        unsigned long dwThreadId;
    } PROCESS_INFORMATION;

    int CreateProcessA(
        const char* lpApplicationName,
        char* lpCommandLine,
        void* lpProcessAttributes,
        void* lpThreadAttributes,
        int bInheritHandles,
        unsigned long dwCreationFlags,
        void* lpEnvironment,
        const char* lpCurrentDirectory,
        STARTUPINFOA* lpStartupInfo,
        PROCESS_INFORMATION* lpProcessInfo
    );

    int CloseHandle(void* hObject);
    int TerminateProcess(void* hProcess, unsigned int uExitCode);
    int GetExitCodeProcess(void* hProcess, unsigned long* lpExitCode);
    unsigned long GetLastError();
]]

local kernel32 = ffi.load("kernel32")

local STARTF_USESHOWWINDOW = 0x00000001
local CREATE_NO_WINDOW = 0x08000000
local STILL_ACTIVE = 259
local SW_HIDE = 0

local GUI = require("plague_gui")
GUI.Initialize()

local LIP = require("LIP")
local configPath = "C:\\plaguecheat.cc\\config\\radio_stations.ini"

local defaultStations = {
    { name = "Antena Rock", url = "https://audio.social3.hr/listen/antena_rock/stream?7360" },
    { name = "ChillSynth FM", url = "http://chillsynth.fm:8000/stream" },
    { name = "Lofi Hip Hop", url = "https://streams.ilovemusic.de/iloveradio17.mp3" }
}

local function loadStations()
    local stations = {}
    local success = true
    local iniData = nil

    iniData = LIP.load(configPath)

    if iniData then
        for sectionName, sectionData in pairs(iniData) do
            if type(sectionData) == "table" and sectionData.name and sectionData.url then
                table.insert(stations, {
                    name = sectionData.name,
                    url = sectionData.url
                })
            end
        end

        if #stations > 0 then
            return stations, "Loaded " .. #stations .. " stations from config/radio_stations.ini"
        end
    end

    return defaultStations, "Using default stations (config/radio_stations.ini not found)"
end

local stations, loadMessage = loadStations()

local currentStation = nil
local isPlaying = false
local ffplayPath = "C:\\plaguecheat.cc\\lib\\ffplay.exe"
local currentProcess = nil
local debugLabel = nil
local currentVolume = 100
local volumeUpdateTimer = 0
local pendingVolumeUpdate = false

local function checkAndDownloadFFplay()
    local ffplayExists = io.open(ffplayPath, "r")
    if ffplayExists then
        ffplayExists:close()
        return true
    end

    local downloadUrl = "https://github.com/ffbinaries/ffbinaries-prebuilt/releases/download/v4.4.1/ffplay-4.4.1-win-64.zip"
    local zipPath = "C:\\plaguecheat.cc\\lib\\ffplay.zip"

    local infoCmd = 'echo [fateware-radio] Required files not found! Downloading required audio player...'
    local downloadCmd = string.format('powershell -Command "Write-Host \\"[fateware-radio] Downloading requirement from GitHub...\\" -ForegroundColor Green; Invoke-WebRequest -Uri \'%s\' -OutFile \'%s\'"', downloadUrl, zipPath)
    local extractCmd = string.format('powershell -Command "Write-Host \\"[fateware-radio] Extracting files...\\" -ForegroundColor Yellow; Expand-Archive -Path \'%s\' -DestinationPath \'C:\\plaguecheat.cc\\lib\' -Force"', zipPath)
    local cleanupCmd = string.format('powershell -Command "Write-Host \\"[fateware-radio] Cleaning up temporary files...\\" -ForegroundColor Cyan; Remove-Item \'%s\' -Force"', zipPath)
    local completeCmd = 'powershell -Command "Write-Host \\"[fateware-radio] Setup complete! You can now use the radio player.\\" -ForegroundColor Green"'

    os.execute(infoCmd)
    os.execute(downloadCmd)
    os.execute(extractCmd)
    os.execute(cleanupCmd)
    os.execute(completeCmd)

    local ffplayCheck = io.open(ffplayPath, "r")
    if ffplayCheck then
        ffplayCheck:close()
        return true
    end
    return false
end

local function killProc(processInfo)
    if processInfo and processInfo.handle then
        kernel32.TerminateProcess(processInfo.handle, 0)
        kernel32.CloseHandle(processInfo.handle)
        kernel32.CloseHandle(processInfo.thread)
    end
end

local function isActive(handle)
    if not handle then return false end
    local exitCode = ffi.new("unsigned long[1]")
    kernel32.GetExitCodeProcess(handle, exitCode)
    return exitCode[0] == STILL_ACTIVE
end

local stopRadio
local startRadio
local playButton

stopRadio = function()
    killProc(currentProcess)
    currentProcess = nil

    isPlaying = false
    currentStation = nil
    if playButton then
        playButton.text = "Play"
    end
    debugLabel.text = "Debug: Worker stopped."
end

startRadio = function(url)
    local si = ffi.new("STARTUPINFOA")
    local pi = ffi.new("PROCESS_INFORMATION")

    si.cb = ffi.sizeof("STARTUPINFOA")
    si.dwFlags = STARTF_USESHOWWINDOW
    si.wShowWindow = SW_HIDE

    local cmd = string.format('"%s" -nodisp -autoexit -loglevel quiet -reconnect 1 -reconnect_streamed 1 -reconnect_delay_max 2 -volume %d "%s"', ffplayPath, currentVolume, url)

    local success = kernel32.CreateProcessA(nil, ffi.new("char[?]", #cmd + 1, cmd), nil, nil, false, CREATE_NO_WINDOW, nil, nil, si, pi)

    if success == 0 then
        debugLabel.text = "Debug: ffplay failed: " .. kernel32.GetLastError()
        return nil
    end

    kernel32.CloseHandle(pi.hThread)
    return {handle = pi.hProcess, thread = pi.hThread}
end

local showRadioGUI = Menu.Checker("Show Radio Player", true)

local mainWindow = GUI.Window("Radio Player", 100, 100, 350, 500)
debugLabel = GUI.Label("Debug: Ready")

if not checkAndDownloadFFplay() then
    debugLabel.text = "Error: Failed to download ffplay.exe"
end

local configLabel = GUI.Label(loadMessage)

local stationNames = {}
for _, station in ipairs(stations) do
    table.insert(stationNames, station.name)
end
local selectedStation = 1
local stationCombo = GUI.Combobox("Station", stationNames, selectedStation - 1, function(value)
    selectedStation = value + 1
end)

playButton = GUI.Button("Play", function()
    if not isPlaying then
        stopRadio()

        local station = stations[selectedStation]
        if station then
            debugLabel.text = string.format("Debug: Starting worker for %s...", station.name)
            local proc = startRadio(station.url)

            if not proc then
                return
            end

            currentProcess = proc
            isPlaying = true
            currentStation = station
            playButton.text = "Stop"
            debugLabel.text = "Debug: Worker started."
        end
    else
        stopRadio()
    end
end)

local volumeSlider = GUI.Slider("Volume", 0, 100, currentVolume, function(value)
    currentVolume = value
    debugLabel.text = string.format("Debug: Volume set to %d%%", currentVolume)

    volumeUpdateTimer = 60
    pendingVolumeUpdate = true
end)

local reloadButton = GUI.Button("Reload Stations", function()
    local newStations, newLoadMessage = loadStations()

    stations = newStations
    configLabel.text = newLoadMessage

    stationNames = {}
    for _, station in ipairs(stations) do
        table.insert(stationNames, station.name)
    end

    selectedStation = 1

    if stationCombo then
        stationCombo.options = stationNames
        stationCombo.selectedIndex = 0
    end

    if isPlaying then
        stopRadio()
    end

    debugLabel.text = "ftwr_debug: Stations reloaded successfully"
end)

local statusLabel = GUI.Label("Ready")
local function OnRadioGUI()
    local showGUI = showRadioGUI:GetBool()
    if showGUI and mainWindow then
        GUI.ShowWindow(mainWindow)
    elseif mainWindow then
        GUI.HideWindow(mainWindow)
    end
end

Cheat.RegisterCallback("OnRenderer", function()
    if pendingVolumeUpdate then
        volumeUpdateTimer = volumeUpdateTimer - 1
        if volumeUpdateTimer <= 0 then
            pendingVolumeUpdate = false
            if isPlaying and currentStation then
                local station = currentStation
                stopRadio()
                local proc = startRadio(station.url)
                if proc then
                    currentProcess = proc
                    isPlaying = true
                    currentStation = station
                    playButton.text = "Stop"
                    debugLabel.text = string.format("Debug: Volume applied at %d%%", currentVolume)
                end
            end
        end
    end

    if isPlaying then
        if not isActive(currentProcess.handle) then
            stopRadio()
        end
    end

    if isPlaying and currentStation then
        statusLabel.text = "Now playing: " .. currentStation.name
    else
        statusLabel.text = "Ready"
    end

    GUI.Render()
end)

Cheat.RegisterCallback("OnRenderer", OnRadioGUI)
