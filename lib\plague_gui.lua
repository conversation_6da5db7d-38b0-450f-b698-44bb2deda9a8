-- plague_gui.lua - Interactive GUI Framework for PlagueCheat by fate (Modern Dark Theme)
-- Updated to match the modern design from granatahelpr.lua
-- Maintains full compatibility with existing scripts

local GUI = {}

-- Internal state
GUI.elements = {}
GUI.windows = {}
GUI.activeWindow = nil
GUI.draggingWindow = nil
GUI.hoveredElement = nil
GUI.activeElement = nil
GUI.openCombobox = nil
GUI.focusedTextbox = nil -- New: Track focused textbox
GUI.lastMouseX = 0
GUI.lastMouseY = 0
GUI.mouseDown = false
GUI.mousePressed = false
GUI.mouseReleased = false

-- Key tracking state (borrowed from keysound.lua)
GUI.activeKeys = {}
GUI.lastKeyTime = 0
GUI.keyRepeatDelay = 0.5 -- Initial delay before repeat
GUI.keyRepeatRate = 0.08 -- Rate of key repeat (slower for better control)

-- Updated font and size to match watermark's base style
GUI.defaultFont = "Arial"
GUI.textSize = 15 -- Matching the default font size of the watermark
GUI.elementSpacing = 10
GUI.initialized = false
GUI.nextElementY = 0
GUI.currentWindow = nil

-- Modern dark theme color definitions (matching granatahelpr.lua)
GUI.colors = {
    -- Modern dark theme colors
    modernDark = Color(25, 25, 28, 240),
    modernDarkSecondary = Color(32, 32, 36, 220),
    modernDarkAccent = Color(40, 40, 44, 200),
    modernBlue = Color(100, 150, 255, 200),
    modernBlueHighlight = Color(120, 170, 255, 240),
    modernGreen = Color(100, 255, 150, 200),
    modernGreenHighlight = Color(120, 255, 170, 240),
    modernYellow = Color(255, 200, 80, 200),
    modernYellowHighlight = Color(255, 220, 100, 240),
    modernRed = Color(255, 100, 100, 200),
    modernRedHighlight = Color(255, 120, 120, 240),
    modernText = Color(245, 245, 245, 255),
    modernTextSecondary = Color(180, 180, 180, 255),
    modernTextMuted = Color(140, 140, 145, 255),
    modernBorder = Color(60, 60, 65, 180),
    modernAccent = Color(0, 122, 255, 200),
    modernSuccess = Color(52, 199, 89, 200),
    
    -- Main component colors using modern theme
    window = Color(25, 25, 28, 240),
    windowOutline = Color(60, 60, 65, 180),
    header = Color(40, 40, 44, 200),
    headerText = Color(245, 245, 245, 255),
    button = Color(32, 32, 36, 220),
    buttonHover = Color(40, 40, 44, 240),
    buttonActive = Color(0, 122, 255, 200),
    checkbox = Color(32, 32, 36, 220),
    checkboxActive = Color(0, 122, 255, 200),
    slider = Color(32, 32, 36, 220),
    sliderTrack = Color(40, 40, 44, 200),
    sliderBar = Color(0, 122, 255, 200),
    sliderHandle = Color(245, 245, 245, 255),
    text = Color(245, 245, 245, 255),
    combobox = Color(32, 32, 36, 220),
    comboboxHover = Color(40, 40, 44, 240),
    comboboxActive = Color(0, 122, 255, 200),
    comboboxArrow = Color(245, 245, 245, 255),
    comboboxDropdownBg = Color(25, 25, 28, 250),
    comboboxDropdownOutline = Color(60, 60, 65, 180),
    comboboxOption = Color(245, 245, 245, 255),
    comboboxOptionHover = Color(0, 122, 255, 200)
}

-- Modern drawing functions (matching granatahelpr.lua style)
local function DrawModernRect(x, y, width, height, color, borderColor, glowEffect)
    borderColor = borderColor or GUI.colors.modernBorder
    glowEffect = glowEffect or false
    
    if glowEffect then
        -- Outer glow effect
        Renderer.DrawRectFilled(Vector2D(x - 1, y - 1), Vector2D(x + width + 1, y + height + 1), Color(color.r, color.g, color.b, 30), 10)
    end
    
    -- Main background with rounded corners
    Renderer.DrawRectFilled(Vector2D(x, y), Vector2D(x + width, y + height), color, 8)
    
    -- Remove the subtle top highlight that was causing the weird glow
    -- Border with rounded corners
    Renderer.DrawRect(Vector2D(x, y), Vector2D(x + width, y + height), borderColor, 8)
end

local function DrawModernText(font, text, pos, centered, color, shadow)
    shadow = shadow ~= false -- default true
    
    if shadow then
        -- Subtle shadow for depth
        local shadowPos = Vector2D(pos.x + 1, pos.y + 1)
        Renderer.DrawText(font, text, shadowPos, centered, false, Color(0, 0, 0, 60))
    end
    
    -- Main text with outline
    Renderer.DrawText(font, text, pos, centered, true, color)
end

-- Rainbow color animation (matching granatahelpr.lua)
local function GetRainbowColor()
    local time = Globals.GetCurrentTime()
    local speed = 2.0
    local r = math.floor(127 * (math.sin(time * speed) + 1))
    local g = math.floor(127 * (math.sin(time * speed + 2.0944) + 1))
    local b = math.floor(127 * (math.sin(time * speed + 4.1888) + 1))
    return Color(r, g, b, 200)
end

function GUI.IsPointInRect(x, y, rectX, rectY, rectWidth, rectHeight)
    return x >= rectX and x <= rectX + rectWidth and
           y >= rectY and y <= rectY + rectHeight
end

function GUI.Initialize()
    if GUI.initialized then return end
    Renderer.LoadFontFromFile(GUI.defaultFont, GUI.defaultFont, GUI.textSize, true)
    Cheat.RegisterCallback("OnRenderer", function() GUI.Render() end)
    GUI.initialized = true
end

local function GetVerticallyAlignedTextY(containerAbsY, containerHeight)
    -- Calculate Y position for text based on container height and GUI.textSize
    -- This aims to vertically center the text within the element's height
    -- The +1 adjustment is a common practice for slight visual correction
    return containerAbsY + (containerHeight - GUI.textSize) / 2 + 1
end

-- Key mapping for text input (extended from keysound.lua)
local keyToCharMap = {
    -- Letters
    [0x41] = "a", [0x42] = "b", [0x43] = "c", [0x44] = "d", [0x45] = "e",
    [0x46] = "f", [0x47] = "g", [0x48] = "h", [0x49] = "i", [0x4A] = "j",
    [0x4B] = "k", [0x4C] = "l", [0x4D] = "m", [0x4E] = "n", [0x4F] = "o",
    [0x50] = "p", [0x51] = "q", [0x52] = "r", [0x53] = "s", [0x54] = "t",
    [0x55] = "u", [0x56] = "v", [0x57] = "w", [0x58] = "x", [0x59] = "y",
    [0x5A] = "z",
    -- Numbers
    [0x30] = "0", [0x31] = "1", [0x32] = "2", [0x33] = "3", [0x34] = "4",
    [0x35] = "5", [0x36] = "6", [0x37] = "7", [0x38] = "8", [0x39] = "9",
    -- Special characters
    [0x20] = " ", -- Space
    [0xBA] = ";", [0xBB] = "=", [0xBC] = ",", [0xBD] = "-", [0xBE] = ".", [0xBF] = "/",
    [0xC0] = "`", [0xDB] = "[", [0xDC] = "\\", [0xDD] = "]", [0xDE] = "'"
}

-- Shifted characters
local shiftedKeyMap = {
    -- Numbers to symbols
    [0x30] = ")", [0x31] = "!", [0x32] = "@", [0x33] = "#", [0x34] = "$",
    [0x35] = "%", [0x36] = "^", [0x37] = "&", [0x38] = "*", [0x39] = "(",
    -- Special characters
    [0xBA] = ":", [0xBB] = "+", [0xBC] = "<", [0xBD] = "_", [0xBE] = ">", [0xBF] = "?",
    [0xC0] = "~", [0xDB] = "{", [0xDC] = "|", [0xDD] = "}", [0xDE] = "\""
}

-- Text input processing
local function ProcessTextInput(textbox)
    local currentTime = Globals.GetCurrentTime()
    local isShiftPressed = Input.GetKeyDown(0x10)
    local isCtrlPressed = Input.GetKeyDown(0x11)
    
    -- Helper function to check if key should trigger (handles repeat timing)
    local function ShouldTriggerKey(keyCode)
        if Input.GetKeyDown(keyCode) then
            if not GUI.activeKeys[keyCode] then
                GUI.activeKeys[keyCode] = currentTime
                return true
            else
                local timeSinceFirst = currentTime - GUI.activeKeys[keyCode]
                if timeSinceFirst >= GUI.keyRepeatDelay then
                    -- Calculate repeat interval
                    local repeatTime = timeSinceFirst - GUI.keyRepeatDelay
                    local repeatCount = math.floor(repeatTime / GUI.keyRepeatRate)
                    local expectedTime = GUI.keyRepeatDelay + (repeatCount + 1) * GUI.keyRepeatRate
                    
                    if timeSinceFirst >= expectedTime then
                        return true
                    end
                end
            end
        else
            GUI.activeKeys[keyCode] = nil
        end
        return false
    end
    
    -- Handle special keys with proper repeat timing
    if ShouldTriggerKey(0x08) then -- Backspace
        if #textbox.text > 0 and textbox.cursorPos > 0 then
            textbox.text = textbox.text:sub(1, textbox.cursorPos - 1) .. textbox.text:sub(textbox.cursorPos + 1)
            textbox.cursorPos = textbox.cursorPos - 1
            if textbox.onChange then textbox.onChange(textbox.text) end
        end
        return
    end
    
    if ShouldTriggerKey(0x2E) then -- Delete
        if textbox.cursorPos < #textbox.text then
            textbox.text = textbox.text:sub(1, textbox.cursorPos) .. textbox.text:sub(textbox.cursorPos + 2)
            if textbox.onChange then textbox.onChange(textbox.text) end
        end
        return
    end
    
    if ShouldTriggerKey(0x25) then -- Left arrow
        textbox.cursorPos = math.max(0, textbox.cursorPos - 1)
        return
    end
    
    if ShouldTriggerKey(0x27) then -- Right arrow
        textbox.cursorPos = math.min(#textbox.text, textbox.cursorPos + 1)
        return
    end
    
    if ShouldTriggerKey(0x24) then -- Home
        textbox.cursorPos = 0
        return
    end
    
    if ShouldTriggerKey(0x23) then -- End
        textbox.cursorPos = #textbox.text
        return
    end
    
    -- Handle Ctrl+A (Select All)
    if isCtrlPressed and ShouldTriggerKey(0x41) then
        textbox.cursorPos = #textbox.text
        -- Note: Full selection would need additional state tracking
        return
    end
    
    -- Handle regular character input (these should only trigger once per press)
    for keyCode, char in pairs(keyToCharMap) do
        if ShouldTriggerKey(keyCode) then
            -- Apply shift modifier
            if isShiftPressed then
                if keyCode >= 0x41 and keyCode <= 0x5A then -- Letters
                    char = char:upper()
                elseif shiftedKeyMap[keyCode] then
                    char = shiftedKeyMap[keyCode]
                end
            end
            
            -- Insert character at cursor position
            if #textbox.text < (textbox.maxLength or 100) then
                textbox.text = textbox.text:sub(1, textbox.cursorPos) .. char .. textbox.text:sub(textbox.cursorPos + 1)
                textbox.cursorPos = textbox.cursorPos + 1
                if textbox.onChange then textbox.onChange(textbox.text) end
            end
            
            break -- Only process one character per frame
        end
    end
end

-- Internal scroll detection using arrow keys only (slower movement)
local lastScrollTime = 0
local scrollDelay = 0.15 -- Delay between scroll steps to make it slower

local function GetArrowScrollDelta()
    local currentTime = Globals.GetCurrentTime()
    local scrollDelta = 0
    
    -- Only allow scrolling if enough time has passed since last scroll
    if currentTime - lastScrollTime >= scrollDelay then
        if Input.GetKeyDown(0x26) then -- Up arrow
            scrollDelta = -1
            lastScrollTime = currentTime
        elseif Input.GetKeyDown(0x28) then -- Down arrow
            scrollDelta = 1
            lastScrollTime = currentTime
        end
    end
    
    return scrollDelta
end

function GUI.ProcessInput()
    local cursorPos = Input.GetCursorPos()
    local mouseX, mouseY = cursorPos.x, cursorPos.y

    local mouseDownState = Input.GetKeyDown(0x01)
    GUI.mousePressed = mouseDownState and not GUI.mouseDown
    GUI.mouseReleased = not mouseDownState and GUI.mouseDown
    GUI.mouseDown = mouseDownState

    local inputHandledByOpenCombobox = false

    -- Handle textbox input if one is focused
    if GUI.focusedTextbox then
        ProcessTextInput(GUI.focusedTextbox)
    end

    if GUI.openCombobox and GUI.openCombobox.isOpen then
        local cb = GUI.openCombobox
        local win = cb.window

        local cbAbsX = win.x + cb.offsetX
        local cbAbsY = win.y + cb.offsetY

        local dropdownX = cbAbsX
        local dropdownY = cbAbsY + cb.height
        local maxVisibleItems = 10
        local itemHeight = 25
        local visibleItems = math.min(#cb.options, maxVisibleItems)
        local dropdownTotalHeight = visibleItems * itemHeight

        -- Initialize scroll position if not set
        if not cb.scrollOffset then
            cb.scrollOffset = 0
        end

        -- Handle arrow key scrolling when combobox is open (slower movement)
        local arrowScrollDelta = GetArrowScrollDelta()
        if arrowScrollDelta ~= 0 and cb.isOpen then
            local maxScroll = math.max(0, #cb.options - maxVisibleItems)
            cb.scrollOffset = math.max(0, math.min(maxScroll, cb.scrollOffset + arrowScrollDelta))
            inputHandledByOpenCombobox = true
        end

        if GUI.mousePressed then
            local clickedOnDropdownItem = false
            if GUI.IsPointInRect(mouseX, mouseY, dropdownX, dropdownY, cb.width, dropdownTotalHeight) then
                local relativeYInDropdown = mouseY - dropdownY
                local visibleItemIndex = math.floor(relativeYInDropdown / itemHeight) + 1
                
                if visibleItemIndex >= 1 and visibleItemIndex <= visibleItems then
                    local actualItemIndex = visibleItemIndex + cb.scrollOffset
                    if actualItemIndex >= 1 and actualItemIndex <= #cb.options then
                        local newIndex = actualItemIndex - 1
                        if newIndex ~= cb.selectedIndex then
                            cb.selectedIndex = newIndex
                            if cb.onChange then cb.onChange(newIndex) end
                        end
                        cb.isOpen = false; GUI.openCombobox = nil; GUI.activeElement = nil
                        inputHandledByOpenCombobox = true; clickedOnDropdownItem = true
                    end
                end
            end

            if not clickedOnDropdownItem then
                local onMainBody = GUI.IsPointInRect(mouseX, mouseY, cbAbsX, cbAbsY, cb.width, cb.height)
                if onMainBody then cb.isOpen = false; GUI.openCombobox = nil; inputHandledByOpenCombobox = true
                else cb.isOpen = false; GUI.openCombobox = nil; end
            end
        end
        if inputHandledByOpenCombobox and GUI.mousePressed then GUI.mousePressed = false end
    end

    if GUI.draggingWindow then
        if GUI.mouseDown then
            local deltaX = mouseX - GUI.lastMouseX; local deltaY = mouseY - GUI.lastMouseY
            GUI.draggingWindow.x = GUI.draggingWindow.x + deltaX
            GUI.draggingWindow.y = GUI.draggingWindow.y + deltaY
        else GUI.draggingWindow = nil end
    end

    GUI.hoveredElement = nil

    for i = #GUI.windows, 1, -1 do
        local window = GUI.windows[i]
        if window.visible then
            local inHeader = GUI.IsPointInRect(mouseX, mouseY, window.x, window.y, window.width, 30)
            if inHeader and GUI.mousePressed and not inputHandledByOpenCombobox then
                GUI.draggingWindow = window; GUI.activeWindow = window
                table.remove(GUI.windows, i); table.insert(GUI.windows, window)
                if GUI.openCombobox then GUI.openCombobox.isOpen = false; GUI.openCombobox = nil; end
                break
            end

            local inWindow = GUI.IsPointInRect(mouseX, mouseY, window.x, window.y, window.width, window.height)
            if inWindow then
                for _, element in ipairs(window.elements) do
                    local elAbsX = window.x + element.offsetX
                    local elAbsY = window.y + element.offsetY
                    if not (element.type == "text" and not element.interactive) then
                        local inElement = GUI.IsPointInRect(mouseX, mouseY, elAbsX, elAbsY, element.width, element.height)
                        if inElement then
                            GUI.hoveredElement = element
                            if GUI.mousePressed and not inputHandledByOpenCombobox then
                                if GUI.openCombobox and GUI.openCombobox ~= element then GUI.openCombobox.isOpen = false; GUI.openCombobox = nil; end
                                GUI.activeElement = element
                                if element.type == "button" and element.onClick then element.onClick()
                                elseif element.type == "checkbox" and element.onChange then element.checked = not element.checked; element.onChange(element.checked)
                                elseif element.type == "textbox" then
                                    -- Focus the textbox
                                    GUI.focusedTextbox = element
                                    element.cursorPos = element.cursorPos or #element.text
                                elseif element.type == "combobox" then
                                    element.isOpen = not element.isOpen
                                    if element.isOpen then GUI.openCombobox = element else GUI.openCombobox = nil end
                                    if GUI.activeWindow ~= window then
                                        GUI.activeWindow = window
                                        for win_idx, win_val in ipairs(GUI.windows) do if win_val == window then table.remove(GUI.windows, win_idx); table.insert(GUI.windows, window); break; end end
                                    end
                                end
                            end
                            if element.type == "slider" then
                                if GUI.mouseDown and (GUI.activeElement == element or (GUI.mousePressed and not inputHandledByOpenCombobox)) then
                                    GUI.activeElement = element
                                    -- Fixed slider interaction - use full width without margins
                                    local relativeX = mouseX - elAbsX
                                    local normalizedValue = math.max(0, math.min(1, relativeX / element.width))
                                    local newValue = math.floor(element.min + normalizedValue * (element.max - element.min))
                                    if newValue ~= element.value then element.value = newValue; if element.onChange then element.onChange(newValue) end end
                                end
                            end
                            if GUI.mousePressed and not inputHandledByOpenCombobox then break end
                        end
                    end
                end
                if GUI.mousePressed and not GUI.hoveredElement and not inputHandledByOpenCombobox then
                    -- Unfocus textbox when clicking outside
                    if GUI.focusedTextbox then
                        GUI.focusedTextbox = nil
                    end
                end
                if GUI.mousePressed and not inputHandledByOpenCombobox then break end
            end
        end
    end
    if GUI.mouseReleased then
        if GUI.activeElement and GUI.activeElement.type ~= "slider" then GUI.activeElement = nil
        elseif not GUI.mouseDown then GUI.activeElement = nil end
    end
    GUI.lastMouseX = mouseX; GUI.lastMouseY = mouseY
end

function GUI.Render()
    GUI.ProcessInput()
    local originalWindowsOrder = {}
    for i,w in ipairs(GUI.windows) do originalWindowsOrder[i] = w end

    for _, window in ipairs(originalWindowsOrder) do
        if window.visible then
            -- Modern window background with glow effect
            DrawModernRect(window.x, window.y, window.width, window.height, GUI.colors.window, GUI.colors.windowOutline, true)

            -- Modern header with gradient effect
            DrawModernRect(window.x, window.y, window.width, 35, GUI.colors.header, Color(0, 0, 0, 0), false)
            
            -- Header accent line
            Renderer.DrawRectFilled(Vector2D(window.x, window.y + 35), Vector2D(window.x + window.width, window.y + 37), GUI.colors.modernAccent)

            -- Modern header text with glow
            local headerTextY = GetVerticallyAlignedTextY(window.y, 35)
            DrawModernText(GUI.defaultFont, window.title, Vector2D(window.x + window.width/2, headerTextY), true, GUI.colors.headerText, true)

            for _, element in ipairs(window.elements) do
                local elAbsX = window.x + element.offsetX
                local elAbsY = window.y + element.offsetY
                local textY_nonCentered = GetVerticallyAlignedTextY(elAbsY, element.height)

                if element.type == "button" then
                    local buttonColor = GUI.colors.button
                    local glowEffect = false
                    
                    if GUI.hoveredElement == element then 
                        buttonColor = GUI.colors.buttonHover
                        glowEffect = true
                    end
                    if GUI.activeElement == element and GUI.mouseDown then 
                        buttonColor = GUI.colors.buttonActive
                        glowEffect = true
                    end
                    
                    -- Modern button with glow on hover/active (no top highlight)
                    DrawModernRect(elAbsX, elAbsY, element.width, element.height, buttonColor, GUI.colors.windowOutline, glowEffect)
                    
                    -- Button text with modern styling
                    local buttonTextY = GetVerticallyAlignedTextY(elAbsY, element.height)
                    DrawModernText(GUI.defaultFont, element.text, Vector2D(elAbsX + element.width/2, buttonTextY), true, GUI.colors.text, true)

                elseif element.type == "checkbox" then
                    local boxSize = element.height - 4
                    
                    -- Modern checkbox background
                    DrawModernRect(elAbsX + 2, elAbsY + 2, boxSize, boxSize, GUI.colors.checkbox, GUI.colors.windowOutline, false)
                    
                    if element.checked then
                        -- Modern checkmark with glow
                        DrawModernRect(elAbsX + 4, elAbsY + 4, boxSize - 4, boxSize - 4, GUI.colors.checkboxActive, Color(0, 0, 0, 0), true)
                        
                        -- Checkmark symbol
                        local centerX = elAbsX + boxSize/2 + 2
                        local centerY = elAbsY + boxSize/2 + 2
                        Renderer.DrawLine(Vector2D(centerX - 4, centerY), Vector2D(centerX - 1, centerY + 3), Color(255, 255, 255, 255), 2)
                        Renderer.DrawLine(Vector2D(centerX - 1, centerY + 3), Vector2D(centerX + 4, centerY - 2), Color(255, 255, 255, 255), 2)
                    end
                    
                    -- Modern checkbox text
                    DrawModernText(GUI.defaultFont, element.text, Vector2D(elAbsX + boxSize + 15, textY_nonCentered), false, GUI.colors.text, true)

                elseif element.type == "slider" then
                    -- Modern slider track (full width, no margins)
                    local trackY = elAbsY + element.height/2 - 3
                    local trackHeight = 6
                    DrawModernRect(elAbsX, trackY, element.width, trackHeight, GUI.colors.sliderTrack, GUI.colors.windowOutline, false)
                    
                    -- Modern slider progress bar with glow (full width calculation)
                    local fillRatio = (element.value - element.min) / (element.max - element.min)
                    local fillWidth = element.width * fillRatio
                    if fillWidth > 0 then
                        DrawModernRect(elAbsX, trackY, fillWidth, trackHeight, GUI.colors.sliderBar, Color(0, 0, 0, 0), true)
                    end
                    
                    -- Modern slider handle - ellipse that spans track height
                    local handleX = elAbsX + fillWidth
                    local handleWidth = 8
                    local handleHeight = trackHeight + 4 -- Slightly taller than track
                    local handleY = trackY - 2 -- Center it on track
                    
                    -- Draw ellipse handle with glow
                    Renderer.DrawRectFilled(Vector2D(handleX - handleWidth/2, handleY), Vector2D(handleX + handleWidth/2, handleY + handleHeight), Color(GUI.colors.sliderHandle.r, GUI.colors.sliderHandle.g, GUI.colors.sliderHandle.b, 100), handleWidth/2)
                    Renderer.DrawRectFilled(Vector2D(handleX - handleWidth/2, handleY), Vector2D(handleX + handleWidth/2, handleY + handleHeight), GUI.colors.sliderHandle, handleWidth/2)
                    
                    -- Modern slider value text with better visibility
                    DrawModernText(GUI.defaultFont, tostring(element.value), Vector2D(elAbsX + element.width + 10, textY_nonCentered), false, GUI.colors.text, true)

                elseif element.type == "text" then
                    -- Modern text with subtle shadow
                    DrawModernText(GUI.defaultFont, element.text, Vector2D(elAbsX, textY_nonCentered), false, GUI.colors.text, true)

                elseif element.type == "combobox" then
                    local bgColor = GUI.colors.combobox
                    local glowEffect = false
                    
                    if GUI.hoveredElement == element or element.isOpen then 
                        bgColor = GUI.colors.comboboxHover
                        glowEffect = true
                    end
                    if GUI.activeElement == element and GUI.mouseDown or element.isOpen then 
                        bgColor = GUI.colors.comboboxActive
                        glowEffect = true
                    end
                    
                    -- Modern combobox with glow on hover/active (no top highlight)
                    DrawModernRect(elAbsX, elAbsY, element.width, element.height, bgColor, GUI.colors.windowOutline, glowEffect)
                    
                    -- Modern combobox text
                    local selectedText = element.options[element.selectedIndex + 1] or ""
                    DrawModernText(GUI.defaultFont, selectedText, Vector2D(elAbsX + 10, GetVerticallyAlignedTextY(elAbsY, element.height)), false, GUI.colors.text, true)
                    
                    -- Modern dropdown arrow with animation
                    local arrowX = elAbsX + element.width - 20
                    local arrowY = elAbsY + element.height/2
                    local arrowSize = 4
                    
                    if element.isOpen then
                        -- Up arrow when open
                        Renderer.DrawLine(Vector2D(arrowX - arrowSize, arrowY + 2), Vector2D(arrowX, arrowY - 2), GUI.colors.comboboxArrow, 2)
                        Renderer.DrawLine(Vector2D(arrowX, arrowY - 2), Vector2D(arrowX + arrowSize, arrowY + 2), GUI.colors.comboboxArrow, 2)
                    else
                        -- Down arrow when closed
                        Renderer.DrawLine(Vector2D(arrowX - arrowSize, arrowY - 2), Vector2D(arrowX, arrowY + 2), GUI.colors.comboboxArrow, 2)
                        Renderer.DrawLine(Vector2D(arrowX, arrowY + 2), Vector2D(arrowX + arrowSize, arrowY - 2), GUI.colors.comboboxArrow, 2)
                    end
                elseif element.type == "textbox" then
                    local isFocused = GUI.focusedTextbox == element
                    local bgColor = GUI.colors.combobox
                    local glowEffect = false
                    
                    if GUI.hoveredElement == element or isFocused then 
                        bgColor = GUI.colors.comboboxHover
                        glowEffect = true
                    end
                    
                    -- Modern textbox with focus indication
                    DrawModernRect(elAbsX, elAbsY, element.width, element.height, bgColor, GUI.colors.windowOutline, glowEffect)
                    
                    -- Add focus border
                    if isFocused then
                        Renderer.DrawRect(Vector2D(elAbsX + 1, elAbsY + 1), Vector2D(elAbsX + element.width - 1, elAbsY + element.height - 1), GUI.colors.modernAccent, 6)
                    end
                    
                    -- Simple text display without complex positioning
                    local displayText = element.text or ""
                    local textX = elAbsX + 8
                    
                    -- Draw text
                    DrawModernText(GUI.defaultFont, displayText, Vector2D(textX, GetVerticallyAlignedTextY(elAbsY, element.height)), false, GUI.colors.text, true)
                    
                    -- Draw placeholder text if empty and not focused
                    if #(element.text or "") == 0 and not isFocused and element.placeholder then
                        DrawModernText(GUI.defaultFont, element.placeholder, Vector2D(textX, GetVerticallyAlignedTextY(elAbsY, element.height)), false, GUI.colors.modernTextMuted, true)
                    end
                end
            end
        end
    end

    -- Modern dropdown rendering with enhanced effects and scrolling
    if GUI.openCombobox and GUI.openCombobox.isOpen then
        local cb = GUI.openCombobox
        local win = cb.window
        local cbAbsX = win.x + cb.offsetX
        local cbAbsY = win.y + cb.offsetY
        local itemHeight = 25
        local dropdownX = cbAbsX
        local dropdownY = cbAbsY + cb.height
        local dropdownWidth = cb.width
        
        local maxVisibleItems = 10
        local visibleItems = math.min(#cb.options, maxVisibleItems)
        local dropdownTotalHeight = visibleItems * itemHeight
        
        -- Initialize scroll offset if not set
        if not cb.scrollOffset then
            cb.scrollOffset = 0
        end
        
        -- Modern dropdown background with glow
        DrawModernRect(dropdownX, dropdownY, dropdownWidth, dropdownTotalHeight, GUI.colors.comboboxDropdownBg, GUI.colors.comboboxDropdownOutline, true)
        
        -- Add scroll hint text if there are more items than visible
        if #cb.options > maxVisibleItems then
            -- Calculate correct position indicator
            local currentStartItem = cb.scrollOffset + 1
            local currentEndItem = math.min(cb.scrollOffset + maxVisibleItems, #cb.options)
            local totalItems = #cb.options
            local remainingItems = totalItems - currentEndItem
            
            local hintText = "Up/Down to navigate (" .. remainingItems .. " more) [" .. currentStartItem .. "-" .. currentEndItem .. "/" .. totalItems .. "]"
            local hintY = dropdownY - 20
            DrawModernText(GUI.defaultFont, hintText, Vector2D(dropdownX + dropdownWidth/2, hintY), true, GUI.colors.modernTextMuted, true)
        end
        
        -- Render visible items based on scroll offset
        local currentItemY = dropdownY
        local cursorPos = Input.GetCursorPos()
        
        for i = 1, visibleItems do
            local optionIndex = i + cb.scrollOffset
            if optionIndex <= #cb.options then
                local optionText = cb.options[optionIndex]
                local itemColor = GUI.colors.comboboxOption
                local itemTextY = GetVerticallyAlignedTextY(currentItemY, itemHeight)
                local isHovered = GUI.IsPointInRect(cursorPos.x, cursorPos.y, dropdownX, currentItemY, dropdownWidth, itemHeight)
                
                if isHovered then
                    itemColor = Color(255, 255, 255, 255)
                    -- Modern hover effect with glow
                    DrawModernRect(dropdownX + 2, currentItemY + 1, dropdownWidth - 4, itemHeight - 2, GUI.colors.comboboxOptionHover, Color(0, 0, 0, 0), true)
                end
                
                -- Highlight selected item with stronger visual feedback
                if optionIndex - 1 == cb.selectedIndex then
                    DrawModernRect(dropdownX + 1, currentItemY, dropdownWidth - 2, itemHeight, Color(GUI.colors.modernAccent.r, GUI.colors.modernAccent.g, GUI.colors.modernAccent.b, 80), Color(0, 0, 0, 0), false)
                    -- Add selection indicator
                    Renderer.DrawRectFilled(Vector2D(dropdownX + 2, currentItemY + itemHeight/2 - 1), Vector2D(dropdownX + 6, currentItemY + itemHeight/2 + 1), Color(255, 255, 255, 200))
                end
                
                -- Modern option text with shadow
                DrawModernText(GUI.defaultFont, optionText, Vector2D(dropdownX + 10, itemTextY), false, itemColor, true)
                currentItemY = currentItemY + itemHeight
            end
        end
        
        -- Draw enhanced scrollbar if needed
        if #cb.options > maxVisibleItems then
            local scrollbarWidth = 12 -- Made wider for better visibility
            local scrollbarX = dropdownX + dropdownWidth - scrollbarWidth - 1
            local scrollbarY = dropdownY + 1
            local scrollbarHeight = dropdownTotalHeight - 2
            
            -- Scrollbar track with glow
            DrawModernRect(scrollbarX, scrollbarY, scrollbarWidth, scrollbarHeight, GUI.colors.sliderTrack, GUI.colors.windowOutline, true)
            
            -- Scrollbar thumb with enhanced visibility
            local maxScroll = #cb.options - maxVisibleItems
            local thumbHeight = math.max(15, scrollbarHeight * (maxVisibleItems / #cb.options))
            local thumbY = scrollbarY + (scrollbarHeight - thumbHeight) * (cb.scrollOffset / (maxScroll > 0 and maxScroll or 1))
            
            DrawModernRect(scrollbarX + 2, thumbY, scrollbarWidth - 4, thumbHeight, GUI.colors.modernAccent, Color(0, 0, 0, 0), true)
        end
        
        -- Draw enhanced scroll indicators (using simple text instead of unicode arrows)
        if #cb.options > maxVisibleItems then
            local indicatorSize = 8
            local indicatorX = dropdownX + dropdownWidth - indicatorSize - 8
            
            -- Up indicator with animation (using "^" instead of unicode)
            if cb.scrollOffset > 0 then
                local upY = dropdownY + 8
                local arrowColor = Color(GUI.colors.modernAccent.r, GUI.colors.modernAccent.g, GUI.colors.modernAccent.b, 255)
                -- Animated pulsing effect
                local time = Globals.GetCurrentTime()
                local pulse = math.floor(200 + 55 * math.sin(time * 4))
                arrowColor.a = pulse
                
                DrawModernText(GUI.defaultFont, "^", Vector2D(indicatorX, upY), true, arrowColor, true)
            end
            
            -- Down indicator with animation (using "v" instead of unicode)
            if cb.scrollOffset < #cb.options - maxVisibleItems then
                local downY = dropdownY + dropdownTotalHeight - 15
                local arrowColor = Color(GUI.colors.modernAccent.r, GUI.colors.modernAccent.g, GUI.colors.modernAccent.b, 255)
                -- Animated pulsing effect
                local time = Globals.GetCurrentTime()
                local pulse = math.floor(200 + 55 * math.sin(time * 4))
                arrowColor.a = pulse
                
                DrawModernText(GUI.defaultFont, "v", Vector2D(indicatorX, downY), true, arrowColor, true)
            end
        end
    end
end

-- Window and Element creation functions (GUI.Window, AddElement, GUI.Button, etc.)
function GUI.Window(title, x, y, width, height)
    local window = {title = title, x = x, y = y, width = width, height = height, visible = true, elements = {}}
    table.insert(GUI.windows, 1, window); GUI.activeWindow = window; GUI.currentWindow = window
    GUI.nextElementY = 40 -- Initial Y offset for first element below header
    return window
end

local function AddElement(elementData)
    if not GUI.currentWindow then return nil end
    local baseElement = {
        window = GUI.currentWindow,
        offsetX = 10,
        offsetY = GUI.nextElementY,
        width = GUI.currentWindow.width - 20,
        height = 20
    }
    for k, v in pairs(elementData) do baseElement[k] = v end
    table.insert(GUI.currentWindow.elements, baseElement)
    GUI.nextElementY = GUI.nextElementY + baseElement.height + GUI.elementSpacing
    return baseElement
end

function GUI.Button(text, onClick)
    return AddElement({type = "button", text = text, height = 30, onClick = onClick})
end

function GUI.Checkbox(text, defaultChecked, onChange)
    return AddElement({type = "checkbox", text = text, height = 20, checked = defaultChecked or false, onChange = onChange})
end

function GUI.Slider(text, min, max, defaultValue, onChange)
    if not GUI.currentWindow then return nil end
    local labelOffsetY = GUI.nextElementY
    AddElement({type = "text", text = text, offsetY = labelOffsetY, height = 20})
    local sliderElement = AddElement({type = "slider", offsetY = labelOffsetY + 20, width = GUI.currentWindow.width - 70, height = 20, min = min, max = max, value = defaultValue or min, onChange = onChange})
    return sliderElement
end

function GUI.Combobox(labelText, options, defaultIndex, onChange)
    if not GUI.currentWindow then return nil end
    local labelOffsetY = GUI.nextElementY
    AddElement({type = "text", text = labelText, offsetY = labelOffsetY, height = 20})
    local comboboxElement = AddElement({
        type = "combobox", 
        offsetY = labelOffsetY + 20, 
        height = 25, 
        options = options or {}, 
        selectedIndex = defaultIndex or 0, 
        isOpen = false, 
        scrollOffset = 0, -- Initialize scroll offset
        onChange = onChange
    })
    return comboboxElement
end

function GUI.Label(text)
    return AddElement({type = "text", text = text, height = 20})
end

function GUI.SetActiveWindow(window)
    GUI.activeWindow = window; GUI.currentWindow = window
    for i, w in ipairs(GUI.windows) do if w == window then table.remove(GUI.windows, i); table.insert(GUI.windows, window); break; end end
end

function GUI.HideWindow(window) window.visible = false end
function GUI.ShowWindow(window) window.visible = true end
function GUI.ToggleWindow(window) window.visible = not window.visible end

function GUI.Textbox(labelText, defaultText, placeholder, maxLength, onChange)
    if not GUI.currentWindow then return nil end
    local labelOffsetY = GUI.nextElementY
    
    -- Add label if provided
    if labelText and labelText ~= "" then
        AddElement({type = "text", text = labelText, offsetY = labelOffsetY, height = 20})
        labelOffsetY = labelOffsetY + 30
    end
    
    -- Create textbox element
    local textboxElement = AddElement({
        type = "textbox", 
        offsetY = labelOffsetY, 
        height = 25, 
        text = defaultText or "", 
        placeholder = placeholder or "Enter text...",
        maxLength = maxLength or 100,
        cursorPos = #(defaultText or ""),
        onChange = onChange
    })
    
    return textboxElement
end

return GUI