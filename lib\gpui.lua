local UI = {}


UI.Colors = {
    WindowBg = Color(40, 40, 40, 245),
    WindowBorder = Color(100, 100, 100, 255),
    ChildBg = Color(40, 40, 40, 245),
    ChildBorder = Color(100, 100, 100, 255),
    TitleBar = Color(60, 60, 60, 255),
    ButtonNormal = Color(70, 70, 70, 255),
    ButtonHover = Color(90, 90, 90, 255),
    ButtonActive = Color(50, 50, 50, 255),
    SliderBg = Color(30, 30, 30, 255),
    SliderKnob = Color(200, 200, 200, 255),
    SliderFill = Color(0, 120, 215, 255),
    Text = Color(255, 255, 255, 255),
    InputBg = Color(50, 50, 50, 255),
    InputBorder = Color(100, 100, 100, 255),
    ComboBg = Color(60, 60, 60, 255)
}






local themes = {
    
    {
        name = "Legacy",
        colors = {
            WindowBg = Color(40, 40, 40, 245),
            WindowBorder = Color(100, 100, 100, 255),
            ChildBg = Color(35, 35, 35, 245),
            ChildBorder = Color(80, 80, 80, 255),
            TitleBar = Color(60, 60, 60, 255),
            ButtonNormal = Color(70, 70, 70, 255),
            ButtonHover = Color(90, 90, 90, 255),
            ButtonActive = Color(50, 50, 50, 255),
            SliderBg = Color(30, 30, 30, 255),
            SliderKnob = Color(200, 200, 200, 255),
            SliderFill = Color(0, 120, 215, 255),
            Text = Color(255, 255, 255, 255),
            InputBg = Color(50, 50, 50, 255),
            InputBorder = Color(100, 100, 100, 255),
            ComboBg = Color(60, 60, 60, 255)
        }
    },
    
    
    {
        name = "Midnight",
        colors = {
            WindowBg = Color(15, 25, 45, 245),
            WindowBorder = Color(80, 120, 180, 255),
            ChildBg = Color(12, 20, 38, 245),
            ChildBorder = Color(60, 90, 140, 255),
            TitleBar = Color(25, 35, 65, 255),
            ButtonNormal = Color(35, 50, 80, 255),
            ButtonHover = Color(50, 70, 110, 255),
            ButtonActive = Color(20, 30, 60, 255),
            SliderBg = Color(10, 20, 40, 255),
            SliderKnob = Color(180, 200, 255, 255),
            SliderFill = Color(100, 150, 255, 255),
            Text = Color(220, 230, 255, 255),
            InputBg = Color(25, 40, 70, 255),
            InputBorder = Color(80, 120, 180, 255),
            ComboBg = Color(30, 45, 75, 255)
        }
    },
    
    
    {
        name = "Forest",
        colors = {
            WindowBg = Color(20, 40, 25, 245),
            WindowBorder = Color(100, 180, 120, 255),
            ChildBg = Color(15, 32, 20, 245),
            ChildBorder = Color(80, 140, 100, 255),
            TitleBar = Color(30, 60, 35, 255),
            ButtonNormal = Color(45, 80, 50, 255),
            ButtonHover = Color(60, 110, 70, 255),
            ButtonActive = Color(25, 50, 30, 255),
            SliderBg = Color(15, 35, 20, 255),
            SliderKnob = Color(200, 255, 220, 255),
            SliderFill = Color(120, 255, 150, 255),
            Text = Color(230, 255, 230, 255),
            InputBg = Color(35, 70, 40, 255),
            InputBorder = Color(100, 180, 120, 255),
            ComboBg = Color(40, 75, 45, 255)
        }
    },
    
    
    {
        name = "Crimson",
        colors = {
            WindowBg = Color(45, 15, 20, 245),
            WindowBorder = Color(180, 80, 100, 255),
            ChildBg = Color(38, 12, 18, 245),
            ChildBorder = Color(140, 60, 80, 255),
            TitleBar = Color(65, 25, 30, 255),
            ButtonNormal = Color(80, 35, 45, 255),
            ButtonHover = Color(110, 50, 65, 255),
            ButtonActive = Color(60, 20, 30, 255),
            SliderBg = Color(40, 10, 15, 255),
            SliderKnob = Color(255, 180, 200, 255),
            SliderFill = Color(255, 100, 140, 255),
            Text = Color(255, 220, 230, 255),
            InputBg = Color(70, 25, 35, 255),
            InputBorder = Color(180, 80, 100, 255),
            ComboBg = Color(75, 30, 40, 255)
        }
    },
    
    
    {
        name = "Purple Haze",
        colors = {
            WindowBg = Color(35, 20, 50, 245),
            WindowBorder = Color(150, 100, 200, 255),
            ChildBg = Color(28, 15, 42, 245),
            ChildBorder = Color(120, 80, 160, 255),
            TitleBar = Color(55, 30, 70, 255),
            ButtonNormal = Color(70, 45, 90, 255),
            ButtonHover = Color(90, 60, 120, 255),
            ButtonActive = Color(50, 25, 70, 255),
            SliderBg = Color(30, 15, 45, 255),
            SliderKnob = Color(220, 180, 255, 255),
            SliderFill = Color(180, 120, 255, 255),
            Text = Color(240, 220, 255, 255),
            InputBg = Color(60, 35, 80, 255),
            InputBorder = Color(150, 100, 200, 255),
            ComboBg = Color(65, 40, 85, 255)
        }
    },
    
    
    {
        name = "Orange Sunset",
        colors = {
            WindowBg = Color(50, 35, 15, 245),
            WindowBorder = Color(200, 150, 80, 255),
            ChildBg = Color(42, 28, 12, 245),
            ChildBorder = Color(160, 120, 60, 255),
            TitleBar = Color(70, 55, 25, 255),
            ButtonNormal = Color(90, 70, 35, 255),
            ButtonHover = Color(120, 90, 50, 255),
            ButtonActive = Color(70, 50, 20, 255),
            SliderBg = Color(45, 30, 10, 255),
            SliderKnob = Color(255, 220, 180, 255),
            SliderFill = Color(255, 180, 100, 255),
            Text = Color(255, 240, 220, 255),
            InputBg = Color(80, 60, 30, 255),
            InputBorder = Color(200, 150, 80, 255),
            ComboBg = Color(85, 65, 35, 255)
        }
    },
    
    
    {
        name = "Cyber Cyan",
        colors = {
            WindowBg = Color(10, 30, 35, 245),
            WindowBorder = Color(80, 200, 220, 255),
            ChildBg = Color(8, 25, 30, 245),
            ChildBorder = Color(60, 160, 180, 255),
            TitleBar = Color(15, 45, 55, 255),
            ButtonNormal = Color(25, 60, 70, 255),
            ButtonHover = Color(35, 80, 95, 255),
            ButtonActive = Color(15, 40, 50, 255),
            SliderBg = Color(5, 25, 30, 255),
            SliderKnob = Color(180, 255, 255, 255),
            SliderFill = Color(100, 255, 255, 255),
            Text = Color(220, 255, 255, 255),
            InputBg = Color(20, 50, 60, 255),
            InputBorder = Color(80, 200, 220, 255),
            ComboBg = Color(25, 55, 65, 255)
        }
    },
    
    
    {
        name = "Golden Sand",
        colors = {
            WindowBg = Color(45, 40, 25, 245),
            WindowBorder = Color(180, 160, 100, 255),
            ChildBg = Color(38, 33, 20, 245),
            ChildBorder = Color(140, 125, 80, 255),
            TitleBar = Color(65, 55, 35, 255),
            ButtonNormal = Color(80, 70, 45, 255),
            ButtonHover = Color(110, 95, 60, 255),
            ButtonActive = Color(60, 50, 30, 255),
            SliderBg = Color(40, 35, 20, 255),
            SliderKnob = Color(255, 240, 180, 255),
            SliderFill = Color(255, 215, 120, 255),
            Text = Color(255, 250, 220, 255),
            InputBg = Color(70, 60, 40, 255),
            InputBorder = Color(180, 160, 100, 255),
            ComboBg = Color(75, 65, 45, 255)
        }
    },
    
    
    {
        name = "Ice Blue",
        colors = {
            WindowBg = Color(25, 35, 45, 245),
            WindowBorder = Color(120, 180, 220, 255),
            ChildBg = Color(20, 28, 38, 245),
            ChildBorder = Color(100, 140, 180, 255),
            TitleBar = Color(35, 50, 65, 255),
            ButtonNormal = Color(50, 70, 90, 255),
            ButtonHover = Color(70, 95, 120, 255),
            ButtonActive = Color(30, 45, 70, 255),
            SliderBg = Color(20, 30, 40, 255),
            SliderKnob = Color(200, 230, 255, 255),
            SliderFill = Color(150, 200, 255, 255),
            Text = Color(240, 250, 255, 255),
            InputBg = Color(40, 60, 80, 255),
            InputBorder = Color(120, 180, 220, 255),
            ComboBg = Color(45, 65, 85, 255)
        }
    },
    
    
    {
        name = "Volcano",
        colors = {
            WindowBg = Color(40, 20, 15, 245),
            WindowBorder = Color(200, 100, 60, 255),
            ChildBg = Color(35, 15, 10, 245),
            ChildBorder = Color(160, 80, 40, 255),
            TitleBar = Color(60, 30, 20, 255),
            ButtonNormal = Color(80, 40, 25, 255),
            ButtonHover = Color(110, 55, 35, 255),
            ButtonActive = Color(60, 25, 15, 255),
            SliderBg = Color(35, 15, 10, 255),
            SliderKnob = Color(255, 200, 150, 255),
            SliderFill = Color(255, 120, 60, 255),
            Text = Color(255, 220, 200, 255),
            InputBg = Color(70, 35, 25, 255),
            InputBorder = Color(200, 100, 60, 255),
            ComboBg = Color(75, 40, 30, 255)
        }
    },
    {
        name = "Old Windows colors",
        colors = {
            WindowBg = Color(192, 192, 192, 245),           
            WindowBorder = Color(128, 128, 128, 255),       
            ChildBg = Color(212, 208, 200, 245),            
            ChildBorder = Color(172, 168, 153, 255),        
            TitleBar = Color(10, 36, 106, 255),             
            ButtonNormal = Color(212, 208, 200, 255),       
            ButtonHover = Color(230, 230, 230, 255),        
            ButtonActive = Color(172, 168, 153, 255),       
            SliderBg = Color(255, 255, 255, 255),           
            SliderKnob = Color(212, 208, 200, 255),         
            SliderFill = Color(10, 36, 106, 255),           
            Text = Color(108, 108, 108, 255),                     
            InputBg = Color(255, 255, 255, 255),            
            InputBorder = Color(128, 128, 128, 255),        
            ComboBg = Color(255, 255, 255, 255)             
            },
        },
    {
        name = "CS 1.6 Classic",
        colors = {
            WindowBg = Color(120, 128, 96, 245),            
            WindowBorder = Color(80, 88, 64, 255),          
            ChildBg = Color(136, 144, 112, 245),            
            ChildBorder = Color(104, 112, 80, 255),         
            TitleBar = Color(88, 96, 72, 255),              
            ButtonNormal = Color(144, 152, 120, 255),       
            ButtonHover = Color(160, 168, 136, 255),        
            ButtonActive = Color(104, 112, 80, 255),        
            SliderBg = Color(104, 112, 80, 255),            
            SliderKnob = Color(176, 184, 152, 255),         
            SliderFill = Color(200, 160, 80, 255),          
            Text = Color(255, 255, 255, 255),               
            InputBg = Color(152, 160, 128, 255),            
            InputBorder = Color(88, 96, 72, 255),           
            ComboBg = Color(152, 160, 128, 255)             
        }
    },
    
    
    {
        name = "Abyss",
        colors = {
            WindowBg = Color(8, 8, 8, 245),
            WindowBorder = Color(25, 25, 25, 255),
            ChildBg = Color(5, 5, 5, 245),
            ChildBorder = Color(20, 20, 20, 255),
            TitleBar = Color(12, 12, 12, 255),
            ButtonNormal = Color(18, 18, 18, 255),
            ButtonHover = Color(30, 30, 30, 255),
            ButtonActive = Color(10, 10, 10, 255),
            SliderBg = Color(3, 3, 3, 255),
            SliderKnob = Color(150, 150, 150, 255),
            SliderFill = Color(80, 80, 80, 255),
            Text = Color(200, 200, 200, 255),
            InputBg = Color(15, 15, 15, 255),
            InputBorder = Color(25, 25, 25, 255),
            ComboBg = Color(18, 18, 18, 255)
        }
    },
    
    
    {
        name = "Shadow",
        colors = {
            WindowBg = Color(12, 8, 8, 245),
            WindowBorder = Color(40, 30, 30, 255),
            ChildBg = Color(8, 5, 5, 245),
            ChildBorder = Color(30, 20, 20, 255),
            TitleBar = Color(18, 12, 12, 255),
            ButtonNormal = Color(25, 18, 18, 255),
            ButtonHover = Color(35, 25, 25, 255),
            ButtonActive = Color(15, 10, 10, 255),
            SliderBg = Color(8, 4, 4, 255),
            SliderKnob = Color(160, 120, 120, 255),
            SliderFill = Color(100, 60, 60, 255),
            Text = Color(220, 180, 180, 255),
            InputBg = Color(20, 15, 15, 255),
            InputBorder = Color(40, 30, 30, 255),
            ComboBg = Color(25, 18, 18, 255)
        }
    },
    
    {
        name = "Obsidian",
        colors = {
            WindowBg = Color(10, 10, 15, 245),
            WindowBorder = Color(35, 35, 50, 255),
            ChildBg = Color(7, 7, 12, 245),
            ChildBorder = Color(25, 25, 35, 255),
            TitleBar = Color(15, 15, 22, 255),
            ButtonNormal = Color(20, 20, 30, 255),
            ButtonHover = Color(30, 30, 45, 255),
            ButtonActive = Color(12, 12, 18, 255),
            SliderBg = Color(5, 5, 10, 255),
            SliderKnob = Color(140, 140, 180, 255),
            SliderFill = Color(70, 70, 120, 255),
            Text = Color(190, 190, 220, 255),
            InputBg = Color(18, 18, 25, 255),
            InputBorder = Color(35, 35, 50, 255),
            ComboBg = Color(20, 20, 30, 255)
        }
    },
    
 
    {
        name = "Fatality",
         colors = {
            WindowBg = Color(25, 30, 45, 245),          
            WindowBorder = Color(60, 70, 100, 255),     
            ChildBg = Color(20, 25, 40, 245),           
            ChildBorder = Color(45, 55, 80, 255),       
            TitleBar = Color(30, 35, 55, 255),          
            ButtonNormal = Color(35, 42, 65, 255),      
            ButtonHover = Color(45, 55, 85, 255),       
            ButtonActive = Color(25, 30, 50, 255),      
            SliderBg = Color(15, 20, 35, 255),          
            SliderKnob = Color(220, 60, 80, 255),       
            SliderFill = Color(200, 50, 70, 255),       
            Text = Color(220, 225, 245, 255),           
            InputBg = Color(30, 38, 60, 255),           
            InputBorder = Color(200, 50, 70, 255),      
            ComboBg = Color(35, 42, 65, 255)            
        }
    },
    { 
        name = "Reworked", 
        colors = { 
            WindowBg = Color(42, 45, 48, 245), 
            WindowBorder = Color(85, 90, 95, 255), 
            ChildBg = Color(38, 41, 44, 245), 
            ChildBorder = Color(75, 80, 85, 255), 
            TitleBar = Color(55, 58, 62, 255), 
            ButtonNormal = Color(58, 62, 68, 255), 
            ButtonHover = Color(68, 72, 78, 255), 
            ButtonActive = Color(48, 52, 58, 255), 
            SliderBg = Color(35, 38, 42, 255), 
            SliderKnob = Color(180, 185, 190, 255), 
            SliderFill = Color(65, 105, 180, 255), 
            Text = Color(240, 242, 245, 255), 
            InputBg = Color(45, 48, 52, 255), 
            InputBorder = Color(90, 95, 100, 255), 
            ComboBg = Color(52, 55, 60, 255) 
        } 
    }
}


local current_theme = 17

UI.Colors = {}
for key, value in pairs(themes[current_theme].colors) do
    UI.Colors[key] = value
end

function UI.RemoveBorders()
    local c = UI.Colors
    c.ChildBorder = c.ChildBg
    c.InputBorder = c.InputBg
    c.WindowBorder = c.WindowBg
    

end 


function UI.ChangeTheme(theme_index)

    if theme_index < 1 or theme_index > #themes then
        print("Invalid theme index: " .. tostring(theme_index) .. ". Available themes: 1-" .. #themes)
        return false
    end
    
    current_theme = theme_index
    local selected_theme = themes[theme_index]
    

    for key, value in pairs(selected_theme.colors) do
        UI.Colors[key] = value
    end
    
    print("Theme changed to: " .. selected_theme.name)
    return true
end


function UI.GetCurrentTheme()
    return {
        index = current_theme,
        name = themes[current_theme].name
    }
end


function UI.GetAvailableThemes()
    local theme_list = {}
    for i, theme in ipairs(themes) do
        table.insert(theme_list, {
            index = i,
            name = theme.name
        })
    end
    return theme_list
end


function UI.ApplyCurrentTheme()

    local selected_theme = themes[current_theme]
    for key, value in pairs(selected_theme.colors) do
        UI.Colors[key] = value
    end
end

UI.ApplyCurrentTheme()


UI.state = {}
UI.windows = {}
UI.currWin = nil
UI.dragWin = nil
UI.dragOff = Vector2D(0, 0)
UI.lastMouse = Vector2D(0, 0)
UI.mouseDown = false
UI.rightMouseDown = false

UI.lastRightMouseDown = false

UI.lastMouseDown = false
UI.focusedInput = nil
UI.keyStates = {}
UI.openCombo = nil
UI.scrollOffsets = {} 
UI.contentHeights = {} 
UI.TextOutLine = false
UI.keybinds = UI.keybinds or {}

function UI.InitKeybind(id, keybindType)
    if not UI.keybinds[id] then
        UI.keybinds[id] = {
            key = nil,
            type = keybindType or "toggle", 
            enabled = false, 
            waitingForKey = false,
            showTypeDropdown = false
        }
    end
end

function UI.HandleKeybinds()
    for id, keybind in pairs(UI.keybinds) do
        if keybind.key then
            if keybind.type == "hold" then
                
                if Input.GetKeyDown(keybind.key) then
                    keybind.enabled = true
                else
                    if keybind.enabled then
                        keybind.enabled = false
                    end
                end
            elseif keybind.type == "toggle" then
                
                if Input.GetKeyDown(keybind.key) and not keybind.was_pressed then
                    keybind.enabled = not keybind.enabled
                    keybind.was_pressed = true
                elseif not Input.GetKeyDown(keybind.key) then
                    keybind.was_pressed = false
                end
            end
        end
    end
end


function UI.SetKeybind(id, key, keybindType, enabled)
    if not UI.keybinds[id] then
        UI.InitKeybind(id, keybindType or "toggle")
    end
    
    if key ~= nil then UI.keybinds[id].key = key end
    if keybindType ~= nil then UI.keybinds[id].type = keybindType end
    if enabled ~= nil then UI.keybinds[id].enabled = enabled end
end

function UI.GetKeybindEnabled(id)
    if not UI.keybinds[id] then return false end
    return UI.keybinds[id].enabled 
end



function UI.GetKeybindKey(id)
    if not UI.keybinds[id] then return nil end
    return UI.keybinds[id].key
end

function UI.SetKeybindKey(id, key)
    if not UI.keybinds[id] then return end
    UI.keybinds[id].key = key
end

function UI.GetKeybindType(id)
    if not UI.keybinds[id] then return "toggle" end
    return UI.keybinds[id].type
end

function UI.SetKeybindType(id, keybindType)
    if not UI.keybinds[id] then return end
    UI.keybinds[id].type = keybindType
end

function UI.IsPointInRect(p, r) 
    return p and r and p.x >= r.x1 and p.x <= r.x2 and p.y >= r.y1 and p.y <= r.y2 
end

function UI.BringToFront(w)
    for i, win in ipairs(UI.windows) do 
        if win == w then 
            table.remove(UI.windows, i)
            break 
        end 
    end
    table.insert(UI.windows, w)
end

function UI.GetValue(id)
    return UI.state[id] and UI.state[id].value or nil
end

function UI.SetValue(id, val)
    if not UI.state[id] then UI.state[id] = {} end
    UI.state[id].value = val
    UI.state[id].changed = true
end

function UI.IsPressed(id)
    return UI.state[id] and UI.state[id].pressed or false
end

function UI.IsHovered(id)
    return UI.state[id] and UI.state[id].hovered or false
end

function UI.HasChanged(id)
    if UI.state[id] and UI.state[id].changed then
        UI.state[id].changed = false
        return true
    end
    return false
end

function UI.IsElementVisible(elementY, elementHeight, window)
    local scrollOffset = UI.scrollOffsets[window.id] or 0
    

    local windowTop = 5 
    local windowBottom = window.size.y - 5 

    local elementTop = elementY
    local elementBottom = elementTop + elementHeight
 
    local topInBounds = elementTop >= windowTop
    local bottomInBounds = elementBottom <= windowBottom
    local isVisible = topInBounds and bottomInBounds
    
   
    return isVisible
end


function UI.HandleMousePress(mp)
    if UI.openCombo then
        local dr = {
            x1 = UI.openCombo.rect.x1, y1 = UI.openCombo.rect.y2,
            x2 = UI.openCombo.rect.x2, y2 = UI.openCombo.rect.y2 + (#UI.openCombo.items * 25)
        }
        if UI.IsPointInRect(mp, dr) then
            local idx = math.floor((mp.y - dr.y1) / 25) + 1
            if idx >= 1 and idx <= #UI.openCombo.items then 
                UI.SetValue(UI.openCombo.id, idx)
                UI.openCombo = nil
                return 
            end
        end
    end
    
    for i = #UI.windows, 1, -1 do
        local w = UI.windows[i]
        if w.visible then
            local tr = {x1 = w.pos.x, y1 = w.pos.y, x2 = w.pos.x + w.size.x, y2 = w.pos.y + 25}
            
            if UI.IsPointInRect(mp, tr) then 
                UI.dragWin = w
                UI.dragOff = Vector2D(mp.x - w.pos.x, mp.y - w.pos.y)
                UI.BringToFront(w)
                break 
            end
        end
    end
end

function UI.HandleMouseDrag(mp)
    if UI.dragWin then
        UI.dragWin.pos.x = mp.x - UI.dragOff.x
        UI.dragWin.pos.y = mp.y - UI.dragOff.y
        local ss = Renderer.GetScreenSize()
        if ss then
            UI.dragWin.pos.x = math.max(0, math.min(UI.dragWin.pos.x, ss.x - UI.dragWin.size.x))
            UI.dragWin.pos.y = math.max(0, math.min(UI.dragWin.pos.y, ss.y - UI.dragWin.size.y))
        end
    end
end


function UI.GetKeyInput() 
   local keyMap = {
            [0x08] = "BACKSPACE", [0x11] = "CTRL", [0x10] = "SHIFT", [0x0D] = "ENTER", 
            [0x20] = "SPACE", [0x2E] = "DELETE", [0x1B] = "ESCAPE",
            [0x30] = "0", [0x31] = "1", [0x32] = "2", [0x33] = "3", [0x34] = "4",
            [0x35] = "5", [0x36] = "6", [0x37] = "7", [0x38] = "8", [0x39] = "9",
            [0x41] = "A", [0x42] = "B", [0x43] = "C", [0x44] = "D", [0x45] = "E",
            [0x46] = "F", [0x47] = "G", [0x48] = "H", [0x49] = "I", [0x4A] = "J",
            [0x4B] = "K", [0x4C] = "L", [0x4D] = "M", [0x4E] = "N", [0x4F] = "O",
            [0x50] = "P", [0x51] = "Q", [0x52] = "R", [0x53] = "S", [0x54] = "T",
            [0x55] = "U", [0x56] = "V", [0x57] = "W", [0x58] = "X", [0x59] = "Y",
            [0x5A] = "Z",
            [0xBA] = ";", [0xBB] = "=", [0xBC] = ",", [0xBD] = "-", [0xBE] = ".", [0xBF] = "/",
            [0xC0] = "`", [0xDB] = "[", [0xDC] = "\\", [0xDD] = "]", [0xDE] = "'", [0x04] = "MOUSE3", [0x05] = "MOUSE4", [0x06] = "MOUSE5"
        }
   
   if Input.GetKeyDown(0x1B) then
       return 0, ""
   end
   
   for key_code, key_name in pairs(keyMap) do
       if Input.GetKeyDown(key_code) then 
           return key_code, key_name 
       end
   end
   
   return nil, nil
end

function UI.HandleKeyboardInput()
    if not UI.focusedInput then return end
    
    local shift = Input.GetKeyDown(0x10)
    local ctrl = Input.GetKeyDown(0x11)
    if ctrl then return end
    
    local keyMap = {
            [0x08] = "BACKSPACE", [0x11] = "CTRL", [0x10] = "SHIFT", [0x0D] = "ENTER", 
            [0x20] = "SPACE", [0x2E] = "DELETE", [0x1B] = "ESCAPE",
            [0x30] = "0", [0x31] = "1", [0x32] = "2", [0x33] = "3", [0x34] = "4",
            [0x35] = "5", [0x36] = "6", [0x37] = "7", [0x38] = "8", [0x39] = "9",
            [0x41] = "A", [0x42] = "B", [0x43] = "C", [0x44] = "D", [0x45] = "E",
            [0x46] = "F", [0x47] = "G", [0x48] = "H", [0x49] = "I", [0x4A] = "J",
            [0x4B] = "K", [0x4C] = "L", [0x4D] = "M", [0x4E] = "N", [0x4F] = "O",
            [0x50] = "P", [0x51] = "Q", [0x52] = "R", [0x53] = "S", [0x54] = "T",
            [0x55] = "U", [0x56] = "V", [0x57] = "W", [0x58] = "X", [0x59] = "Y",
            [0x5A] = "Z",
            [0xBA] = ";", [0xBB] = "=", [0xBC] = ",", [0xBD] = "-", [0xBE] = ".", [0xBF] = "/",
            [0xC0] = "`", [0xDB] = "[", [0xDC] = "\\", [0xDD] = "]", [0xDE] = "'", [0x04] = "MOUSE3", [0x05] = "MOUSE4", [0x06] = "MOUSE5"
        }
    
    for kc, ch in pairs(keyMap) do
        local down = Input.GetKeyDown(kc)
        local wasDown = UI.keyStates[kc] or false
        if down and not wasDown then
            local txt = UI.state[UI.focusedInput].value or ""
            if ch == "BACKSPACE" then
                if #txt > 0 then 
                    UI.SetValue(UI.focusedInput, string.sub(txt, 1, -2))
                end
            elseif ch == "DELETE" then
                if #txt > 0 then 
                    UI.SetValue(UI.focusedInput, string.sub(txt, 1, -2))
                end
            elseif ch == "SPACE" then
                UI.SetValue(UI.focusedInput, txt .. " ")
            elseif ch == "ESCAPE" then
                
            elseif ch == "ENTER" then 
                UI.focusedInput = nil
            else 
                local fc = ch
                if string.match(ch, "[A-Z]") then
                    fc = shift and ch or string.lower(ch)
                elseif ch >= "0" and ch <= "9" and shift then
                    local sn = {["0"] = ")", ["1"] = "!", ["2"] = "@", ["3"] = "#", ["4"] = "$",
                               ["5"] = "%", ["6"] = "^", ["7"] = "&", ["8"] = "*", ["9"] = "("}
                    fc = sn[ch] or ch
                elseif shift then
                    local sc = {[";"] = ":", ["="] = "+", [","] = "<", ["-"] = "_", ["."] = ">", 
                               ["/"] = "?", ["`"] = "~", ["["] = "{", ["\\"] = "|", ["]"] = "}", ["'"] = "\""}
                    fc = sc[ch] or ch
                end
                UI.SetValue(UI.focusedInput, txt .. fc)
            end
        end
        UI.keyStates[kc] = down
    end
end

function UI.CreateWindow(title, pos, size, disableScroll) 
    local w = {
        title = title, 
        pos = pos, 
        size = size, 
        visible = true, 
        nextY = 35,
        nextX = 0,  
        id = title .. "_" .. tostring(os.time()),
        disablescroll = disableScroll
    }
    table.insert(UI.windows, w)
    UI.scrollOffsets[w.id] = 0
    UI.contentHeights[w.id] = 0
    return w 
end

function UI.HandleScrollInput()
    
end

function UI.UpdateChildContentHeight(elementHeight)
    if UI.currWin and UI.currWin.parent then
        
        UI.currWin.nextY = UI.currWin.nextY + elementHeight
        UI.contentHeights[UI.currWin.id] = math.max(UI.contentHeights[UI.currWin.id], UI.currWin.nextY)
    end
end


function UI.BeginWindow(w)
    if not w.visible then return false end
    UI.currWin = w
    w.nextY = 35
    w.nextX = 0
    
    
    UI.contentHeights[w.id] = 35
    
    
    Renderer.DrawRectFilled(w.pos, Vector2D(w.pos.x + w.size.x, w.pos.y + w.size.y), UI.Colors.WindowBg, 5)
    Renderer.DrawRect(w.pos, Vector2D(w.pos.x + w.size.x, w.pos.y + w.size.y), UI.Colors.WindowBorder, 5)
    
    
    local te = Vector2D(w.pos.x + w.size.x, w.pos.y + 25)
    Renderer.DrawRectFilled(w.pos, te, UI.Colors.TitleBar, 5)
    Renderer.DrawText('font', w.title, Vector2D(w.pos.x + 10, w.pos.y + 5), false, UI.TextOutLine, UI.Colors.Text)
    
    
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local maxScroll = math.max(0, UI.contentHeights[w.id] - (w.size.y - 60))
    
    
   

    
    if maxScroll > 0 and not w.disablescroll then
        local scrollbarX = w.pos.x + w.size.x - 15
        local scrollbarTop = w.pos.y + 35
        local scrollbarHeight = w.size.y - 45
        local scrollbarBottom = scrollbarTop + scrollbarHeight
        
        
        Renderer.DrawRectFilled(
            Vector2D(scrollbarX, scrollbarTop), 
            Vector2D(scrollbarX + 12, scrollbarBottom), 
            UI.Colors.SliderBg, 2
        )
        
        
        Renderer.DrawRect(
            Vector2D(scrollbarX, scrollbarTop), 
            Vector2D(scrollbarX + 12, scrollbarBottom), 
            UI.Colors.WindowBorder, 2
        )
        
        
        local thumbHeight = math.max(30, (scrollbarHeight * (w.size.y - 60)) / UI.contentHeights[w.id])
        local thumbPos = scrollbarTop + (scrollOffset / maxScroll) * (scrollbarHeight - thumbHeight)
        
        Renderer.DrawRectFilled(
            Vector2D(scrollbarX + 2, thumbPos), 
            Vector2D(scrollbarX + 10, thumbPos + thumbHeight), 
            UI.Colors.SliderKnob, 3
        )
        
        
        Renderer.DrawRect(
            Vector2D(scrollbarX + 2, thumbPos), 
            Vector2D(scrollbarX + 10, thumbPos + thumbHeight), 
            Color(255, 255, 255, 150), 3
        )
    end
    
    return true
end

UI.renderQueue = {}
UI.RENDER_PRIORITY = {
    BACKGROUND = 0,     
    NORMAL = 1,         
    INPUT = 2,          
    COMBO_CLOSED = 3,   
    COMBO_DROPDOWN = 10 
}


function UI.QueueRender(priority, renderFunc)
    if not UI.renderQueue[priority] then
        UI.renderQueue[priority] = {}
    end
    table.insert(UI.renderQueue[priority], renderFunc)
end


function UI.ExecuteRenderQueue()
    
    local priorities = {}
    for priority, _ in pairs(UI.renderQueue) do
        table.insert(priorities, priority)
    end
    table.sort(priorities)
    
    
    for _, priority in ipairs(priorities) do
        for _, renderFunc in ipairs(UI.renderQueue[priority]) do
            renderFunc()
        end
    end
    
    
    UI.renderQueue = {}
end

function UI.HandleChildScrollInput()
    
end



function UI.EndWindow() 
    UI.currWin = nil 
end

function UI.EndChild()
    if not UI.currWin or not UI.currWin.parent then return end
    
    local child = UI.currWin
    local parent = child.parent
    
    
    parent.nextY = parent.nextY + child.size.y + 10
    UI.contentHeights[parent.id] = math.max(UI.contentHeights[parent.id], parent.nextY)
    
    
    UI.currWin = parent
end

function UI.Update()
    local mp = Input.GetCursorPos()
    if not mp then 
        return 
    end
    
    local prevMouseDown = UI.mouseDown
    local prevRightMouseDown = UI.rightMouseDown
    UI.mouseDown = Input.GetKeyDown(1)
    UI.rightMouseDown = Input.GetKeyDown(2)  
    
    
    for id, s in pairs(UI.state) do
        s.pressed = false
        s.hovered = false
    end
    
    
    if UI.mouseDown and not prevMouseDown and UI.openCombo then
        local cr = UI.openCombo.rect
        local dr = {
            x1 = cr.x1, y1 = cr.y2, 
            x2 = cr.x2, y2 = cr.y2 + (#UI.openCombo.items * 25)
        }
        if not UI.IsPointInRect(mp, cr) and not UI.IsPointInRect(mp, dr) then 
            UI.openCombo = nil 
        end
    end
    
    
    if UI.mouseDown and not prevMouseDown then 
        UI.HandleMousePress(mp)
    elseif not UI.mouseDown and prevMouseDown then 
        UI.dragWin = nil
    elseif UI.mouseDown and UI.dragWin then 
        UI.HandleMouseDrag(mp) 
    end
    
    UI.HandleKeyboardInput()
    UI.HandleScrollInput()
    UI.lastMouse = mp
    UI.lastMouseDown = prevMouseDown
    UI.lastRightMouseDown = prevRightMouseDown 
    
    
    UI.ExecuteRenderQueue()
end
UI.childWindows = {}
UI.dragState = {
    scrolling = nil  
}



function UI.BeginChild(id, size, border)
    if not UI.currWin then return false end
    
    local parent = UI.currWin
    local scrollOffset = UI.scrollOffsets[parent.id] or 0
    local elementHeight = size.y
    
    if not UI.IsElementVisible(parent.nextY - scrollOffset, elementHeight, parent) then
        parent.nextY = parent.nextY + elementHeight + 10
        UI.contentHeights[parent.id] = math.max(UI.contentHeights[parent.id], parent.nextY)
        return false
    end
    
    
    if not parent.nextX then
        parent.nextX = 0
    end
    
    local childId = parent.id .. "_child_" .. id
    local pos = Vector2D(parent.pos.x + 10 + parent.nextX, parent.pos.y + parent.nextY - scrollOffset)
    
    
    if not UI.childWindows[childId] then
        UI.childWindows[childId] = {
            id = childId,
            pos = pos,
            size = size,
            nextY = 5,
            nextX = 5,
            parent = parent
        }
        UI.scrollOffsets[childId] = 0
        UI.contentHeights[childId] = 5
    end
    
    local child = UI.childWindows[childId]
    child.pos = pos
    child.size = size 
    
    
    
    local savedContentHeight = UI.contentHeights[childId] or 5
    child.nextY = 5
    child.nextX = 5
    
    
    UI.currWin = child
    
    
    UI.contentHeights[childId] = savedContentHeight
    
    
    Renderer.DrawRectFilled(pos, Vector2D(pos.x + size.x, pos.y + size.y), UI.Colors.ChildBg, 6)
    
    if border then
        Renderer.DrawRect(pos, Vector2D(pos.x + size.x, pos.y + size.y), UI.Colors.ChildBorder, 6)
    end
    
    
    local childScrollOffset = UI.scrollOffsets[childId] or 0
    local childContentHeight = UI.contentHeights[childId] or 5
    local visibleHeight = size.y - 10 
    local maxScroll = math.max(0, childContentHeight - visibleHeight)
    
    
    if maxScroll > 0 then
        local scrollbarX = pos.x + size.x - 14
        local scrollbarTop = pos.y + 6
        local scrollbarHeight = size.y - 12
        local scrollbarBottom = scrollbarTop + scrollbarHeight
        
        
        Renderer.DrawRectFilled(
            Vector2D(scrollbarX, scrollbarTop), 
            Vector2D(scrollbarX + 10, scrollbarBottom), 
            UI.Colors.SliderBg, 6
        )
        
        
        local thumbHeight = math.max(20, (scrollbarHeight * visibleHeight) / childContentHeight)
        local thumbPos = scrollbarTop + (childScrollOffset / maxScroll) * (scrollbarHeight - thumbHeight)
        
        
        local mousePos = Input.GetCursorPos()
        
        
        local scrollbarRect = {
            x1 = scrollbarX,
            y1 = scrollbarTop,
            x2 = scrollbarX + 10,
            y2 = scrollbarBottom
        }

        local overScrollbar = UI.IsPointInRect(mousePos, scrollbarRect)
        local overThumb = mousePos.y >= thumbPos and mousePos.y <= thumbPos + thumbHeight and overScrollbar
        
        
        if not UI.dragState then
            UI.dragState = {}
        end
        
        
        if UI.mouseDown then
            
            if (overThumb or UI.dragState.scrolling == childId) and not UI.dragState.scrolling then
                UI.dragState.scrolling = childId
                
                UI.dragState.dragOffset = mousePos.y - thumbPos
            elseif UI.dragState.scrolling == childId then
                
                local relativeY = mousePos.y - scrollbarTop - UI.dragState.dragOffset
                local scrollPercent = math.max(0, math.min(1, relativeY / (scrollbarHeight - thumbHeight)))
                UI.scrollOffsets[childId] = scrollPercent * maxScroll
            elseif overScrollbar and not overThumb and not UI.dragState.scrolling then
                
                local relativeY = mousePos.y - scrollbarTop
                local scrollPercent = math.max(0, math.min(1, (relativeY - thumbHeight/2) / (scrollbarHeight - thumbHeight)))
                UI.scrollOffsets[childId] = scrollPercent * maxScroll
            end
        else
            
            if UI.dragState.scrolling == childId then
                UI.dragState.scrolling = nil
                UI.dragState.dragOffset = nil
            end
        end
        
        
        childScrollOffset = UI.scrollOffsets[childId] or 0
        thumbPos = scrollbarTop + (childScrollOffset / maxScroll) * (scrollbarHeight - thumbHeight)
        
        
        local isDragging = UI.dragState.scrolling == childId
        local thumbColor = isDragging and UI.Colors.SliderFill or (overScrollbar and UI.Colors.ButtonHover or UI.Colors.SliderKnob)
        Renderer.DrawRectFilled(
            Vector2D(scrollbarX + 1, thumbPos), 
            Vector2D(scrollbarX + 9, thumbPos + thumbHeight), 
            thumbColor, 6
        )
        
        
        if childScrollOffset > 0 and not UI.dragState.scrolling then
            local arrowPos = Vector2D(scrollbarX + 5, scrollbarTop + 3)
            local overUpArrow = mousePos.x >= scrollbarX and mousePos.x <= scrollbarX + 10 and
                               mousePos.y >= scrollbarTop and mousePos.y <= scrollbarTop + 15
            
            if overUpArrow and UI.mouseDown then
                UI.scrollOffsets[childId] = math.max(0, childScrollOffset - 20)
            end
            
            local arrowColor = overUpArrow and UI.Colors.SliderFill or UI.Colors.Text
            Renderer.DrawText('font', "^", arrowPos, true, UI.TextOutLine, arrowColor)
        end
        
        
        if childScrollOffset < maxScroll and not UI.dragState.scrolling then
            local arrowPos = Vector2D(scrollbarX + 5, scrollbarBottom - 12)
            local overDownArrow = mousePos.x >= scrollbarX and mousePos.x <= scrollbarX + 10 and
                                 mousePos.y >= scrollbarBottom - 15 and mousePos.y <= scrollbarBottom
            
            if overDownArrow and UI.mouseDown then
                UI.scrollOffsets[childId] = math.min(maxScroll, childScrollOffset + 20)
            end
            
            local arrowColor = overDownArrow and UI.Colors.SliderFill or UI.Colors.Text
            Renderer.DrawText('font', "v", arrowPos, true, UI.TextOutLine, arrowColor)
        end
    end
    
    return true
end

function UI.Button(id, text, size)
    if not UI.currWin then return id end
    if not UI.state[id] then UI.state[id] = {value = false} end
    
    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local elementHeight = (size and size.y) or 25
    
    
    w.nextX = w.nextX or 0
    
    
    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight + 5
        w.nextX = 0  
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return id
    end
    
    local p = Vector2D(w.pos.x + 10 + w.nextX, w.pos.y + w.nextY - scrollOffset)
    local s = size or Vector2D(100, 25)
    local r = {x1 = p.x, y1 = p.y, x2 = p.x + s.x, y2 = p.y + s.y}
    
    
    if w.nextX + s.x + 10 > w.size.x - 20 then  
        w.nextY = w.nextY + s.y + 5
       
        p = Vector2D(w.pos.x + w.nextX, w.pos.y + w.nextY - scrollOffset)
        r = {x1 = p.x, y1 = p.y, x2 = p.x + s.x, y2 = p.y + s.y}
    end
    
    local mp = Input.GetCursorPos()
    local hov = mp and UI.IsPointInRect(mp, r)
    UI.state[id].hovered = hov or false
    
    local press = hov and UI.mouseDown and not UI.lastMouseDown
    if press then
        UI.state[id].pressed = true
        UI.state[id].value = true
        UI.state[id].changed = true
    else
        UI.state[id].value = false
    end
    
    
    local col = UI.mouseDown and hov and UI.Colors.ButtonActive or (hov and UI.Colors.ButtonHover or UI.Colors.ButtonNormal)
    Renderer.DrawRectFilled(p, Vector2D(p.x + s.x, p.y + s.y), col, 6)
    
    
    Renderer.DrawRect(p, Vector2D(p.x + s.x, p.y + s.y), UI.Colors.InputBorder, 6)
    
    
    Renderer.DrawText('font', text, Vector2D(p.x + s.x/2, p.y + s.y/2 - 7), true, UI.TextOutLine, UI.Colors.Text)
    
  
    w.nextY = w.nextY + s.y + 5  

    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY + s.y)
    
    return id
end

function UI.Icon(id, texture, icon_size)
    if not UI.currWin then return id end
    
    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local elementHeight = (icon_size and icon_size.y) or 25
    
    
    w.nextX = w.nextX or 0
    
    
    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight + 5
        w.nextX = 0  
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return id
    end
    
    local p = Vector2D(w.pos.x + 10 + w.nextX, w.pos.y + w.nextY - scrollOffset)
    local s = icon_size or Vector2D(25, 25)  
    local r = {x1 = p.x, y1 = p.y, x2 = p.x + s.x, y2 = p.y + s.y}
    
    
    if w.nextX + s.x + 10 > w.size.x - 20 then  
        w.nextY = w.nextY + s.y + 5
        w.nextX = 0
        p = Vector2D(w.pos.x + 10, w.pos.y + w.nextY - scrollOffset)
        r = {x1 = p.x, y1 = p.y, x2 = p.x + s.x, y2 = p.y + s.y}
    end
    
    
    if texture then
        Renderer.DrawTexture(texture, p, s)
    end
    
    
    w.nextX = w.nextX + s.x + 5  
    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY + s.y)
    
    return id
end

function UI.IconButton(id, texture, icon_size, size, padding)
    if not UI.currWin then return id end
    if not UI.state[id] then UI.state[id] = {value = false} end
    
    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local elementHeight = (size and size.y) or 25
    local iconPadding = padding or 5  
    
    
    w.nextX = w.nextX or 0
    
    
    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight + 5
        w.nextX = 0  
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return id
    end
    
    local p = Vector2D(w.pos.x + 10 + w.nextX, w.pos.y + w.nextY - scrollOffset)
    local s = size or Vector2D(100, 25)
    local r = {x1 = p.x, y1 = p.y, x2 = p.x + s.x, y2 = p.y + s.y}
    
    
    if w.nextX + s.x + 10 > w.size.x - 20 then  
        w.nextY = w.nextY + s.y + 5
   
        p = Vector2D(w.pos.x + w.nextX, w.pos.y + w.nextY - scrollOffset)
        r = {x1 = p.x, y1 = p.y, x2 = p.x + s.x, y2 = p.y + s.y}
    end
    
    local mp = Input.GetCursorPos()
    local hov = mp and UI.IsPointInRect(mp, r)
    UI.state[id].hovered = hov or false
    
    local press = hov and UI.mouseDown and not UI.lastMouseDown
    if press then
        UI.state[id].pressed = true
        UI.state[id].value = true
        UI.state[id].changed = true
    else
        UI.state[id].value = false
    end
    
    
    local col = UI.mouseDown and hov and UI.Colors.ButtonActive or (hov and UI.Colors.ButtonHover or UI.Colors.ButtonNormal)
    Renderer.DrawRectFilled(p, Vector2D(p.x + s.x, p.y + s.y), col, 6)
    
    
    Renderer.DrawRect(p, Vector2D(p.x + s.x, p.y + s.y), UI.Colors.InputBorder, 6)
    
    
    local iconPos = Vector2D(
        p.x + iconPadding,
        p.y + iconPadding
    )
    
    
    local maxIconSize = Vector2D(
        s.x - (iconPadding * 2),
        s.y - (iconPadding * 2)
    )
    
    
    local finalIconSize = icon_size or maxIconSize
    
    
    if finalIconSize.x > maxIconSize.x or finalIconSize.y > maxIconSize.y then
        local scaleX = maxIconSize.x / finalIconSize.x
        local scaleY = maxIconSize.y / finalIconSize.y
        local scale = math.min(scaleX, scaleY)
        finalIconSize = Vector2D(finalIconSize.x * scale, finalIconSize.y * scale)
    end
    
    
    iconPos = Vector2D(
        p.x + (s.x - finalIconSize.x) / 2,
        p.y + (s.y - finalIconSize.y) / 2
    )
    
    
    if texture then
        Renderer.DrawTexture(texture, iconPos, finalIconSize)
    end
    
    
    w.nextX = w.nextX + s.x + 5  
    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY + s.y)
    
    return id
end

function UI.Slider(id, label, def, min, max, size, isFloat)
    if not UI.currWin then return id end
    if not UI.state[id] then UI.state[id] = {value = def or 0} end
    
    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local elementHeight = 55
    

    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return id
    end
    
    local p = Vector2D(w.pos.x + 10 + (w.nextX or 0), w.pos.y + w.nextY - scrollOffset)
    local s = size or Vector2D(180, 22)
    
    
    Renderer.DrawText('font', label, p, false, UI.TextOutLine, UI.Colors.Text)
    
    local sp = Vector2D(p.x, p.y + 25)
    local r = {x1 = sp.x, y1 = sp.y, x2 = sp.x + s.x, y2 = sp.y + s.y}
    local mp = Input.GetCursorPos()
    local hov = mp and UI.IsPointInRect(mp, r)
    
    UI.state[id].hovered = hov or false
    
    if hov and UI.mouseDown then
        local rx = mp.x - sp.x
        local pct = math.max(0, math.min(1, rx / s.x))
        local nv = (min or 0) + ((max or 100) - (min or 0)) * pct
   
        if not isFloat then
            nv = math.floor(nv + 0.5) 
        end
        
        if UI.state[id].value ~= nv then
            UI.state[id].value = nv
            UI.state[id].changed = true
        end
    end
    
    local cv = UI.state[id].value or 0
    
    
    Renderer.DrawRectFilled(sp, Vector2D(sp.x + s.x, sp.y + s.y), UI.Colors.SliderBg, 4)
    
    
    local fw = ((cv - (min or 0)) / ((max or 100) - (min or 0))) * s.x
    if fw > 0 then 
        Renderer.DrawRectFilled(sp, Vector2D(sp.x + fw, sp.y + s.y), UI.Colors.SliderFill, 4) 
    end
    
    
    Renderer.DrawRect(sp, Vector2D(sp.x + s.x, sp.y + s.y), UI.Colors.InputBorder, 4)
    
    
    if fw > 0 then
        local handleX = sp.x + fw - 3
        local handleY = sp.y - 2
        local handleSize = Vector2D(6, s.y + 4)
        
        
        Renderer.DrawRectFilled(
            Vector2D(handleX, handleY), 
            Vector2D(handleX + handleSize.x, handleY + handleSize.y), 
            UI.Colors.SliderKnob, 3
        )
        
        
        Renderer.DrawRect(
            Vector2D(handleX, handleY), 
            Vector2D(handleX + handleSize.x, handleY + handleSize.y), 
            UI.Colors.InputBorder, 3
        )
    end

    
    local displayText
    if isFloat then
        displayText = string.format("%.2f", cv)
    else
        displayText = string.format("%d", cv)
    end
    
    
    local valueWidth = 50
    local valueBg = Vector2D(sp.x + s.x + 15, sp.y - 2)
    Renderer.DrawRectFilled(valueBg, Vector2D(valueBg.x + valueWidth, valueBg.y + 26), UI.Colors.InputBg, 4)
    Renderer.DrawRect(valueBg, Vector2D(valueBg.x + valueWidth, valueBg.y + 26), UI.Colors.InputBorder, 4)
    
    Renderer.DrawText('font', displayText, Vector2D(valueBg.x + valueWidth/2, valueBg.y + 6), true, UI.TextOutLine, UI.Colors.Text)
    
    w.nextY = w.nextY + 55
    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
    return id
end

function UI.Dummy(size)
    if not UI.currWin then return end
    local w = UI.currWin
    local s = size or Vector2D(0, 0)
    w.nextY = w.nextY + s.y
end

function UI.ComboBox(id, label, items, def, size)
    if not UI.currWin then return id end
    if not UI.state[id] then UI.state[id] = {value = def or 1} end

    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local elementHeight = 50

    
    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return id
    end

    local p = Vector2D(w.pos.x + 10 + (w.nextX or 0), w.pos.y + w.nextY - scrollOffset)
    local s = size or Vector2D(150, 25)

    
    UI.QueueRender(UI.RENDER_PRIORITY.NORMAL, function()
        Renderer.DrawText('font', label, p, false, UI.TextOutLine, UI.Colors.Text)
    end)

    local cp = Vector2D(p.x, p.y + 20)
    local r = {x1 = cp.x, y1 = cp.y, x2 = cp.x + s.x, y2 = cp.y + s.y}
    local mp = Input.GetCursorPos()
    local hov = mp and UI.IsPointInRect(mp, r)
    local click = hov and UI.mouseDown and not UI.lastMouseDown

    UI.state[id].hovered = hov or false

    if click then
        if UI.openCombo and UI.openCombo.id == id then 
            UI.openCombo = nil
        else 
            UI.openCombo = {id = id, items = items, rect = r} 
        end
    end

    local cv = UI.state[id].value or 1
    local open = UI.openCombo and UI.openCombo.id == id

    

    UI.QueueRender(UI.RENDER_PRIORITY.COMBO_CLOSED, function()
        local col = hov and UI.Colors.ButtonHover or UI.Colors.ComboBg
        Renderer.DrawRectFilled(cp, Vector2D(cp.x + s.x, cp.y + s.y), col, 6)
        Renderer.DrawRect(cp, Vector2D(cp.x + s.x, cp.y + s.y), UI.Colors.InputBorder, 6)

        local st = (items and items[cv]) and items[cv] or "None"
        Renderer.DrawText('font', st, Vector2D(cp.x + 8, cp.y + 6), false, UI.TextOutLine, UI.Colors.Text)

        
        local arrowColor = hov and UI.Colors.SliderFill or UI.Colors.Text
        local arrowX = cp.x + s.x - 15
        local arrowY = cp.y + s.y / 2

        local arrowChar = open and "^" or "v"
        Renderer.DrawText('font', arrowChar, Vector2D(arrowX - 2, arrowY - 6), false, UI.TextOutLine, arrowColor)
    end)

    
    if open and items then
        local dropdownHeight = #items * 25
        local dp = Vector2D(cp.x, cp.y + s.y)

        UI.QueueRender(UI.RENDER_PRIORITY.COMBO_DROPDOWN, function()
            
            Renderer.DrawRectFilled(dp, Vector2D(dp.x + s.x, dp.y + dropdownHeight), UI.Colors.ComboBg, 6)
            Renderer.DrawRect(dp, Vector2D(dp.x + s.x, dp.y + dropdownHeight), UI.Colors.InputBorder, 6)

            
            for i, item in ipairs(items) do
                local ip = Vector2D(dp.x, dp.y + (i-1) * 25)
                local ir = {x1 = ip.x, y1 = ip.y, x2 = ip.x + s.x, y2 = ip.y + 25}
                local ih = mp and UI.IsPointInRect(mp, ir)
                if ih then 
                    Renderer.DrawRectFilled(ip, Vector2D(ip.x + s.x, ip.y + 25), UI.Colors.ButtonHover, 0) 
                end
                Renderer.DrawText('font', item, Vector2D(ip.x + 8, ip.y + 6), false, UI.TextOutLine, UI.Colors.Text)
            end
        end)
    end

    w.nextY = w.nextY + 50
    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
    return id
end

function UI.List(id, label, items, def, size)
    if not UI.currWin then return id end
    if not UI.state[id] then UI.state[id] = {value = def or 1} end
    
    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local s = size or Vector2D(200, 150)
    local elementHeight = s.y + 25 
    
    
    w.nextX = w.nextX or 0
    
    
    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight + 5
        w.nextX = 0
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return id
    end
    
    local p = Vector2D(w.pos.x + 10 + w.nextX, w.pos.y + w.nextY - scrollOffset)
    
    
    if w.nextX + s.x + 10 > w.size.x - 20 then
        w.nextY = w.nextY + elementHeight + 5
        w.nextX = 0
        p = Vector2D(w.pos.x + 10, w.pos.y + w.nextY - scrollOffset)
    end
    
    
    if label and label ~= "" then
        Renderer.DrawText('font', label, p, false, UI.TextOutLine, UI.Colors.Text)
    end
    
    local listPos = Vector2D(p.x, p.y + (label and 20 or 0))
    local listScrollId = id .. "_scroll"
    
    
    if not UI.scrollOffsets[listScrollId] then
        UI.scrollOffsets[listScrollId] = 0
    end
    
    local itemHeight = 25
    local visibleHeight = s.y
    local fullContentHeight = items and (#items * itemHeight) or 0
    local maxScroll = math.max(0, fullContentHeight - visibleHeight)
    local needsScrollbar = maxScroll > 0
    local listScrollOffset = UI.scrollOffsets[listScrollId] or 0
    local mp = Input.GetCursorPos()
    
    
    listScrollOffset = math.floor(listScrollOffset / itemHeight) * itemHeight
    UI.scrollOffsets[listScrollId] = listScrollOffset
    
    
    Renderer.DrawRectFilled(listPos, Vector2D(listPos.x + s.x, listPos.y + s.y), UI.Colors.ComboBg, 2)
    Renderer.DrawRect(listPos, Vector2D(listPos.x + s.x, listPos.y + s.y), UI.Colors.InputBorder, 2)
    
    
    local clipRect = {
        x1 = listPos.x,
        y1 = listPos.y,
        x2 = listPos.x + s.x,
        y2 = listPos.y + s.y
    }
    
    
    if items then
        local itemWidth = needsScrollbar and s.x - 14 or s.x
        local startItem = math.max(1, math.floor(listScrollOffset / itemHeight) + 1)
        local endItem = math.min(#items, startItem + math.ceil(visibleHeight / itemHeight))
        
        for i = startItem, endItem do
            local item = items[i]
            if item then
                local itemY = listPos.y + (i - 1) * itemHeight - listScrollOffset
                
                
                if itemY >= listPos.y and itemY + itemHeight <= listPos.y + visibleHeight then
                    local ip = Vector2D(listPos.x + 2, itemY + 2)
                    local ir = {
                        x1 = listPos.x, 
                        y1 = itemY, 
                        x2 = listPos.x + itemWidth, 
                        y2 = itemY + itemHeight
                    }
                    
                    local ih = mp and UI.IsPointInRect(mp, ir) and 
                              itemY >= listPos.y and itemY + itemHeight <= listPos.y + visibleHeight
                    
                    
                    if i == UI.state[id].value then
                        Renderer.DrawRectFilled(
                            Vector2D(listPos.x + 1, itemY), 
                            Vector2D(listPos.x + itemWidth - 1, itemY + itemHeight), 
                            UI.Colors.SliderFill, 0
                        )
                    elseif ih then
                        Renderer.DrawRectFilled(
                            Vector2D(listPos.x + 1, itemY), 
                            Vector2D(listPos.x + itemWidth - 1, itemY + itemHeight), 
                            UI.Colors.ButtonHover, 0
                        )
                        
                        
                        if UI.mouseDown and not UI.lastMouseDown then
                            UI.state[id].value = i
                            UI.state[id].changed = true
                        end
                    end
                    
                    
                    local textColor = (i == UI.state[id].value) and UI.Colors.ChildBg or UI.Colors.Text
                    Renderer.DrawText('font', item, Vector2D(ip.x + 6, ip.y + 4), false, UI.TextOutLine, textColor)
                end
            end
        end
    end
    
    
    if needsScrollbar then
        local scrollbarX = listPos.x + s.x - 14
        local scrollbarTop = listPos.y + 2
        local scrollbarHeight = s.y - 4
        local scrollbarBottom = scrollbarTop + scrollbarHeight
        
        
        Renderer.DrawRectFilled(
            Vector2D(scrollbarX, scrollbarTop), 
            Vector2D(scrollbarX + 10, scrollbarBottom), 
            UI.Colors.SliderBg, 2
        )
        
        
        local thumbHeight = math.max(20, (scrollbarHeight * visibleHeight) / fullContentHeight)
        local thumbPos = scrollbarTop + (listScrollOffset / maxScroll) * (scrollbarHeight - thumbHeight)
        
        
        local scrollbarRect = {
            x1 = scrollbarX,
            y1 = scrollbarTop,
            x2 = scrollbarX + 10,
            y2 = scrollbarBottom
        }

        local overScrollbar = UI.IsPointInRect(mp, scrollbarRect)
        local overThumb = mp.y >= thumbPos and mp.y <= thumbPos + thumbHeight and overScrollbar
        
        
        if not UI.dragState then
            UI.dragState = {}
        end
        
        
        if UI.mouseDown then
            if (overThumb or UI.dragState.scrolling == listScrollId) and not UI.dragState.scrolling then
                UI.dragState.scrolling = listScrollId
                UI.dragState.dragOffset = mp.y - thumbPos
            elseif UI.dragState.scrolling == listScrollId then
                local relativeY = mp.y - scrollbarTop - UI.dragState.dragOffset
                local scrollPercent = math.max(0, math.min(1, relativeY / (scrollbarHeight - thumbHeight)))
                local newScroll = scrollPercent * maxScroll
                
                UI.scrollOffsets[listScrollId] = math.floor(newScroll / itemHeight) * itemHeight
            elseif overScrollbar and not overThumb and not UI.dragState.scrolling then
                local relativeY = mp.y - scrollbarTop
                local scrollPercent = math.max(0, math.min(1, (relativeY - thumbHeight/2) / (scrollbarHeight - thumbHeight)))
                local newScroll = scrollPercent * maxScroll
                
                UI.scrollOffsets[listScrollId] = math.floor(newScroll / itemHeight) * itemHeight
            end
        else
            if UI.dragState.scrolling == listScrollId then
                UI.dragState.scrolling = nil
                UI.dragState.dragOffset = nil
            end
        end
        
        
        listScrollOffset = UI.scrollOffsets[listScrollId] or 0
        thumbPos = scrollbarTop + (listScrollOffset / maxScroll) * (scrollbarHeight - thumbHeight)
        
        
        local isDragging = UI.dragState.scrolling == listScrollId
        local thumbColor = isDragging and UI.Colors.SliderFill or (overScrollbar and UI.Colors.ButtonHover or UI.Colors.SliderKnob)
        Renderer.DrawRectFilled(
            Vector2D(scrollbarX + 1, thumbPos), 
            Vector2D(scrollbarX + 9, thumbPos + thumbHeight), 
            thumbColor, 2
        )
        
        
        if listScrollOffset > 0 and not UI.dragState.scrolling then
            local arrowPos = Vector2D(scrollbarX + 5, scrollbarTop + 3)
            local overUpArrow = mp.x >= scrollbarX and mp.x <= scrollbarX + 10 and
                               mp.y >= scrollbarTop and mp.y <= scrollbarTop + 15
            
            if overUpArrow and UI.mouseDown and not UI.lastMouseDown then
                local newScroll = math.max(0, listScrollOffset - itemHeight)
                UI.scrollOffsets[listScrollId] = newScroll
            end
            
            local arrowColor = overUpArrow and UI.Colors.SliderFill or UI.Colors.Text
            Renderer.DrawText('font', "▲", arrowPos, true, UI.TextOutLine, arrowColor)
        end
        
        
        if listScrollOffset < maxScroll and not UI.dragState.scrolling then
            local arrowPos = Vector2D(scrollbarX + 5, scrollbarBottom - 12)
            local overDownArrow = mp.x >= scrollbarX and mp.x <= scrollbarX + 10 and
                                 mp.y >= scrollbarBottom - 15 and mp.y <= scrollbarBottom
            
            if overDownArrow and UI.mouseDown and not UI.lastMouseDown then
                local newScroll = math.min(maxScroll, listScrollOffset + itemHeight)
                UI.scrollOffsets[listScrollId] = newScroll
            end
            
            local arrowColor = overDownArrow and UI.Colors.SliderFill or UI.Colors.Text
            Renderer.DrawText('font', "▼", arrowPos, true, UI.TextOutLine, arrowColor)
        end
    end
    
    
   
    w.nextY = w.nextY + s.y + 23

    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY + elementHeight)
    
    return id
end

function UI.TextInput(id, label, def, size)
    if not UI.currWin then return id end
    if not UI.state[id] then UI.state[id] = {value = def or ""} end
    
    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local elementHeight = 50
    
    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return id
    end
    
    local p = Vector2D(w.pos.x + 10 + (w.nextX or 0), w.pos.y + w.nextY - scrollOffset)
    local s = size or Vector2D(150, 25)
    
    Renderer.DrawText('font', label, p, false, UI.TextOutLine, UI.Colors.Text)
    
    local ip = Vector2D(p.x, p.y + 20)
    local r = {x1 = ip.x, y1 = ip.y, x2 = ip.x + s.x, y2 = ip.y + s.y}
    local mp = Input.GetCursorPos()
    local hov = mp and UI.IsPointInRect(mp, r)
    local click = hov and UI.mouseDown and not UI.lastMouseDown
    
    UI.state[id].hovered = hov or false
    
    if click then 
        UI.focusedInput = id
        UI.state[id].focused = true
    elseif UI.mouseDown and not UI.lastMouseDown and not hov and UI.focusedInput == id then 
        UI.focusedInput = nil
        UI.state[id].focused = false
    end
    
    local foc = UI.focusedInput == id
    UI.state[id].focused = foc
    
    
    local bgColor = foc and UI.Colors.InputBg or UI.Colors.InputBg
    local borderColor = foc and UI.Colors.SliderFill or (hov and UI.Colors.ButtonHover or UI.Colors.InputBorder)
    
    Renderer.DrawRectFilled(ip, Vector2D(ip.x + s.x, ip.y + s.y), bgColor, 6)
    Renderer.DrawRect(ip, Vector2D(ip.x + s.x, ip.y + s.y), borderColor, 6)
    
    local ct = UI.state[id].value or ""
    local dt = foc and ct .. "|" or ct
    Renderer.DrawText('font', dt, Vector2D(ip.x + 8, ip.y + 6), false, UI.TextOutLine, UI.Colors.Text)
    
    w.nextY = w.nextY + 50
    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
    return id
end




function UI.DrawKeybindButton(id, pos, size)
    if not UI.keybinds[id] then return end
    
    local keybind = UI.keybinds[id]
    local mp = Input.GetCursorPos()
    local buttonRect = {x1 = pos.x, y1 = pos.y, x2 = pos.x + size.x, y2 = pos.y + size.y}
    local buttonHov = mp and UI.IsPointInRect(mp, buttonRect)
    local leftClick = buttonHov and UI.mouseDown and not UI.lastMouseDown
    local rightClick = buttonHov and UI.rightMouseDown and not UI.lastRightMouseDown
    
    
    if leftClick then
        keybind.waitingForKey = not keybind.waitingForKey
        keybind.showTypeDropdown = false 
    end
    
    
    if rightClick then
        keybind.showTypeDropdown = not keybind.showTypeDropdown
        keybind.waitingForKey = false 
    end
    
    
    if UI.mouseDown and not buttonHov and keybind.showTypeDropdown then
        local dropdownRect = {
            x1 = pos.x, 
            y1 = pos.y + size.y,
            x2 = pos.x + size.x, 
            y2 = pos.y + size.y + 75
        }
        if not UI.IsPointInRect(mp, dropdownRect) then
            keybind.showTypeDropdown = false
        end
    end
    
    
    if keybind.waitingForKey and keybind.type ~= "always" then
        local keyCode, keyName = UI.GetKeyInput()
        if keyCode and keyCode ~= 0x1B then 
            keybind.key = keyCode
            keybind.waitingForKey = false
        elseif keyCode == 0x1B then 
            keybind.waitingForKey = false
        end
    end
    
    
    local buttonColor = keybind.waitingForKey and UI.Colors.ButtonActive or 
                       (buttonHov and UI.Colors.ButtonHover or UI.Colors.ButtonNormal)
    
    Renderer.DrawRectFilled(pos, Vector2D(pos.x + size.x, pos.y + size.y), buttonColor, 4)
    Renderer.DrawRect(pos, Vector2D(pos.x + size.x, pos.y + size.y), UI.Colors.InputBorder, 4)
    
    
    local keybindText = "None"
    
    if keybind.waitingForKey and  keybind.type ~= "always" then
        keybindText = "Press Key"
    elseif keybind.key and  keybind.type ~= "always" then
        local keyMap = {
            [0x08] = "BACKSPACE", [0x11] = "CTRL", [0x10] = "SHIFT", [0x0D] = "ENTER", 
            [0x20] = "SPACE", [0x2E] = "DELETE", [0x1B] = "ESCAPE",
            [0x30] = "0", [0x31] = "1", [0x32] = "2", [0x33] = "3", [0x34] = "4",
            [0x35] = "5", [0x36] = "6", [0x37] = "7", [0x38] = "8", [0x39] = "9",
            [0x41] = "A", [0x42] = "B", [0x43] = "C", [0x44] = "D", [0x45] = "E",
            [0x46] = "F", [0x47] = "G", [0x48] = "H", [0x49] = "I", [0x4A] = "J",
            [0x4B] = "K", [0x4C] = "L", [0x4D] = "M", [0x4E] = "N", [0x4F] = "O",
            [0x50] = "P", [0x51] = "Q", [0x52] = "R", [0x53] = "S", [0x54] = "T",
            [0x55] = "U", [0x56] = "V", [0x57] = "W", [0x58] = "X", [0x59] = "Y",
            [0x5A] = "Z",
            [0xBA] = ";", [0xBB] = "=", [0xBC] = ",", [0xBD] = "-", [0xBE] = ".", [0xBF] = "/",
            [0xC0] = "`", [0xDB] = "[", [0xDC] = "\\", [0xDD] = "]", [0xDE] = "'", [0x04] = "MOUSE3", [0x05] = "MOUSE4", [0x06] = "MOUSE5"
        }
        keybindText = keyMap[keybind.key] or "None"
    elseif keybind.type == "always" then
        keybindText = "Always"

    end

    Renderer.DrawText('font', keybindText, Vector2D(pos.x + size.x/2, pos.y + size.y/2 - 7), true, UI.TextOutLine, UI.Colors.Text)
    
    
    if keybind.showTypeDropdown then
        local dropdownItems = {"toggle", "hold", "always"}
        local dropdownHeight = #dropdownItems * 25
        local dropdownPos = Vector2D(pos.x, pos.y + size.y)
        
        UI.QueueRender(UI.RENDER_PRIORITY.COMBO_DROPDOWN, function()
            
            Renderer.DrawRectFilled(dropdownPos, Vector2D(dropdownPos.x + size.x, dropdownPos.y + dropdownHeight), UI.Colors.ComboBg, 6)
            Renderer.DrawRect(dropdownPos, Vector2D(dropdownPos.x + size.x, dropdownPos.y + dropdownHeight), UI.Colors.InputBorder, 6)
            
            
            for i, item in ipairs(dropdownItems) do
                local itemPos = Vector2D(dropdownPos.x, dropdownPos.y + (i-1) * 25)
                local itemRect = {x1 = itemPos.x, y1 = itemPos.y, x2 = itemPos.x + size.x, y2 = itemPos.y + 25}
                local itemHov = mp and UI.IsPointInRect(mp, itemRect)
                local itemClick = itemHov and UI.mouseDown and not UI.lastMouseDown
                
                
                if itemClick then
                    keybind.type = item
                    keybind.showTypeDropdown = false
                end
                
                
                if itemHov then 
                    Renderer.DrawRectFilled(itemPos, Vector2D(itemPos.x + size.x, itemPos.y + 25), UI.Colors.ButtonHover, 0) 
                end
                
                
                if item == keybind.type then
                    Renderer.DrawRectFilled(itemPos, Vector2D(itemPos.x + size.x, itemPos.y + 25), UI.Colors.SliderFill, 0)
                end
                
                
                Renderer.DrawText('font', item, Vector2D(itemPos.x + 8, itemPos.y + 6), false, UI.TextOutLine, UI.Colors.Text)
            end
        end)
    end
end

function UI.Checkbox(id, label, def, hasKeybind, keybindType)
    if not UI.currWin then return id end
    if not UI.state[id] then 
        UI.state[id] = {
            value = def or false,
            hovered = false,
            pressed = false,
            changed = false
        }
    end
    
    
    if hasKeybind then
        UI.InitKeybind(id, keybindType)
    end
    
    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local elementHeight = 25
    
    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return id
    end
    
    local p = Vector2D(w.pos.x + 10 + (w.nextX or 0), w.pos.y + w.nextY - scrollOffset)
    local cs = 16
    local r = {x1 = p.x, y1 = p.y, x2 = p.x + cs, y2 = p.y + cs}
    
    local mp = Input.GetCursorPos()
    local hov = mp and UI.IsPointInRect(mp, r)
    local click = hov and UI.mouseDown and not UI.lastMouseDown
    
    UI.state[id].hovered = hov or false
    
    
    if click then
        UI.state[id].value = not UI.state[id].value
        UI.state[id].changed = true
        UI.state[id].pressed = true
    end
    
    
    local bgColor = UI.state[id].value and UI.Colors.SliderFill or (hov and UI.Colors.ButtonHover or UI.Colors.ButtonNormal)
    local borderColor = UI.state[id].value and UI.Colors.SliderFill or UI.Colors.InputBorder
    
    Renderer.DrawRectFilled(p, Vector2D(p.x + cs, p.y + cs), bgColor, 4)
    Renderer.DrawRect(p, Vector2D(p.x + cs, p.y + cs), borderColor, 4)
    
    
    
    
    local labelX = p.x + cs + 10
    Renderer.DrawText('font', label, Vector2D(labelX, p.y + 1), false, UI.TextOutLine, UI.Colors.Text)
    
    
    if hasKeybind then
        local keybindButtonX = labelX + 170
        local keybindButtonSize = Vector2D(55, 20)
        local keybindButtonPos = Vector2D(keybindButtonX, p.y)
        
        
        UI.DrawKeybindButton(id, keybindButtonPos, keybindButtonSize)
    end
    
    w.nextY = w.nextY + 25
    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
    return id
end


function UI.Separator()
    if not UI.currWin then return end
    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local elementHeight = 15
    
    
    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return
    end
   
    local p = Vector2D(w.pos.x + 10 + (w.nextX or 0), w.pos.y + w.nextY + 5 - scrollOffset)
    local ep = Vector2D(w.pos.x + w.size.x - 20, w.pos.y + w.nextY + 5 - scrollOffset)
    
    
    Renderer.DrawLine(p, ep, UI.Colors.InputBorder, 1)
    
    w.nextY = w.nextY + 15
    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
end

function UI.Text(text)
    if not UI.currWin then return end
    local w = UI.currWin
    local scrollOffset = UI.scrollOffsets[w.id] or 0
    local elementHeight = 20
    
    
    if not UI.IsElementVisible(w.nextY - scrollOffset, elementHeight, w) then
        w.nextY = w.nextY + elementHeight
        UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
        return
    end
    
    local p = Vector2D(w.pos.x + 10 + (w.nextX or 0), w.pos.y + w.nextY - scrollOffset)
    Renderer.DrawText('font', text, p, false, UI.TextOutLine, UI.Colors.Text)
    w.nextY = w.nextY + 20
    UI.contentHeights[w.id] = math.max(UI.contentHeights[w.id], w.nextY)
end

function UI.SetWindowVisible(w, vis) 
    if w then w.visible = vis end 
end


function UI.SameLine(offset)
    if not UI.currWin then return end
    local w = UI.currWin
    
    w.nextY = w.nextY - (w.lastElementHeight or 25)
    
    w.nextX = (w.nextX or 0) + (w.lastElementWidth or 100) + (offset or 10)
end

function UI.NewLine()
    if not UI.currWin then return end
    local w = UI.currWin
    w.nextX = 0
    
end

function UI.SetNextX(x)
    if not UI.currWin then return end
    UI.currWin.nextX = x
end

function UI.GetNextX()
    if not UI.currWin then return 0 end
    return UI.currWin.nextX or 0
end

function UI.GetTextSize(text, width_map)
    if width_map == nil then return 0 end
    local offset = 0

    for i = 1, text:len() do
        local char = text:sub(i, i)
        offset = offset + (width_map[char] or 0)
    end

    return offset
end



return UI