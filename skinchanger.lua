-- Skinchanger with CS:GO API Integration & Texture Preview by fate
-- Uses https://bymykel.com/CSGO-API/ for skin previews and data
-- Uses Plague API for proper menu system and texture rendering
-- Enable Unsafe Scripts!
--
-- FEATURES:
-- - Browse CS:GO skins with preview images rendered as textures
-- - Filter by weapon type and category
-- - Search functionality with real-time filtering
-- - Download and cache skin preview images
-- - Apply skins (integrate with your cheat's skin system)
-- - Proper Plague API menu integration
-- - Texture-based image preview system
--
-- USAGE:
-- 1. Load the script with "Enable Unsafe Scripts" checked
-- 2. Use the menu controls to browse and select skins
-- 3. Preview images are automatically downloaded and displayed
-- 4. Click "Apply Selected Skin" to apply the skin
--
-- INTEGRATION:
-- To integrate with your cheat's skin system, modify the applySkin() function
-- to call your cheat's skin changing functions with the paint_index value.

package.path = "C:\\plaguecheat.cc\\lib\\?.lua;" .. package.path

-- Safe require function with error handling
local function safeRequire(module)
    local success, result = pcall(require, module)
    if success then
        return result
    else
        print("Error loading module '" .. module .. "': " .. tostring(result))
        return nil
    end
end

local LIP = safeRequire("LIP2")

-- Configuration
local CONFIG_FILE = "C:\\plaguecheat.cc\\config\\skinchanger.ini"
local CACHE_DIR = "C:\\plaguecheat.cc\\data\\skins\\"
local API_BASE_URL = "https://bymykel.com/CSGO-API"

-- Create directories if they don't exist
os.execute('mkdir "' .. CACHE_DIR:gsub("/", "\\") .. '" 2>nul')
os.execute('mkdir "C:\\plaguecheat.cc\\config" 2>nul')

-- Global state
local skinData = {}
local filteredSkins = {}
local selectedSkinIndex = 1
local currentTexture = nil
local textureCache = {}
local isLoading = false

-- Menu variables (created using Plague API)
local menuVars = {
    enabled = nil,
    weaponFilter = nil,
    categoryFilter = nil,
    skinList = nil,
    autoDownload = nil,
    previewSize = nil
}

-- Weapon and category lists
local weaponTypes = {
    "All Weapons", "Pistols", "Rifles", "SMGs", "Shotguns", "Sniper Rifles", "Machine Guns"
}

local categoryTypes = {
    "All Categories", "Consumer Grade", "Industrial Grade", "Mil-Spec Grade", 
    "Restricted", "Classified", "Covert", "Contraband"
}

-- Sample skin data (from CS:GO API format)
local function getDefaultSkinData()
    return {
        {
            id = "skin-65604",
            name = "Desert Eagle | Urban DDPAT",
            weapon = { name = "Desert Eagle", id = "weapon_deagle" },
            category = { name = "Pistols" },
            rarity = { name = "Industrial Grade", color = "#5e98d9" },
            paint_index = "17",
            image = "https://raw.githubusercontent.com/ByMykel/counter-strike-image-tracker/main/static/panorama/images/econ/default_generated/weapon_deagle_hy_ddpat_urb_light_png.png"
        },
        {
            id = "skin-1967292",
            name = "AK-47 | Redline",
            weapon = { name = "AK-47", id = "weapon_ak47" },
            category = { name = "Rifles" },
            rarity = { name = "Classified", color = "#d32ce6" },
            paint_index = "282",
            image = "https://raw.githubusercontent.com/ByMykel/counter-strike-image-tracker/main/static/panorama/images/econ/default_generated/weapon_ak47_cu_ak47_cobra_light_png.png"
        },
        {
            id = "skin-524292",
            name = "AWP | Dragon Lore",
            weapon = { name = "AWP", id = "weapon_awp" },
            category = { name = "Sniper Rifles" },
            rarity = { name = "Covert", color = "#eb4b4b" },
            paint_index = "344",
            image = "https://raw.githubusercontent.com/ByMykel/counter-strike-image-tracker/main/static/panorama/images/econ/default_generated/weapon_awp_cu_awp_dragon_lore_light_png.png"
        },
        {
            id = "skin-131072",
            name = "M4A4 | Howl",
            weapon = { name = "M4A4", id = "weapon_m4a1" },
            category = { name = "Rifles" },
            rarity = { name = "Covert", color = "#eb4b4b" },
            paint_index = "309",
            image = "https://raw.githubusercontent.com/ByMykel/counter-strike-image-tracker/main/static/panorama/images/econ/default_generated/weapon_m4a1_cu_m4a4_howl_light_png.png"
        },
        {
            id = "skin-262144",
            name = "Glock-18 | Fade",
            weapon = { name = "Glock-18", id = "weapon_glock" },
            category = { name = "Pistols" },
            rarity = { name = "Restricted", color = "#8847ff" },
            paint_index = "38",
            image = "https://raw.githubusercontent.com/ByMykel/counter-strike-image-tracker/main/static/panorama/images/econ/default_generated/weapon_glock_aa_fade_light_png.png"
        }
    }
end

-- Configuration management with error handling
local function loadConfig()
    if not LIP then
        print("Warning: LIP2 library not available, using default config")
        return { auto_download = true, preview_size = 200 }
    end

    local success, config = pcall(LIP.load, CONFIG_FILE)
    if success and config and config.settings then
        return {
            auto_download = config.settings.auto_download == "true",
            preview_size = tonumber(config.settings.preview_size) or 200
        }
    end
    return { auto_download = true, preview_size = 200 }
end

local function saveConfig(settings)
    if not LIP then
        print("Warning: LIP2 library not available, cannot save config")
        return
    end

    local config = {
        settings = {
            auto_download = tostring(settings.auto_download),
            preview_size = tostring(settings.preview_size)
        }
    }

    local success, err = pcall(LIP.save, CONFIG_FILE, config)
    if not success then
        print("Error saving config: " .. tostring(err))
    end
end

-- Download image using PowerShell and convert to base64 with error handling
local function downloadImageAsBase64(url, skinId)
    local success, result = pcall(function()
        local tempFile = CACHE_DIR .. skinId .. ".png"
        local base64File = CACHE_DIR .. skinId .. ".b64"

        -- Check if base64 file already exists
        local file = io.open(base64File, "r")
        if file then
            local base64Data = file:read("*all")
            file:close()
            return base64Data
        end

        -- Download image
        local downloadCmd = string.format(
            'powershell -Command "try { Invoke-WebRequest -Uri \'%s\' -OutFile \'%s\' } catch { Write-Host \'Download failed\' }"',
            url, tempFile
        )

        if os.execute(downloadCmd) == 0 then
            -- Convert to base64
            local base64Cmd = string.format(
                'powershell -Command "try { [Convert]::ToBase64String([IO.File]::ReadAllBytes(\'%s\')) | Out-File -FilePath \'%s\' -Encoding ASCII } catch { Write-Host \'Base64 conversion failed\' }"',
                tempFile, base64File
            )

            if os.execute(base64Cmd) == 0 then
                local file = io.open(base64File, "r")
                if file then
                    local base64Data = file:read("*all"):gsub("%s+", "") -- Remove whitespace
                    file:close()
                    os.remove(tempFile) -- Clean up temp file
                    return base64Data
                end
            end
        end

        return nil
    end)

    if success then
        return result
    else
        print("Error downloading image for " .. skinId .. ": " .. tostring(result))
        return nil
    end
end

-- Create texture from skin image with error handling
local function createSkinTexture(skin)
    local success, result = pcall(function()
        if textureCache[skin.id] then
            return textureCache[skin.id]
        end

        local base64Data = downloadImageAsBase64(skin.image, skin.id)
        if base64Data and base64Data ~= "" then
            local texture = Renderer.CreateTextureFromBase64(base64Data, Vector2D(256, 256))
            if texture then
                textureCache[skin.id] = texture
                return texture
            end
        end

        return nil
    end)

    if success then
        return result
    else
        print("Error creating texture for " .. skin.name .. ": " .. tostring(result))
        return nil
    end
end

-- Filter skins based on current selection
local function updateFilteredSkins()
    filteredSkins = {}
    local weaponFilter = menuVars.weaponFilter and menuVars.weaponFilter:GetValue() or 0
    local categoryFilter = menuVars.categoryFilter and menuVars.categoryFilter:GetValue() or 0
    
    for _, skin in ipairs(skinData) do
        local matchesWeapon = weaponFilter == 0 or 
            (weaponFilter == 1 and skin.category.name == "Pistols") or
            (weaponFilter == 2 and skin.category.name == "Rifles") or
            (weaponFilter == 3 and skin.category.name == "SMGs") or
            (weaponFilter == 4 and skin.category.name == "Shotguns") or
            (weaponFilter == 5 and skin.category.name == "Sniper Rifles") or
            (weaponFilter == 6 and skin.category.name == "Machine Guns")
            
        local matchesCategory = categoryFilter == 0 or
            (categoryFilter == 1 and skin.rarity.name == "Consumer Grade") or
            (categoryFilter == 2 and skin.rarity.name == "Industrial Grade") or
            (categoryFilter == 3 and skin.rarity.name == "Mil-Spec Grade") or
            (categoryFilter == 4 and skin.rarity.name == "Restricted") or
            (categoryFilter == 5 and skin.rarity.name == "Classified") or
            (categoryFilter == 6 and skin.rarity.name == "Covert") or
            (categoryFilter == 7 and skin.rarity.name == "Contraband")
            
        if matchesWeapon and matchesCategory then
            table.insert(filteredSkins, skin)
        end
    end
    
    -- Update skin list options
    local skinNames = {}
    for i, skin in ipairs(filteredSkins) do
        table.insert(skinNames, skin.name)
    end
    
    if menuVars.skinList and #skinNames > 0 then
        -- Note: Plague API doesn't have a direct way to update combo items
        -- This would need to be handled differently in a real implementation
        selectedSkinIndex = math.min(selectedSkinIndex, #filteredSkins)
    end
end

-- Apply selected skin (integrate with your cheat's skin system here) with error handling
local function applySkin()
    local success, err = pcall(function()
        if #filteredSkins > 0 and selectedSkinIndex <= #filteredSkins then
            local skin = filteredSkins[selectedSkinIndex]

            -- TODO: Replace this with your cheat's skin changing function
            -- Example: YourCheat.SetSkin(skin.weapon.id, tonumber(skin.paint_index))

            print("Applying skin: " .. skin.name .. " (Paint Index: " .. skin.paint_index .. ")")

            -- You would typically call something like:
            -- Cheat.SkinChanger.SetSkin(skin.weapon.id, tonumber(skin.paint_index))
            -- or whatever your cheat's API provides
        else
            print("No skin selected or invalid selection")
        end
    end)

    if not success then
        print("Error applying skin: " .. tostring(err))
    end
end

-- Initialize skin data
local function initializeSkinData()
    skinData = getDefaultSkinData()
    updateFilteredSkins()
end

-- Create menu using Plague API with error handling
local function createMenu()
    local success, err = pcall(function()
        -- Main toggle
        menuVars.enabled = Menu.Checker('Skinchanger Enabled', false)

        -- Auto download setting
        local config = loadConfig()
        menuVars.autoDownload = Menu.Checker('Auto Download Images', config.auto_download)

        -- Preview size slider
        menuVars.previewSize = Menu.Slider('Preview Size', config.preview_size, 100, 400)

        -- Weapon filter
        menuVars.weaponFilter = Menu.Combo('Weapon Filter', 0, weaponTypes)

        -- Category filter
        menuVars.categoryFilter = Menu.Combo('Category Filter', 0, categoryTypes)

        -- Skin selection (will be populated after filtering)
        local initialSkinNames = {}
        for _, skin in ipairs(skinData) do
            table.insert(initialSkinNames, skin.name)
        end
        menuVars.skinList = Menu.List('Available Skins', 0, initialSkinNames, 8)

        -- Apply button (using a checkbox as a button trigger)
        menuVars.applyButton = Menu.Checker('Apply Selected Skin', false)
    end)

    if not success then
        print("Error creating menu: " .. tostring(err))
        print("Menu creation failed - some features may not work properly")
    end
end

-- Render skin preview texture with error handling
local function renderSkinPreview()
    local success, err = pcall(function()
        if not menuVars.enabled or not menuVars.enabled:GetValue() then
            return
        end

        if #filteredSkins > 0 then
            local skinIndex = menuVars.skinList and menuVars.skinList:GetValue() or 0
            skinIndex = skinIndex + 1 -- Convert from 0-based to 1-based

            if skinIndex <= #filteredSkins then
                selectedSkinIndex = skinIndex
                local skin = filteredSkins[selectedSkinIndex]

                -- Create or get cached texture
                if not currentTexture or currentTexture.skinId ~= skin.id then
                    currentTexture = createSkinTexture(skin)
                    if currentTexture then
                        currentTexture.skinId = skin.id
                    end
                end

                -- Render texture if available
                if currentTexture then
                    local screenSize = Renderer.GetScreenSize()
                    local previewSize = menuVars.previewSize and menuVars.previewSize:GetValue() or 200

                    -- Position preview in top-right corner
                    local posX = screenSize.x - previewSize - 20
                    local posY = 20

                    -- Draw background
                    Renderer.DrawRectFilled(
                        Vector2D(posX - 5, posY - 5),
                        Vector2D(posX + previewSize + 5, posY + previewSize + 5),
                        Color(0, 0, 0, 180)
                    )

                    -- Draw texture
                    Renderer.DrawTexture(
                        currentTexture,
                        Vector2D(posX, posY),
                        Vector2D(previewSize, previewSize)
                    )

                    -- Draw skin info text
                    local textY = posY + previewSize + 10
                    Renderer.DrawText('Arial', skin.name, Vector2D(posX, textY), false, true, Color(255, 255, 255, 255))
                    Renderer.DrawText('Arial', 'Paint Index: ' .. skin.paint_index, Vector2D(posX, textY + 20), false, true, Color(200, 200, 200, 255))
                    Renderer.DrawText('Arial', 'Rarity: ' .. skin.rarity.name, Vector2D(posX, textY + 40), false, true, Color(200, 200, 200, 255))
                end
            end
        end
    end)

    if not success then
        print("Error in renderSkinPreview: " .. tostring(err))
    end
end

-- Handle menu value changes
local function handleMenuChanges()
    if not menuVars.enabled or not menuVars.enabled:GetValue() then
        return
    end

    -- Save config when auto download changes
    if menuVars.autoDownload then
        local config = loadConfig()
        local newAutoDownload = menuVars.autoDownload:GetValue()
        local newPreviewSize = menuVars.previewSize and menuVars.previewSize:GetValue() or 200

        if config.auto_download ~= newAutoDownload or config.preview_size ~= newPreviewSize then
            saveConfig({
                auto_download = newAutoDownload,
                preview_size = newPreviewSize
            })
        end
    end

    -- Handle apply button
    if menuVars.applyButton and menuVars.applyButton:GetValue() then
        applySkin()
        -- Reset the button (checkbox) after applying
        -- Note: Plague API might not support setting values, so this might not work
        -- menuVars.applyButton:SetValue(false)
    end

    -- Update filtered skins when filters change
    updateFilteredSkins()
end

-- Main callback functions with error handling
local function onPaint()
    local success, err = pcall(function()
        if menuVars.enabled and menuVars.enabled:GetValue() then
            renderSkinPreview()
            handleMenuChanges()
        end
    end)

    if not success then
        print("Error in onPaint callback: " .. tostring(err))
    end
end

-- Initialize everything with comprehensive error handling
local function initialize()
    local success, err = pcall(function()
        print("Initializing Skinchanger...")

        -- Check if required APIs are available
        if not Menu then
            error("Menu API not available - make sure you're running this in the cheat environment")
        end

        if not Renderer then
            error("Renderer API not available - make sure you're running this in the cheat environment")
        end

        if not Cheat then
            error("Cheat API not available - make sure you're running this in the cheat environment")
        end

        -- Initialize components
        initializeSkinData()
        createMenu()

        -- Register callbacks
        Cheat.RegisterCallback("OnRenderer", onPaint)

        print("Skinchanger loaded successfully!")
        print("Use the menu to browse skins and check 'Apply Selected Skin' to apply.")
    end)

    if not success then
        print("CRITICAL ERROR: Failed to initialize Skinchanger!")
        print("Error: " .. tostring(err))
        print("Please check that:")
        print("1. You have 'Enable Unsafe Scripts' checked")
        print("2. You're running this in the cheat environment")
        print("3. All required libraries are available")
        return false
    end

    return true
end

-- Start the script with error handling
local initSuccess = initialize()
if not initSuccess then
    print("Skinchanger failed to load - check the errors above")
end
