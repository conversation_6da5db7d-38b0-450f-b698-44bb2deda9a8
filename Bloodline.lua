package.path = "C:\\plaguecheat.cc\\lib\\?.lua;" .. package.path -- Define new plague library path!
local GUI = require("GUI_LIB")  -- Added to use the plague_gui lib
local LIP = require("LIP2")
GUI.Initialize()  -- Initialize the GUI library

-- Global GUI options table (already exists and is good)
local gui_options = {
	font_size = 13,
	watermark_enable = false,
	hitlogs_enable = false,
	dmgmarkers_enable = false,
	teamlist_enable = false,
	spectator_enable = false,
	fakepitch_enable = false,
	playertrails_enable = false,
	trail_length = 50,
	trail_width = 5,
	trail_color = 0,
	trail_color_picker = Color(255, 255, 255, 255),
	hitsound_enable = false,
	hitsound_volume = 50,
	hitsound = 0,
    blockbotmode = 0,
    blockbot_key = 0x56,
    blockbot_key_mode = "Toggle", -- Default mode
    autojump_enable = false, -- Added autojump_enable to gui_options
    blockbot_enable = false, -- Added blockbot_enable to gui_options
    blockbot_circle_color = Color(0, 255, 255, 255), -- Default <PERSON><PERSON>
    blockbot_on_head_color = Color(255, 255, 0, 255), -- Default Yellow
    sound_enabled = false,
    sound_peak_radius = 10,
    sound_draw_always = false,
    particle_amount = 200,
    theme = 0,
}




-- Global GUI elements table (already exists and is good)
GUI_ELEMENTS = {}

-- Global font state
local font_state = {
    current_font_size = gui_options.font_size,
    fonts = {},
    current_font_name = "",
}

-- Function to approximate text width using a provided character width map for animation
-- Moved to the top to ensure it's defined before any calls.
function ApproxTextWidthAnimation(txt, map)
  local m = map or animation_state.main_width_map -- Use main map by default if no map is provided
  local tot = 0
  for i = 1, #txt do
    local c = txt:sub(i,i)
    tot = tot + (m[c] or animation_state.mono_width)
  end
  return tot
end


-- Global state for dragging and snapping
local global_ui_state = {
    CONTROL_KEY = 0x11, -- CTRL Key
    SNAP_DISTANCE_THRESHOLD = 250, -- Distance for the snapping to work to either side (So 250 pixels to either side from the center)
    SNAP_INDICATOR_COLOR = Color(255, 255, 255, 85), -- The color of the snap indicator on the screen
    isTeamDmgDragging = false, -- if not place here you can drag 2 elements at once
    isWatermarkDragging = false, -- Dragging state for the watermark
    isDamageIndicatorDragging = false, -- Dragging for the dmg indicator
    isSpecDragging = false, -- Dragging state for spectator list
    isIntroActive = true, -- Controls the startup animation
    guiMenuInitialized = false,
    -- removed loggedIn and loginWindowCreated
    configLoadAttempted = false,
    cfgFile = "c:\\plaguecheat.cc\\config\\bloodline.ini",
    configLoaded = false,
    introCancelDelay = 0.1, -- Delay in seconds before allowing intro cancellation
    introStartDelayTime = nil, -- Time when the intro starts
}

-- Watermark specific state
local watermark_state = {
    username = Cheat.GetUserName() or "User", -- Returns Forum username (Only returns "plaguecheat" atm)
    base_segments = {}, -- Non dynamic parts of the watermark (FPS and Ping are added later)
    total_display_segments_count = 0,
    display_segments = {},
    padding = 10, -- Padding for the watermark
    innerHeight = 0, -- Adjusted watermark height based on current font size
    x = 5, y = 10, -- Starting pos for the watermark (Can be dragged around While Menu is open) (Vector2D)
    drag_offset_x = 0, drag_offset_y = 0, -- The dragging offset pos (Vector2D)
    prev_mouse_down = false, -- Check if the mouse was down last frame
    saved_x = 0, saved_y = 0, -- The last saved Pos for the watermark
    avg_frame_time = 0.0, -- The avg framerate (Calculated later)
    smoothing_alpha = 0.005, -- Smoothing factor for the fps (0 -1) The lower the more smooth its gonna be [Relying of the avg framerate more] the higher the more responsive (the current framerate)
    calculated_fps = 0.0, -- Calculated fps
    last_raw_map_name = nil, -- Map name (Isnt used anymore) - Keep if map name is added later
    last_is_connected = nil, -- check if we are connected last time we checked
    text_vertical_offset = 4,
    kerning_map = { ["PlAGUECHEAT"] = -2, },
}

-- Initialize watermark username
if watermark_state.username == "plaguecheat" then
    watermark_state.username = Cheat.GetUserName()
end

-- Initialize watermark base segments
watermark_state.base_segments = {
    { text = "PLAGUE", color = Color(255, 255, 255) },
    { text = "CHEAT", color = Color(137, 154, 224) },
    { text = " | ", color = Color(150, 150, 150) },
    { text = watermark_state.username, color = Color(137, 154, 224) },
}
watermark_state.total_display_segments_count = #watermark_state.base_segments + 4 -- Adjusted size (FPS + Ping + 2 separators)
for i = 1, watermark_state.total_display_segments_count do
    watermark_state.display_segments[i] = { text = "", color = Color(0,0,0) }
end
for i = 1, #watermark_state.base_segments do -- load the base segments into the display segments
    watermark_state.display_segments[i].text = watermark_state.base_segments[i].text
    watermark_state.display_segments[i].color = watermark_state.base_segments[i].color
end
watermark_state.innerHeight = font_state.current_font_size + watermark_state.padding
watermark_state.saved_x = watermark_state.x
watermark_state.saved_y = watermark_state.y


-- Damage Indicator specific state
local damage_indicator_state = {
    DISPLAY_TIME = 5, -- Dmg Indicator display time in sec (5 sec)
    MAX_INDICATORS = 4, -- Max number of dmg indicators on the screen (4)
    SPACING = 15, -- The Spacing in between the indicators
    INNER_BOX_PADDING = 10, -- Inner padding
    CHAR_HEIGHT = 11, -- Estimated char hight (This might need adjustment based on font size)
    BOX_HEIGHT = 0, -- Adjusted damage indicator box height based on current font size
    OUTER_OUTLINE_OFFSET = 5,
    indicators = {}, -- dmg indicator array
    count = 0, -- how many indicators there are active
    x = 5, y = 60, -- Starting pos of the dmg indicator (Vector2D)
    prev_mouse_down = false,
    saved_x = 0, saved_y = 0,
    current_fake_preview_drag_x = 0,
    current_fake_preview_drag_y = 0,
    fake_preview_drag_offset_x = 0,
    fake_preview_drag_offset_y = 0,
    fake_hit_preview_active = false, -- Controls visibility of the fake preview when menu is open
    HITLOG_OFFSET_X = 8, -- Isnt used but it is for the text offset (centering of the text in the dmg indicator)
    hitbox_names = { -- Hitbox names
        [0] = "Generic", [1] = "Head", [2] = "Chest", [3] = "Stomach",
        [4] = "Left Arm", [5] = "Right Arm", [6] = "Left Leg", [7] = "Right Leg", [-1] = "Unknown" -- Added -1 for safety
    },
    segment_fixed_1 = "Hit ",
    segment_fixed_2 = " in the",
    segment_fixed_3 = "for ",
    segment_fixed_4 = " HP",
    segment_fixed_5 = " | ",
    segment_fixed_6 = "Health Remaining:",
    segment_fixed_7 = " HP",
    fake_victim_name = "Player", -- Preview indicator (used for dragging)
    fake_hitbox_name = "Head",
    fake_damage_hp = 99,
    fake_health_left = 1,
    current_alignment = 'left', -- alligment on the screen used for rendering (center, left, right) DO NOT USE ANOTHER VALUE THAN THOSE 3 would result in a crash
    draw_direction = 'down', -- not used anymore but could be added as a feature later
    kerning_map_ui = {}, -- Initialized later
}
damage_indicator_state.BOX_HEIGHT = font_state.current_font_size + damage_indicator_state.INNER_BOX_PADDING
damage_indicator_state.saved_x = damage_indicator_state.x
damage_indicator_state.saved_y = damage_indicator_state.y
damage_indicator_state.current_fake_preview_drag_x = damage_indicator_state.x
damage_indicator_state.current_fake_preview_drag_y = damage_indicator_state.y

-- Team Damage Tracker specific state
local team_dmg_tracker_state = {
    padding = 10,
    outer_outline_offset = 5,
    line_height = 0, -- Adjusted line height based on current font size
    hover_color = Color(107, 107, 107, 60),
    title_underline_color = Color(137, 154, 224, 255),
    title_underline_thickness = 1,
    saved_x = 5, saved_y = 500,
    drag_offset_x = 0, drag_offset_y = 0,
    prev_mouse_down = false,
    team_names = {},
    team_dmg = {},
    team_kills = {},
    damage_count = 0,
}
team_dmg_tracker_state.line_height = font_state.current_font_size + 2

-- Spectator List specific state
local spectator_list_state = {
    x = 5, y = 900,
    drag_offset_x = 0,
    drag_offset_y = 0,
    prev_mouse_down = false,
}

-- Player Trails specific state
local player_trails_state = {
    trails = {},
    color = Color(137, 154, 224, 150),
    segment_color = nil, -- Will be set dynamically or from gui_options
}

-- Hitmarkers and Impacts state
local hit_effects_state = {
    fade_rate = 0.75, -- how fast opacity drops (lower = slower)
    rise_speed = 30, -- how many pixels per second the text moves up
    impacts = {},
    hitmarkers = {},
    num_hitmarkers = 0,
    num_impacts = 0,
}

-- Blockbot specific state (Updated to include all relevant state variables)
local blockbot_state = {
    enemy = nil,
    last_key_state = false,
    keybind_active = false, -- NEW: State controlled by keybind
    debug = {
        target_name = "None",
        blocking_mode = "None",
        is_on_head = false,
        distance = 0,
    },
    blockEnemy = nil, -- The actual teammate pawn being blocked
    currentTarget = nil, -- For target stickiness
    lastDrawnTeammatePos = nil, -- For visual smoothing
    bot_has_active_jump_command = false, -- For auto-jump
    prev_block_enemy_ref_for_accel = nil, -- For acceleration prediction
    prev_target_pos_for_accel = nil, -- For acceleration prediction
    prev_target_vel_for_accel = nil, -- For acceleration prediction
    prev_actual_frame_time_for_accel_calc = 0.015625, -- For acceleration prediction
    prev_lateral_offset_sign_for_adad = 0, -- For ADAD detection
    adad_active_timer = 0, -- For ADAD countermeasures duration
    last_lateral_change_time = 0, -- For ADAD rhythm detection
    adad_rhythm_streak = 0, -- For ADAD rhythm detection
    animated_circle_phase = 0, -- For animated circle visual
}

-- Blockbot Constants (Moved from blockbot.lua and put into a table)
local blockbot_constants = {
    MAX_PLAYER_SPEED = 250,
    MAX_PREDICTION_FRAMETIME = 0.033, -- Cap prediction frametime to avoid issues with extreme lag spikes (approx 30 FPS)
    AUTOJUMP_TARGET_Z_VEL_THRESHOLD = 200, -- Minimum vertical speed of target to trigger autojump (UPDATED FROM 100 TO 200)
    MAX_CORRECTION_DISTANCE = 100, -- Define this constant for correction speed calculation

    -- On-Head Blocking Mode
    ON_HEAD_PREDICTION_FRAMES = 10,
    ON_HEAD_DEADZONE_HORIZONTAL = 1,
    ON_HEAD_HEIGHT_OFFSET = 72,         -- How far above the target's head we aim to be
    ON_HEAD_Z_THRESHOLD = 5,            -- Minimum Z distance above target to be considered "on head"
    ON_HEAD_XY_TOLERANCE = 15,
    ON_HEAD_CORRECTION_TIMESCALE_FRAMES = 0.5, -- How quickly to correct position (in frames)
    ON_HEAD_CORRECTION_GAIN = 15,       -- Multiplier for correction speed

    -- Front Block Mode (Aggressive blocking in front of the teammate)
    FRONT_BLOCK_DISTANCE = 35,          -- How far in front of the teammate to position
    FRONT_BLOCK_HEIGHT_OFFSET = 0,      -- Vertical offset for the block position
    FRONT_BLOCK_DEADZONE_HORIZONTAL = 5,
    FRONT_BLOCK_PREDICTION_FRAMES = 4,
    FRONT_BLOCK_CORRECTION_TIMESCALE_FRAMES = 0.15,
    FRONT_BLOCK_CORRECTION_GAIN = 35,
    FRONT_BLOCK_VELOCITY_THRESHOLD_FOR_DIRECTION = 50, -- Min speed for target to use their velocity dir for front block

    -- View Angles Mode (Side-to-side blocking, with ADAD adaptation)
    VIEW_ANGLES_MAX_STRAFE_POWER_BASE = 1.0,
    VIEW_ANGLES_MAX_STRAFE_POWER_ADAD = 1.0,

    VIEW_ANGLES_PREDICTION_FRAMES_ACCEL_BASE_MIN = 1,
    VIEW_ANGLES_PREDICTION_FRAMES_ACCEL_BASE_MAX = 4,
    VIEW_ANGLES_PREDICTION_FRAMES_ACCEL_ADAD = 1,

    VIEW_ANGLES_ACCEL_DAMPING_FACTOR = 0.85,

    VIEW_ANGLES_LATERAL_OFFSET_DEADZONE_BASE = 0.2,
    VIEW_ANGLES_LATERAL_OFFSET_DEADZONE_ADAD = 0.05,

    VIEW_ANGLES_MIN_VALID_PREV_FRAME_TIME = 0.001,

    -- ADAD Detection Specific Constants
    ADAD_DETECTION_MIN_SPEED_XY = 70,
    ADAD_COUNTER_DURATION_SECONDS = 0.3,
    ADAD_MIN_LATERAL_OFFSET_FOR_SIGN = 0.1,
    ADAD_RHYTHM_WINDOW_SECONDS = 0.15,
    ADAD_MIN_RHYTHM_COUNT = 2,

    -- Animated Circle Visuals
    ANIMATED_CIRCLE_RADIUS = 30, -- Radius for the animated circle
    ANIMATED_CIRCLE_SPEED = 2.0, -- How fast the circle moves up and down
    ANIMATED_CIRCLE_HEIGHT_RANGE = 72, -- How much the circle moves up and down (e.g., player height)
    ANIMATED_CIRCLE_BASE_Z_OFFSET = 0, -- Base Z offset (e.g., from feet)
    INTERPOLATION_ALPHA = 0.15, -- Smoothing factor for visual interpolation
}


-- Startup Animation specific state
local animation_state = {
    screen_size = nil,
    fade_in_duration = 0.8,
    p_hold_duration = 1.0,
    text_reveal_duration = 1.2,
    hold_duration = 2.0,
    fade_out_duration = 0.8,
    t1 = 0, t2 = 0, t3 = 0, t4 = 0, t5 = 0,
    sub_text = "Made By jc & Rixz",
    sub_font_size = 50,
    sub_animation_duration = 1.5,
    sub_hold_duration = 0,
    sub_fade_duration = 0,
    s1 = 0, s2 = 0, s3 = 0, s4 = 0,
    font_size = 260,
    letter_spacing = -30,
    main_width_map = {},
    mono_width = 136,
    sub_width_map = {},
    rest_text = "LOODLINE",
    p_width = 0,
    rest_width = 0,
    num_gaps = 0,
    full_text_width = 0,
    rest_chars = {},
    rest_char_widths = {},
    kerning_map = {},
    sub_text_width = 0,
    mid_r = 0, mid_g = 0, mid_b = 0,
    white_r = 255, white_g = 255, white_b = 255,
    start_time = 0,
}

-- Calculate animation phase breakpoints
animation_state.t1 = animation_state.fade_in_duration
animation_state.t2 = animation_state.t1 + animation_state.p_hold_duration
animation_state.t3 = animation_state.t2 + animation_state.text_reveal_duration
animation_state.t4 = animation_state.t3 + animation_state.hold_duration
animation_state.t5 = animation_state.t4 + animation_state.fade_out_duration

animation_state.sub_fade_duration = animation_state.fade_out_duration
animation_state.s1 = animation_state.t3
animation_state.s2 = animation_state.s1 + animation_state.sub_animation_duration
animation_state.s3 = animation_state.t4
animation_state.s4 = animation_state.s3 + animation_state.sub_fade_duration

-- Load animation fonts
Renderer.LoadFontFromFile("Segu", "Segoeuib", animation_state.font_size, false)
Renderer.LoadFontFromFile("SeguSmall", "Segoeuib", animation_state.sub_font_size, true)

-- Main animation font width map
animation_state.main_width_map = {
  [' '] = 72, ['!'] = 85, ['"'] = 128, ['#'] = 154, ['$'] = 150,
  ['%'] = 225, ['&'] = 221, ['\'']= 76, ['(']= 96, [')']= 96,
  ['*'] = 118, ['+'] = 184, [','] = 70, ['-'] = 105, ['.'] = 70,
  ['/'] = 115, ['0'] = 150, ['1'] = 150, ['2'] = 150, ['3'] = 150,
  ['4'] = 150, ['5'] = 150, ['6'] = 150, ['7'] = 150, ['8'] = 150,
  ['9'] = 150, [':'] = 70, [';'] = 70, ['<'] = 184, ['='] = 184,
  ['>'] = 184, ['?'] = 114, ['@'] = 248, ['A'] = 183, ['B'] = 167,
  ['C'] = 162, ['D'] = 192, ['E'] = 138, ['F'] = 135, ['G'] = 185,
  ['H'] = 199, ['I'] = 82,  ['J'] = 116, ['K'] = 169, ['L'] = 133,
  ['M'] = 249, ['N'] = 205, ['O'] = 197, ['P'] = 160, ['Q'] = 197,
  ['R'] = 170, ['S'] = 146, ['T'] = 152, ['U'] = 188, ['V'] = 173,
  ['W'] = 261, ['X'] = 170, ['Y'] = 158, ['Z'] = 158, ['['] = 96,
  ['\\']= 113,[']'] = 96, ['^'] = 184, ['_'] = 108, ['`'] = 82,
  ['a'] = 140, ['b'] = 161, ['c'] = 125, ['d'] = 161, ['e'] = 141,
  ['f'] = 100, ['g'] = 31, ['h'] = 157, ['i'] = 74,  ['j'] = 74,
  ['k'] = 145, ['l'] = 74,  ['m'] = 238, ['n'] = 157, ['o'] = 159,
  ['p'] = 161, ['q'] = 161, ['r'] = 103, ['s'] = 114, ['t'] = 101,
  ['u'] = 157, ['v'] = 141, ['w'] = 207, ['x'] = 144, ['y'] = 140,
  ['z'] = 125, ['{'] = 96,  ['|'] = 85,  ['}'] = 96,  ['~'] = 184,
}

-- Subtitle animation font width map
animation_state.sub_width_map = {
  [' '] = 14, ['!'] = 16, ['"'] = 25, ['#'] = 30, ['$'] = 29, ['%'] = 43, ['&'] = 42,
  ['\''] = 15, ['('] = 19, [')'] = 18, ['*'] = 23, ['+'] = 35, [','] = 14, ['-'] = 20,
  ['.'] = 14, ['/'] = 25, ['0'] = 29, ['1'] = 29, ['2'] = 29, ['3'] = 29, ['4'] = 29,
  ['5'] = 29, ['6'] = 29, ['7'] = 29, ['8'] = 29, ['9'] = 29, [':'] = 14, [';'] = 14,
  ['<'] = 35, ['='] = 35, ['>'] = 35, ['?'] = 22, ['@'] = 48, ['A'] = 35, ['B'] = 32,
  ['C'] = 31, ['D'] = 37, ['E'] = 27, ['F'] = 26, ['G'] = 36, ['H'] = 38, ['I'] = 16,
  ['J'] = 22, ['K'] = 34, ['L'] = 26, ['M'] = 48, ['N'] = 40, ['O'] = 38, ['P'] = 31,
  ['Q'] = 40, ['R'] = 34, ['S'] = 28, ['T'] = 29, ['U'] = 36, ['V'] = 34, ['W'] = 50,
  ['X'] = 33, ['Y'] = 31, ['Z'] = 30, ['['] = 18, ['\\'] = 24, [']'] = 18, ['^'] = 35,
  ['_'] = 21, ['`'] = 16, ['a'] = 27, ['b'] = 31, ['c'] = 24, ['d'] = 31, ['e'] = 27,
  ['f'] = 20, ['g'] = 31, ['h'] = 30, ['i'] = 14, ['j'] = 18, ['k'] = 29, ['l'] = 14,
  ['m'] = 46, ['n'] = 30, ['o'] = 31, ['p'] = 31, ['q'] = 31, ['r'] = 20, ['s'] = 22,
  ['t'] = 19, ['u'] = 30, ['v'] = 28, ['w'] = 40, ['x'] = 28, ['y'] = 29, ['z'] = 24,
  ['{'] = 18, ['|'] = 16, ['}'] = 18, ['~'] = 35,
}

-- Calculate main animation text dimensions
animation_state.p_width = ApproxTextWidthAnimation("B", animation_state.main_width_map) -- Width of 'B'
animation_state.rest_width = ApproxTextWidthAnimation(animation_state.rest_text, animation_state.main_width_map) -- Width of 'LOODLINE'
animation_state.num_gaps = #animation_state.rest_text -- Number of gaps between letters in "LOODLINE"
animation_state.full_text_width = animation_state.p_width + animation_state.rest_width + animation_state.letter_spacing * animation_state.num_gaps -- Total width of "BLOODLINE" with kerning

-- Individual glyph data for "LOODLINE" for animation
for i = 1, #animation_state.rest_text do
  animation_state.rest_chars[i] = animation_state.rest_text:sub(i,i)
  animation_state.rest_char_widths[i] = animation_state.main_width_map[animation_state.rest_chars[i]] or animation_state.mono_width
end

-- Kerning overrides per-pair for the main animation text
animation_state.kerning_map = {
    ["LO"] = -10,
    ["OO"] = -15,
    ["OD"] = -20,
    ["DL"] = -20,  -- pull D and L 20px closer
    ["NE"] = -30,  -- pull N and E 25px closer
}

-- Subtitle animation text width for centering
animation_state.sub_text_width = ApproxTextWidthAnimation(animation_state.sub_text, animation_state.sub_width_map)

-- Mid and white colors for animation
local c1, c2 = Color(255,0,0,255), Color(129,0,0,255)
animation_state.mid_r, animation_state.mid_g, animation_state.mid_b = math.floor((c1.r + c2.r)*0.5), math.floor((c1.g + c2.g)*0.5), math.floor((c1.b + c2.b)*0.5)

animation_state.white_r = 255
animation_state.white_g = 255
animation_state.white_b = 255


animation_state.start_time = Globals.GetCurrentTime()

-- Initialize damage indicator kerning map
damage_indicator_state.kerning_map_ui = {
    ["LO"] = -2, ["OO"] = -3, ["NE"] = -2, ["LI"] = -1,
    [damage_indicator_state.fake_victim_name .. " in the " ] = -15,
    [damage_indicator_state.fake_hitbox_name .. " for "] = -15,
    [":".. damage_indicator_state.fake_health_left] = -15,
}

-- Sound runtime state (transient data for active sound indicators and processed sounds)
local sound_runtime_state = {
    active_sound_indicators = {},
    processed_sounds = {},
    -- Add other runtime-specific sound data here if needed
}

-- Helper function: returns a unique font name for the given size, loading it if necessary
local function GetFontForSize(size)
    if font_state.fonts[size] then
        return font_state.fonts[size]
    else
        local fontName = "Tahoma" .. size
        Renderer.LoadFontFromFile(fontName, "Tahoma", size, true)
        font_state.fonts[size] = fontName
        return fontName
    end
end

-- Initial font load: use dynamic font based on gui_options.font_size
font_state.current_font_size = gui_options.font_size
font_state.current_font_name = GetFontForSize(font_state.current_font_size)

-- Function to update the UI font if the slider value changes
local function UpdateFont()
    local new_font_size = gui_options.font_size
    if new_font_size ~= font_state.current_font_size then
        font_state.current_font_size = new_font_size
        font_state.current_font_name = GetFontForSize(font_state.current_font_size)
        -- Update dependent UI element sizes immediately
        watermark_state.innerHeight = font_state.current_font_size + watermark_state.padding
        damage_indicator_state.BOX_HEIGHT = font_state.current_font_size + damage_indicator_state.INNER_BOX_PADDING
        team_dmg_tracker_state.line_height = font_state.current_font_size + 2
    end
end

-- Helper function to trim leading and trailing whitespace from a string
local function trim_whitespace(s)
    if type(s) ~= 'string' then return s end
    return s:match("^%s*(.-)%s*$") or ""
end

local widthMapUI = {
  [' '] = 4, ['!'] = 4, ['"'] = 5, ['#'] = 9, ['$'] = 7, ['%'] = 13, ['&'] = 9,
  ['\''] = 3, ['('] = 5, [')'] = 5, ['*'] = 7, ['+'] = 9, [','] = 4, ['-'] = 5,
  ['.'] = 4, ['/'] = 5, ['0'] = 7, ['1'] = 7, ['2'] = 7, ['3'] = 7, ['4'] = 7,
  ['5'] = 7, ['6'] = 7, ['7'] = 7, ['8'] = 7, ['9'] = 7, [':'] = 5, [';'] = 5,
  ['<'] = 9, ['='] = 9, ['>'] = 9, ['?'] = 6, ['@'] = 12, ['A'] = 8, ['B'] = 8,
  ['C'] = 8, ['D'] = 9, ['E'] = 7, ['F'] = 7, ['G'] = 9, ['H'] = 9, ['I'] = 5,
  ['J'] = 5, ['K'] = 8, ['L'] = 6, ['M'] = 10, ['N'] = 9, ['O'] = 9, ['P'] = 7,
  ['Q'] = 9, ['R'] = 8, ['S'] = 7, ['T'] = 8, ['U'] = 9, ['V'] = 8, ['W'] = 12,
  ['X'] = 8, ['Y'] = 7, ['Z'] = 7, ['['] = 5, ['\\'] = 5, [']'] = 5, ['^'] = 9,
  ['_'] = 7, ['`'] = 7, ['a'] = 7, ['b'] = 7, ['c'] = 6, ['d'] = 7, ['e'] = 7,
  ['f'] = 4, ['g'] = 7, ['h'] = 7, ['i'] = 3, ['j'] = 4, ['k'] = 6, ['l'] = 3,
  ['m'] = 11, ['n'] = 7, ['o'] = 7, ['p'] = 7, ['q'] = 7, ['r'] = 5, ['s'] = 6,
  ['t'] = 4, ['u'] = 7, ['v'] = 6, ['w'] = 10, ['x'] = 6, ['y'] = 6, ['z'] = 6,
  ['{'] = 6, ['|'] = 5, ['}'] = 6, ['~'] = 9,
}

-- Returns text width (Plaguecheat doesnt have a GetTextSize function) for UI elements
local monoW_ui = 8  -- whatever your new font’s character width is (this is an approximation)
local function ApproxTextWidthUI(txt)
    local totalWidth = 0
    local text_to_measure = tostring(txt or "")
    for i = 1, #text_to_measure do
        local ch = text_to_measure:sub(i, i)
        totalWidth = totalWidth + (widthMapUI[ch] or monoW_ui)
    end
    return totalWidth * (font_state.current_font_size / 13)
end

-- Returns the diffnret segments width for UI elements
local function GetTotalSegmentWidthUI(segments)
    local total = 0
    for _, segment in ipairs(segments) do
        -- Added safety check for nil text and ensure it's a string
        local text_to_measure = tostring(segment.text or "")
        total = total + ApproxTextWidthUI(text_to_measure)
    end
    return total
end

-- Watermark Drawing the Different colored text (Taking the rect dimensions and the segments that should be used for the watermark)
local function DrawColoredTextSegmentsCentered(segments, rectX, rectY, rectW, rectH)
    local totalW = GetTotalSegmentWidthUI(segments)
    local startX = rectX + (rectW - totalW) / 2
    local textY = rectY + ((rectH - (font_state.current_font_size + 1)) + watermark_state.text_vertical_offset) / 2 -- Adjust Y based on current font size

    for i, segment in ipairs(segments) do -- 'i' is the index, which is used for dynamic coloring
        -- DO NOT trim text for watermark
        local text = tostring(segment.text or "") -- Ensure text is a string
        local color = Color(0,0,0) -- Default for safety, will be overwritten

        -- Apply theme colors dynamically based on segment index
        if i == 1 then -- PLAGUE
            color = GUI.colors.text
        elseif i == 2 then -- CHEAT
            color = GUI.colors.headerAccent
        elseif i == 3 or i == 5 or i == 7 then -- Separators
            color = GUI.colors.text -- Use main text color for separators
            color = Color(color.r, color.g, color.b, math.floor(color.a * 0.6)) -- Make it grey-ish by reducing alpha
        elseif i == 4 or i == 6 or i == 8 then -- Username, FPS, Ping
            color = GUI.colors.text
        end

        for j = 1, #text do
            local char = text:sub(j, j)
            Renderer.DrawText(font_state.current_font_name, char, Vector2D(startX, textY), false, false, color)

            -- Calculate the width of the current character
            local charWidth = ApproxTextWidthUI(char)

            -- Apply kerning adjustment if the next character exists
            if j < #text then
                local nextChar = text:sub(j + 1, j + 1)
                local pair = char .. nextChar
                local kerningAdjustment = watermark_state.kerning_map[pair] or 0
                startX = startX + charWidth + kerningAdjustment
            else
                startX = startX + charWidth
            end
        end
    end
end

-- Helper function -- Drawing Filled Rects (Taking x, y, height, width and color)
local function DrawRect(x, y, width, height, color)
    Renderer.DrawRectFilled(Vector2D(x, y), Vector2D(x + width, y + height), color)
end

local function AddDamageIndicator(victimName, hitboxname, nDamageHp, healthLeft) -- adding a dmg indicator to the list for rendering from the player_hurt game event
    victimName = victimName or "Unknown"
    hitboxname = hitboxname or "Unknown"
    nDamageHp = nDamageHp or 0
    healthLeft = healthLeft or 0
    local creationTime = Globals.GetCurrentTime() or 0

    if damage_indicator_state.count >= damage_indicator_state.MAX_INDICATORS then
        -- Shift existing indicators up
        for i = 1, damage_indicator_state.MAX_INDICATORS - 1 do
            damage_indicator_state.indicators[i] = damage_indicator_state.indicators[i + 1]
        end
        damage_indicator_state.count = damage_indicator_state.MAX_INDICATORS - 1
    end

    damage_indicator_state.count = damage_indicator_state.count + 1
    damage_indicator_state.indicators[damage_indicator_state.count] = {
        victimName = victimName,
        hitboxname = hitboxname,
        nDamageHp = nDamageHp,
        healthLeft = healthLeft,
        createdAt = creationTime, -- Use the fetched time
    }
end

local function GetEnemyAndVictimNames(event) -- geting the names of the diffrent players the enemy and the victem
    local playerControllerAttacker = event:GetPlayerController("attacker")
    local playerControllerVictim = event:GetPlayerController("userid")
    local playerHitGroup = event:GetInt("hitgroup") or -1 -- Default to -1 if nil

    local victimName = "Unknown"
    -- Use the default "Unknown" from hitbox_names if lookup fails
    local hitboxname = damage_indicator_state.hitbox_names[playerHitGroup] or damage_indicator_state.hitbox_names[-1]

    -- Check victim validity
    if playerControllerVictim and playerControllerVictim.m_sSanitizedPlayerName then
        victimName = playerControllerVictim.m_sSanitizedPlayerName
    end
    return victimName, hitboxname
end

local function OnFireGameEvent(event) -- the function that is used for the cheat callback, (this is getting run when a game event triggers)
    if not event or not event.GetName then return end
    if not Globals.IsConnected() then return end
    if not gui_options.hitlogs_enable then return end
    local eventName = event:GetName()

    if eventName == "player_hurt" then
        local attackerController = event:GetPlayerController("attacker")
        if attackerController == nil then
            return -- Exit if attackerController is nil
        end

        -- Check if the attacker is the local player more robustly
        if attackerController.m_bIsLocalPlayerController ~= nil and attackerController.m_bIsLocalPlayerController then
            -- Get damage and health, providing default 0 if nil
            local nDamageHp = event:GetInt("dmg_health") or 0
            local healthLeft = event:GetInt("health") or 0

            -- Get victim name and hitbox name
            local victimName, hitboxname = GetEnemyAndVictimNames(event)

            -- Only add indicator if we have valid damage (optional, but good practice)
            if nDamageHp > 0 then
                 AddDamageIndicator(victimName, hitboxname, nDamageHp, healthLeft)
            end
        end
    end
end


local function SetFakeHitPreviewActive(active) -- setting the fakehotpreview to be active
    damage_indicator_state.fake_hit_preview_active = active
end

local function DrawDamageIndicator(indicator, drawBaseY, fadeAlpha, currentAlignment, draggableAreaX, draggableAreaWidth, useSpecificWidth, isHovered)
    -- Use defaults directly here as a fallback, though AddDamageIndicator should handle it
    local victimName = indicator.victimName or "Player"
    local hitboxname = indicator.hitboxname or "body"
    local nDamageHp = indicator.nDamageHp or 0
    local healthLeft = indicator.healthLeft or 0

    -- Convert to string safely and trim whitespace for Damage Indicators
    local victimNameStr = trim_whitespace(tostring(victimName))
    local hitboxnameStr = trim_whitespace(tostring(hitboxname))
    local nDamageHpStr = tostring(nDamageHp)
    local healthLeftStr = trim_whitespace(tostring(healthLeft))

    -- text width for both dynamic and static parts for the dmg indicator
    local widthFixed1 = ApproxTextWidthUI(damage_indicator_state.segment_fixed_1)
    local widthDynamic2 = ApproxTextWidthUI(victimNameStr)
    local widthFixed3 = ApproxTextWidthUI(damage_indicator_state.segment_fixed_2)
    local widthDynamic4 = ApproxTextWidthUI(hitboxnameStr)
    local widthFixed5 = ApproxTextWidthUI(damage_indicator_state.segment_fixed_3)
    local widthDynamic6 = ApproxTextWidthUI(nDamageHpStr)
    local widthFixed7 = ApproxTextWidthUI(damage_indicator_state.segment_fixed_4)
    local widthFixed8 = ApproxTextWidthUI(damage_indicator_state.segment_fixed_5)
    local widthFixed9 = ApproxTextWidthUI(damage_indicator_state.segment_fixed_6)
    local widthDynamic10 = ApproxTextWidthUI(healthLeftStr)
    local widthFixed11 = ApproxTextWidthUI(damage_indicator_state.segment_fixed_7)

    local dynamicInnerBoxWidth = widthFixed1 + widthDynamic2 + widthFixed3 + widthDynamic4 +
                                 widthFixed5 + widthDynamic6 + widthFixed7 + widthFixed8 +
                                 widthFixed9 + widthDynamic10 + widthFixed11 + damage_indicator_state.INNER_BOX_PADDING * 2

    local innerBoxHeight = damage_indicator_state.BOX_HEIGHT

    local finalInnerBoxWidthToDraw = useSpecificWidth or dynamicInnerBoxWidth
    -- colors - Ensure alpha is valid
    local safeFadeAlpha = math.floor(math.max(0, math.min(255, fadeAlpha or 0)) + 0.5) -- Ensure alpha is 0-255

    local outlineColor = Color(GUI.colors.windowOutline.r, GUI.colors.windowOutline.g, GUI.colors.windowOutline.b, safeFadeAlpha)
    local innerColor = Color(GUI.colors.window.r, GUI.colors.window.g, GUI.colors.window.b, safeFadeAlpha)
    local innerColor = Color(GUI.colors.window.r, GUI.colors.window.g, GUI.colors.window.b, safeFadeAlpha)
    if isHovered then
    innerColor = Color(GUI.colors.buttonHover.r, GUI.colors.buttonHover.g, GUI.colors.buttonHover.b, safeFadeAlpha)
    end
    local whiteColor = Color(GUI.colors.text.r, GUI.colors.text.g, GUI.colors.text.b, safeFadeAlpha)
    local greyColor = Color(GUI.colors.text.r, GUI.colors.text.g, GUI.colors.text.b, math.floor(safeFadeAlpha * 0.6))
    local blueColor = Color(GUI.colors.headerAccent.r, GUI.colors.headerAccent.g, GUI.colors.headerAccent.b, safeFadeAlpha)

    local innerDrawX = 0
    local widthForAlignment = draggableAreaWidth

    -- Calculate base X position depending on alignment
    if currentAlignment == 'left' then
        innerDrawX = draggableAreaX
    elseif currentAlignment == 'center' then
        -- Center based on the potentially wider width (`useSpecificWidth` if provided)
        innerDrawX = draggableAreaX + (widthForAlignment / 2) - (finalInnerBoxWidthToDraw / 2)
    elseif currentAlignment == 'right' then
        innerDrawX = draggableAreaX + widthForAlignment - finalInnerBoxWidthToDraw
    end

    -- clamp so the outer box (innerDrawX - offset … +boxWidth + offset) stays on-screen
    do
    local screenW = Renderer.GetScreenSize().x
    local offset  = damage_indicator_state.OUTER_OUTLINE_OFFSET
    -- innerDrawX must be in [offset, screenW - boxWidth - offset]
    local minX = offset
    local maxX = screenW - finalInnerBoxWidthToDraw - offset
    innerDrawX = math.max(minX, math.min(maxX, innerDrawX))
    end

    local outerDrawX = innerDrawX - damage_indicator_state.OUTER_OUTLINE_OFFSET
    local outerDrawY = drawBaseY - damage_indicator_state.OUTER_OUTLINE_OFFSET
    local outerWidth = finalInnerBoxWidthToDraw + damage_indicator_state.OUTER_OUTLINE_OFFSET * 2
    local outerHeight = innerBoxHeight + damage_indicator_state.OUTER_OUTLINE_OFFSET * 2
    --- Dmg indicator rendering -- Add Blue line here? (No blue line added yet)
    DrawRect(outerDrawX, outerDrawY, outerWidth, outerHeight, outlineColor)
    DrawRect(innerDrawX, drawBaseY, finalInnerBoxWidthToDraw, innerBoxHeight, innerColor)

    -- Calculate text start position to center the actual text content within the potentially wider box
    local totalTextWidthOfThisIndicator = dynamicInnerBoxWidth - damage_indicator_state.INNER_BOX_PADDING * 2 -- Use the actual text width
    local textStartX = innerDrawX + (finalInnerBoxWidthToDraw / 2) - (totalTextWidthOfThisIndicator / 2) + 8 -- Center based on actual text width
    local textY = drawBaseY + (innerBoxHeight / 2) - (font_state.current_font_size / 2) -- Adjust text Y based on current font size
-- Kerning map for custom character pair spacing
    local currentTextX = textStartX
    -- Draw text segments
    Renderer.DrawText(font_state.current_font_name, damage_indicator_state.segment_fixed_1, Vector2D(currentTextX, textY), false, false, whiteColor)
    currentTextX = currentTextX + widthFixed1

    -- Apply kerning for victimName
    local pair = damage_indicator_state.segment_fixed_1:sub(-1) .. victimNameStr:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, victimNameStr, Vector2D(currentTextX, textY), false, false, blueColor)
    currentTextX = currentTextX + widthDynamic2

    -- Apply kerning for " in the"
    pair = victimNameStr:sub(-1) .. damage_indicator_state.segment_fixed_2:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, damage_indicator_state.segment_fixed_2, Vector2D(currentTextX, textY), false, false, whiteColor)
    currentTextX = currentTextX + widthFixed3

    -- Apply kerning for hitboxname
    pair = damage_indicator_state.segment_fixed_2:sub(-1) .. hitboxnameStr:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, hitboxnameStr, Vector2D(currentTextX, textY), false, false, blueColor)
    currentTextX = currentTextX + widthDynamic4

    -- Apply kerning for "for"
    pair = hitboxnameStr:sub(-1) .. damage_indicator_state.segment_fixed_3:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, damage_indicator_state.segment_fixed_3, Vector2D(currentTextX, textY), false, false, whiteColor)
    currentTextX = currentTextX + widthFixed5

    -- Apply kerning for nDamageHp
    pair = damage_indicator_state.segment_fixed_3:sub(-1) .. nDamageHpStr:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, nDamageHpStr, Vector2D(currentTextX, textY), false, false, blueColor)
    currentTextX = currentTextX + widthDynamic6

    -- Apply kerning for " HP"
    pair = nDamageHpStr:sub(-1) .. damage_indicator_state.segment_fixed_4:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, damage_indicator_state.segment_fixed_4, Vector2D(currentTextX, textY), false, false, whiteColor)
    currentTextX = currentTextX + widthFixed7

    -- Apply kerning for " | "
    pair = damage_indicator_state.segment_fixed_4:sub(-1) .. damage_indicator_state.segment_fixed_5:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, damage_indicator_state.segment_fixed_5, Vector2D(currentTextX, textY), false, false, greyColor)
    currentTextX = currentTextX + widthFixed8

    -- Apply kerning for "Health Remaining:"
    pair = damage_indicator_state.segment_fixed_5:sub(-1) .. damage_indicator_state.segment_fixed_6:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, damage_indicator_state.segment_fixed_6, Vector2D(currentTextX, textY), false, false, whiteColor)
    currentTextX = currentTextX + widthFixed9

    -- Apply kerning for healthLeft
    pair = damage_indicator_state.segment_fixed_6:sub(-1) .. healthLeftStr:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, healthLeftStr, Vector2D(currentTextX, textY), false, false, blueColor)
    currentTextX = currentTextX + widthDynamic10

    -- Apply kerning for " HP"
    pair = healthLeftStr:sub(-1) .. damage_indicator_state.segment_fixed_7:sub(1, 1)
    currentTextX = currentTextX + (damage_indicator_state.kerning_map_ui[pair] or 0)

    Renderer.DrawText(font_state.current_font_name, damage_indicator_state.segment_fixed_7, Vector2D(currentTextX, textY), false, false, whiteColor)
end


local function GetLocalPlayerPing() -- Loops thru every enity and returns the localplayercontroller and then gets the Ping (Getting players ping)
    local highest_entity_index = Entities.GetHighestEntityIndex() or 0

    for i = 1, highest_entity_index do
        local entity = Entities.GetEntityFromIndex(i)
        -- Added check for entity validity and m_bIsLocalPlayerController existence
        if entity and entity.m_bIsLocalPlayerController then
            return entity.m_iPing or 0
        end
    end
    return 0
end


-- New OnRenderer function for Watermark
local function OnRendererWatermark()
    -- Call UpdateFont at the beginning of a rendering function
    UpdateFont()

    -- Only render if intro is not active
    if global_ui_state.isIntroActive then return end

    if not gui_options.watermark_enable then return end
    local mousePos = Input.GetCursorPos()
    local mouseDown = Input.GetKeyDown(0x01)
    local screenSize = Renderer.GetScreenSize()
    local screenCenterX = screenSize.x / 2
    local screenCenterY = screenSize.y / 2
    local menuOpen = Input.IsMenuOpen()
    local controlDown = Input.GetKeyDown(global_ui_state.CONTROL_KEY)

    -- Use a local state for mouse released specific to this renderer
    local mouseReleased = watermark_state.prev_mouse_down and not mouseDown

    -- FPS Calculation
    local current_frame_time = Globals.GetFrameTime() or (1/60)
    if watermark_state.avg_frame_time == 0.0 then
        watermark_state.avg_frame_time = current_frame_time
    else
        watermark_state.avg_frame_time = watermark_state.smoothing_alpha * current_frame_time + (1 - watermark_state.smoothing_alpha) * watermark_state.avg_frame_time
    end
    watermark_state.calculated_fps = (watermark_state.avg_frame_time > 0.000001) and (1.0 / watermark_state.avg_frame_time) or 0.0

    -- Ping Calculation
    local playerPing = GetLocalPlayerPing()
    local pingText = string.format("%.0f MS", playerPing)
    local fpsText = string.format("%.0f FPS", watermark_state.calculated_fps)

    -- Update dynamic watermark segments
    local separator1Index = #watermark_state.base_segments + 1
    local fpsSegmentIndex = #watermark_state.base_segments + 2
    local separator2Index = #watermark_state.base_segments + 3
    local pingSegmentIndex = #watermark_state.base_segments + 4

    -- Ensure indices are within bounds before assignment
    if separator1Index <= #watermark_state.display_segments then watermark_state.display_segments[separator1Index] = { text = " | ", color = Color(150, 150, 150) } end
    if fpsSegmentIndex <= #watermark_state.display_segments then watermark_state.display_segments[fpsSegmentIndex] = { text = fpsText, color = Color(255, 255, 255) } end
    if separator2Index <= #watermark_state.display_segments then watermark_state.display_segments[separator2Index] = { text = " | ", color = Color(150, 150, 150) } end
    if pingSegmentIndex <= #watermark_state.display_segments then watermark_state.display_segments[pingSegmentIndex] = { text = pingText, color = Color(255, 255, 255) } end

    -- Watermark Dimensions Calculation
    local watermark_textW = GetTotalSegmentWidthUI(watermark_state.display_segments)
    local watermark_innerWidth = watermark_textW + watermark_state.padding * 2
    -- Recalculate watermark height based on current font size
    watermark_state.innerHeight = font_state.current_font_size + watermark_state.padding
    local watermark_outerPadding = 5
    local watermark_outerWidth = watermark_innerWidth + watermark_outerPadding * 2
    local watermark_outerHeight = watermark_state.innerHeight + watermark_outerPadding * 2

    -- Watermark Click Area
    local wmClickAreaX = watermark_state.x - watermark_outerPadding
    local wmClickAreaY = watermark_state.y - watermark_outerPadding
    local wmClickAreaW = watermark_outerWidth
    local wmClickAreaH = watermark_outerHeight

    -- Watermark Dragging Logic
    local isMouseInWatermarkRect = menuOpen and
                                   mousePos.x >= wmClickAreaX and mousePos.x <= wmClickAreaX + wmClickAreaW and
                                   mousePos.y >= wmClickAreaY and mousePos.y <= wmClickAreaY + wmClickAreaH

    -- Check if any other element is being dragged before starting watermark drag
    if menuOpen and mouseDown and not global_ui_state.isTeamDmgDragging and not global_ui_state.isDamageIndicatorDragging and not GUI.draggingWindow and not global_ui_state.isSpecDragging and not global_ui_state.isWatermarkDragging and isMouseInWatermarkRect then
        global_ui_state.isWatermarkDragging = true
        watermark_state.drag_offset_x = mousePos.x - watermark_state.x
        watermark_state.drag_offset_y = mousePos.y - watermark_state.y
    end

    if (global_ui_state.isWatermarkDragging and mouseReleased) or (global_ui_state.isWatermarkDragging and not menuOpen) then
        if controlDown then
             -- Snapping Logic (Watermark)
             local bestTargetX = nil
             local minDistX = global_ui_state.SNAP_DISTANCE_THRESHOLD + 1
             local currentOuterX = watermark_state.x - watermark_outerPadding

             -- Snap Left
             local distLeft = math.abs(currentOuterX - 0)
             if distLeft <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
                 bestTargetX = watermark_outerPadding
                 minDistX = distLeft
             end
             -- Snap Center
             local currentOuterCenterX = currentOuterX + watermark_outerWidth / 2
             local distCenterX = math.abs(currentOuterCenterX - screenCenterX)
             if distCenterX <= global_ui_state.SNAP_DISTANCE_THRESHOLD and distCenterX < minDistX then
                  bestTargetX = screenCenterX - watermark_outerWidth / 2 + watermark_outerPadding
                  minDistX = distCenterX
              end
             -- Snap Right
             local currentOuterRightX = currentOuterX + watermark_outerWidth
             local distRight = math.abs(currentOuterRightX - screenSize.x)
             if distRight <= global_ui_state.SNAP_DISTANCE_THRESHOLD and distRight < minDistX then
                  bestTargetX = screenSize.x - watermark_outerWidth + watermark_outerPadding
                  minDistX = distRight
              end

             if bestTargetX ~= nil then watermark_state.x = bestTargetX end
        end
        global_ui_state.isWatermarkDragging = false
        watermark_state.saved_x = watermark_state.x
        watermark_state.saved_y = watermark_state.y
    end

    if global_ui_state.isWatermarkDragging then
        local potentialNewWatermarkX = mousePos.x - watermark_state.drag_offset_x
        local potentialNewWatermarkY = mousePos.y - watermark_state.drag_offset_y

        -- Clamping Logic (Watermark)
        local minX = watermark_outerPadding
        local maxX = screenSize.x - watermark_outerWidth + watermark_outerPadding
        local minY = watermark_outerPadding
        local maxY = screenSize.y - watermark_outerHeight + watermark_outerPadding

        watermark_state.x = math.max(minX, math.min(maxX, potentialNewWatermarkX))
        watermark_state.y = math.max(minY, math.min(maxY, potentialNewWatermarkY))
    else
        -- Restore position if menu is open and not dragging
        if menuOpen then
            watermark_state.x = watermark_state.saved_x
            watermark_state.y = watermark_state.saved_y
        end
    end

    -- Draw Snap Indicators (Watermark)
    if menuOpen and controlDown and global_ui_state.isWatermarkDragging then -- Only show when dragging
         local snapIndicatorWidth = watermark_innerWidth
         local snapIndicatorHeight = watermark_state.innerHeight
         local currentInnerX = watermark_state.x
         local currentInnerY = watermark_state.y
         local currentOuterX = currentInnerX - watermark_outerPadding
         local currentOuterWidth = watermark_outerWidth

         -- Snap Left Preview
         local distLeft = math.abs(currentOuterX - 0)
         if distLeft <= global_ui_state.SNAP_DISTANCE_THRESHOLD then DrawRect(watermark_outerPadding, currentInnerY, snapIndicatorWidth, snapIndicatorHeight, global_ui_state.SNAP_INDICATOR_COLOR) end
         -- Snap Center Preview
         local currentOuterCenterX = currentOuterX + currentOuterWidth / 2
         local distCenterX = math.abs(currentOuterCenterX - screenCenterX)
         if distCenterX <= global_ui_state.SNAP_DISTANCE_THRESHOLD then DrawRect(screenCenterX - snapIndicatorWidth / 2, currentInnerY, snapIndicatorWidth, snapIndicatorHeight, global_ui_state.SNAP_INDICATOR_COLOR) end
         -- Snap Right Preview
         local currentOuterRightX = currentOuterX + currentOuterWidth
         local distRight = math.abs(currentOuterRightX - screenSize.x)
         if distRight <= global_ui_state.SNAP_DISTANCE_THRESHOLD then DrawRect(screenSize.x - currentOuterWidth + watermark_outerPadding, currentInnerY, snapIndicatorWidth, snapIndicatorHeight, global_ui_state.SNAP_INDICATOR_COLOR) end
     end

     --- Watermark Rendering ---
    -- Only render if enabled by checkbox
    if gui_options.watermark_enable then
        DrawRect(watermark_state.x - watermark_outerPadding, watermark_state.y - watermark_outerPadding, watermark_outerWidth, watermark_outerHeight, GUI.colors.windowOutline) -- Outer box
        DrawRect(watermark_state.x, watermark_state.y, watermark_innerWidth, watermark_state.innerHeight, GUI.colors.window)
        if menuOpen and not global_ui_state.isWatermarkDragging and isMouseInWatermarkRect then
            DrawRect(watermark_state.x, watermark_state.y, watermark_innerWidth, watermark_state.innerHeight, GUI.colors.buttonHover)
        end
        if global_ui_state.isWatermarkDragging and not controlDown then
            Renderer.DrawText(font_state.current_font_name, "Hold CTRL To Snap", Vector2D(watermark_state.x, watermark_state.y - 18), false, false, GUI.colors.text)
        elseif global_ui_state.isWatermarkDragging and controlDown then
            Renderer.DrawText(font_state.current_font_name, "SNAPPING", Vector2D(watermark_state.x, watermark_state.y - 18), false, false, GUI.colors.text)
        end
        DrawColoredTextSegmentsCentered(watermark_state.display_segments, watermark_state.x, watermark_state.y, watermark_innerWidth, watermark_state.innerHeight) -- Text
    end
    -- Watermark rendering over --

    -- Update previous mouse state for next frame (only for watermark dragging)
    watermark_state.prev_mouse_down = mouseDown
end


-- New OnRenderer function for Damage Indicators
local function OnRendererDamageIndicators()
    -- Only render if intro is not active
    if global_ui_state.isIntroActive then return end

    if not gui_options.hitlogs_enable then return end
    local mousePos = Input.GetCursorPos()
    local mouseDown = Input.GetKeyDown(0x01)
    local screenSize = Renderer.GetScreenSize()
    local screenCenterX = screenSize.x / 2
    local screenCenterY = screenSize.y / 2
    local menuOpen = Input.IsMenuOpen()
    local controlDown = Input.GetKeyDown(global_ui_state.CONTROL_KEY)

    -- Use a local state for mouse released specific to this renderer
    local mouseReleased = damage_indicator_state.prev_mouse_down and not mouseDown

    -- Damage Indicator Logic
    SetFakeHitPreviewActive(menuOpen) -- Show fake preview only when menu is open

    -- Calculate max width needed for alignment (considering real indicators only)
    local maxRealIndicatorInnerBoxWidth = 0

    -- Calculate width needed for each real damage indicator and find the maximum
    local currentTime = Globals.GetCurrentTime() or 0 -- Get time once for this frame
    local newIndicators = {}
    local newCount = 0

    -- Filter out old indicators AND calculate max width simultaneously for REAL indicators
    for i = 1, damage_indicator_state.count do
        local indicator = damage_indicator_state.indicators[i]
        if indicator then
            local timeAlive = currentTime - (indicator.createdAt or 0)
            if timeAlive < damage_indicator_state.DISPLAY_TIME then
                 -- Keep this indicator
                 newCount = newCount + 1
                 newIndicators[newCount] = indicator

                 -- Calculate its width (trimming applied here)
                 local victimNameStr = trim_whitespace(tostring(indicator.victimName or "Player"))
                 local hitboxnameStr = trim_whitespace(tostring(indicator.hitboxname or "body"))
                 local nDamageHpStr = trim_whitespace(tostring(indicator.nDamageHp or 0))
                 local healthLeftStr = trim_whitespace(tostring(indicator.healthLeft or 0))
                 local totalTextWidth = ApproxTextWidthUI(damage_indicator_state.segment_fixed_1) + ApproxTextWidthUI(victimNameStr) +
                                        ApproxTextWidthUI(damage_indicator_state.segment_fixed_2) + ApproxTextWidthUI(hitboxnameStr) +
                                        ApproxTextWidthUI(damage_indicator_state.segment_fixed_3) + ApproxTextWidthUI(nDamageHpStr) +
                                        ApproxTextWidthUI(damage_indicator_state.segment_fixed_4) + ApproxTextWidthUI(damage_indicator_state.segment_fixed_5) +
                                        ApproxTextWidthUI(damage_indicator_state.segment_fixed_6) + ApproxTextWidthUI(healthLeftStr) +
                                        ApproxTextWidthUI(damage_indicator_state.segment_fixed_7)
                 local dynamicInnerBoxWidth = totalTextWidth + damage_indicator_state.INNER_BOX_PADDING * 2

                 -- Update max width if this indicator is wider
                 if dynamicInnerBoxWidth > maxRealIndicatorInnerBoxWidth then
                     maxRealIndicatorInnerBoxWidth = dynamicInnerBoxWidth
                 end
            end
        end
    end
    -- Update the list and count *after* iterating
    damage_indicator_state.indicators = newIndicators
    damage_indicator_state.count = newCount

    -- Calculate fake preview dimensions independently (trimming applied here)
    local fakeInnerBoxWidth_calc = 0
    -- Adjust fake preview total height based on current font size
    local fakePreviewTotalHeight = font_state.current_font_size + damage_indicator_state.INNER_BOX_PADDING + damage_indicator_state.OUTER_OUTLINE_OFFSET * 2
    if damage_indicator_state.fake_hit_preview_active then
         local fakeVictimNameStr = trim_whitespace(tostring(damage_indicator_state.fake_victim_name))
         local fakeHitboxnameStr = trim_whitespace(tostring(damage_indicator_state.fake_hitbox_name))
         local fakeDamageHpStr = trim_whitespace(tostring(damage_indicator_state.fake_damage_hp))
         local fakeHealthLeftStr = trim_whitespace(tostring(damage_indicator_state.fake_health_left))
         local fakeTotalTextWidth = ApproxTextWidthUI(damage_indicator_state.segment_fixed_1) + ApproxTextWidthUI(fakeVictimNameStr) +
                                    ApproxTextWidthUI(damage_indicator_state.segment_fixed_2) + ApproxTextWidthUI(fakeHitboxnameStr) +
                                    ApproxTextWidthUI(damage_indicator_state.segment_fixed_3) + ApproxTextWidthUI(fakeDamageHpStr) +
                                    ApproxTextWidthUI(damage_indicator_state.segment_fixed_4) + ApproxTextWidthUI(damage_indicator_state.segment_fixed_5) +
                                    ApproxTextWidthUI(damage_indicator_state.segment_fixed_6) + ApproxTextWidthUI(fakeHealthLeftStr) +
                                    ApproxTextWidthUI(damage_indicator_state.segment_fixed_7)
         fakeInnerBoxWidth_calc = fakeTotalTextWidth + damage_indicator_state.INNER_BOX_PADDING * 2
    end

    -- Determine the base drawing position for the real indicator stack
    local realStackDrawX, realStackDrawY = damage_indicator_state.saved_x, damage_indicator_state.saved_y
    local realStackInnerWidth = maxRealIndicatorInnerBoxWidth
    local realStackOuterWidth = realStackInnerWidth + damage_indicator_state.OUTER_OUTLINE_OFFSET * 2
    -- Calculate max real stack height based on current font size
    local maxRealStackHeight = (damage_indicator_state.MAX_INDICATORS * (font_state.current_font_size + damage_indicator_state.INNER_BOX_PADDING + damage_indicator_state.OUTER_OUTLINE_OFFSET * 2)) + ((damage_indicator_state.MAX_INDICATORS > 0) and (damage_indicator_state.MAX_INDICATORS - 1) * damage_indicator_state.SPACING or 0)


    -- Determine the drawing position for the fake preview
    local fakePreviewDrawX, fakePreviewDrawY_base
    if global_ui_state.isDamageIndicatorDragging then
        fakePreviewDrawX = damage_indicator_state.current_fake_preview_drag_x
        fakePreviewDrawY_base = damage_indicator_state.current_fake_preview_drag_y
    else
        fakePreviewDrawX = damage_indicator_state.saved_x
        fakePreviewDrawY_base = damage_indicator_state.saved_y
    end

    local fakePreviewOuterX_calc = 0
    local fakePreviewOuterY_calc = 0
    local fakePreviewOuterW_calc = 0
    local fakePreviewOuterH_calc = 0
    local fakePreviewDrawY = 0

    if damage_indicator_state.fake_hit_preview_active then
        -- The fake preview draws at its base position, not stacked below real indicators
        fakePreviewDrawY = fakePreviewDrawY_base

        -- Calculate the X position based on alignment and the calculated width of the fake indicator
        local fakePreviewPotentialDrawX_inner = 0
        if damage_indicator_state.current_alignment == 'left' then
            fakePreviewPotentialDrawX_inner = fakePreviewDrawX
        elseif damage_indicator_state.current_alignment == 'center' then
             -- Center the fake indicator using its own calculated width
            fakePreviewPotentialDrawX_inner = fakePreviewDrawX + (fakeInnerBoxWidth_calc / 2) - (fakeInnerBoxWidth_calc / 2) -- This simplifies to fakePreviewDrawX
        elseif damage_indicator_state.current_alignment == 'right' then
            -- Align the right edge of the fake indicator with its X position
            fakePreviewPotentialDrawX_inner = fakePreviewDrawX + fakeInnerBoxWidth_calc - fakeInnerBoxWidth_calc -- This simplifies to fakePreviewDrawX
        end

        -- Recalculate outer bounds for the fake preview based on its independent position
        fakePreviewOuterX_calc = fakePreviewDrawX - damage_indicator_state.OUTER_OUTLINE_OFFSET
        fakePreviewOuterY_calc = fakePreviewDrawY - damage_indicator_state.OUTER_OUTLINE_OFFSET
        fakePreviewOuterW_calc = fakeInnerBoxWidth_calc + damage_indicator_state.OUTER_OUTLINE_OFFSET * 2
        fakePreviewOuterH_calc = fakePreviewTotalHeight -- Use the calculated total height
    end

    -- Damage Indicator Dragging Hit Detection (uses fake preview bounds)
    local isMouseInDamageIndicatorRect = false
    if menuOpen and damage_indicator_state.fake_hit_preview_active then
        isMouseInDamageIndicatorRect = mousePos.x >= fakePreviewOuterX_calc and mousePos.x <= fakePreviewOuterX_calc + fakePreviewOuterW_calc and
                                       mousePos.y >= fakePreviewOuterY_calc and mousePos.y <= fakePreviewOuterY_calc + fakePreviewOuterH_calc
    end

    -- Damage Indicator Dragging Logic
    -- Check if any other element is being dragged before starting damage indicator drag
    if menuOpen and mouseDown and not global_ui_state.isTeamDmgDragging and not global_ui_state.isWatermarkDragging and not global_ui_state.isSpecDragging and not GUI.draggingWindow and not global_ui_state.isDamageIndicatorDragging and isMouseInDamageIndicatorRect then
        global_ui_state.isDamageIndicatorDragging = true
        damage_indicator_state.fake_preview_drag_offset_x = mousePos.x - fakePreviewDrawX -- Offset relative to the fake preview's draw X
        damage_indicator_state.fake_preview_drag_offset_y = mousePos.y - fakePreviewDrawY -- Offset relative to the fake preview's draw Y
        -- Initialize current drag position
        damage_indicator_state.current_fake_preview_drag_x = fakePreviewDrawX
        damage_indicator_state.current_fake_preview_drag_y = fakePreviewDrawY
    end

    if global_ui_state.isDamageIndicatorDragging and (mouseReleased or not menuOpen) then
        if controlDown then
            -- Snapping Logic (Fake Preview) - Based on the fake preview's position/width
            local bestTargetInnerX = nil
            local minDistX = global_ui_state.SNAP_DISTANCE_THRESHOLD + 1
            local newAlignment = damage_indicator_state.current_alignment

            local currentFakePreviewInnerX = damage_indicator_state.current_fake_preview_drag_x
            local currentFakePreviewInnerWidth = fakeInnerBoxWidth_calc

            -- Snap Left
            local distLeft = math.abs(currentFakePreviewInnerX - 5) -- Snap to left edge with padding
            if distLeft <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
                bestTargetInnerX = 5
                minDistX = distLeft
                newAlignment = 'left'
            end
            -- Snap Center
            local currentFakePreviewCenterX = currentFakePreviewInnerX + currentFakePreviewInnerWidth / 2
            local distCenterX = math.abs(currentFakePreviewCenterX - screenCenterX)
            if distCenterX <= global_ui_state.SNAP_DISTANCE_THRESHOLD and distCenterX < minDistX then
                 bestTargetInnerX = screenCenterX - fakeInnerBoxWidth_calc / 2
                 minDistX = distCenterX
                 newAlignment = 'center'
             end
             -- Snap Right
            local currentFakePreviewRightX = currentFakePreviewInnerX + currentFakePreviewInnerWidth
            local distRight = math.abs(currentFakePreviewRightX - (screenSize.x - 5)) -- Snap to right edge with padding
            if distRight <= global_ui_state.SNAP_DISTANCE_THRESHOLD and distRight < minDistX then
                 bestTargetInnerX = screenSize.x - fakeInnerBoxWidth_calc - 5
                 minDistX = distRight
                 newAlignment = 'right'
             end

            if bestTargetInnerX ~= nil then damage_indicator_state.current_fake_preview_drag_x = bestTargetInnerX end -- Apply snap to drag position
            damage_indicator_state.current_alignment = newAlignment
        end
        global_ui_state.isDamageIndicatorDragging = false
        -- Save the final drag position as the new saved position for the stack anchor
        -- Clamp savedDamageIndicatorY based on the max real stack height
        local minSavedY = 5
        local maxSavedY = screenSize.y - maxRealStackHeight - 5 -- Add some padding

        damage_indicator_state.saved_x = damage_indicator_state.current_fake_preview_drag_x
        damage_indicator_state.saved_y = math.max(minSavedY, math.min(maxSavedY, damage_indicator_state.current_fake_preview_drag_y)) -- Clamp saved Y
    end

    -- Update current drag position ONLY if currently dragging the fake preview
    if global_ui_state.isDamageIndicatorDragging then
        local potentialNewFakePreviewX = mousePos.x - damage_indicator_state.fake_preview_drag_offset_x
        local potentialNewFakePreviewY = mousePos.y - damage_indicator_state.fake_preview_drag_offset_y

        -- Clamping Logic (Fake Preview Drag Position)
        local clampWidth = fakeInnerBoxWidth_calc + damage_indicator_state.OUTER_OUTLINE_OFFSET * 2 - 5
        local clampHeight = fakePreviewTotalHeight - 5 -- Clamp based on fake preview height

        local minX = 5
        local maxX = screenSize.x - clampWidth
        local minY = 5
        local maxY = screenSize.y - clampHeight - maxRealStackHeight

        -- Clamp X and determine alignment based on clamped X
        local clampedX = math.max(minX, math.min(maxX, potentialNewFakePreviewX))
        damage_indicator_state.current_fake_preview_drag_x = clampedX -- Update drag position
        damage_indicator_state.current_fake_preview_drag_y = math.max(minY, math.min(maxY, potentialNewFakePreviewY)) -- Update drag position
    end


    -- Draw Snap Indicators (Damage Indicators)
    if menuOpen and controlDown and global_ui_state.isDamageIndicatorDragging then -- Only show when dragging
        local snapIndicatorInnerWidth = fakeInnerBoxWidth_calc -- Snap preview uses fake indicator's width
        local snapIndicatorInnerHeight = font_state.current_font_size + damage_indicator_state.INNER_BOX_PADDING -- Adjust height based on current font size
        local currentInnerY_base = fakePreviewDrawY -- Use the fake preview's draw Y

        local currentFakePreviewInnerX = fakePreviewDrawX -- Use the fake preview's draw position for snap checks
        local currentFakePreviewInnerWidth = fakeInnerBoxWidth_calc

        -- Snap Left Preview
        local distLeft = math.abs(currentFakePreviewInnerX - 5) -- Snap to left edge with padding
        if distLeft <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
             DrawRect(5, currentInnerY_base, snapIndicatorInnerWidth, snapIndicatorInnerHeight, global_ui_state.SNAP_INDICATOR_COLOR) -- Draw at screen edge
        end
        -- Snap Center Preview
        local currentFakePreviewCenterX = currentFakePreviewInnerX + currentFakePreviewInnerWidth / 2
        local distCenterX = math.abs(currentFakePreviewCenterX - screenCenterX)
        if distCenterX <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
             local snapTargetDrawX = screenCenterX - snapIndicatorInnerWidth / 2
             DrawRect(snapTargetDrawX, currentInnerY_base, snapIndicatorInnerWidth, snapIndicatorInnerHeight, global_ui_state.SNAP_INDICATOR_COLOR)
         end
        -- Snap Right Preview
        local currentFakePreviewRightX = currentFakePreviewInnerX + currentFakePreviewInnerWidth
        local distRight = math.abs(currentFakePreviewRightX - (screenSize.x - 5)) -- Snap to right edge with padding
        if distRight <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
             local snapTargetDrawX = screenSize.x - snapIndicatorInnerWidth - 5
             DrawRect(snapTargetDrawX, currentInnerY_base, snapIndicatorInnerWidth, snapIndicatorInnerHeight, global_ui_state.SNAP_INDICATOR_COLOR)
        end
    end


    --- Damage Indicator Rendering ---
    -- Draw Real Indicators (only when menu is closed)
    if not menuOpen then
        local currentDrawY = realStackDrawY -- Start drawing from the saved base Y for real indicators
        for i = 1, damage_indicator_state.count do
            local indicator = damage_indicator_state.indicators[i]
            if indicator then -- Should always be valid after filtering, but check anyway
                local timeAlive = currentTime - (indicator.createdAt or 0)
                -- Recalculate fade alpha based on remaining display time
                local fadeAlpha = 255 * (1 - (timeAlive / damage_indicator_state.DISPLAY_TIME))
                fadeAlpha = math.floor(math.max(0, math.min(255, fadeAlpha)) + 0.5)

                if fadeAlpha > 0 then
                    -- Pass stackInnerWidth as useSpecificWidth to ensure consistent alignment
                    DrawDamageIndicator(indicator, currentDrawY, fadeAlpha, damage_indicator_state.current_alignment, realStackDrawX, realStackInnerWidth, nil)
                    -- Increment Y position for the next indicator, adjusting for current font size
                    currentDrawY = currentDrawY + font_state.current_font_size + damage_indicator_state.INNER_BOX_PADDING + damage_indicator_state.OUTER_OUTLINE_OFFSET * 2 + damage_indicator_state.SPACING
                end
            end
        end
    end

    -- Draw Fake Preview Indicator (if menu is open)
    if damage_indicator_state.fake_hit_preview_active then
        local fadeAlpha = 255 -- Fake preview is always fully visible
        local fakeIndicatorData = {
             victimName = damage_indicator_state.fake_victim_name, hitboxname = damage_indicator_state.fake_hitbox_name,
             nDamageHp = damage_indicator_state.fake_damage_hp, healthLeft = damage_indicator_state.fake_health_left,
        }
        if global_ui_state.isDamageIndicatorDragging and not controlDown then
            Renderer.DrawText(font_state.current_font_name, "Hold CTRL To Snap", Vector2D(fakePreviewDrawX, fakePreviewDrawY - 18), false, false, GUI.colors.text)
        elseif global_ui_state.isDamageIndicatorDragging and controlDown then
            Renderer.DrawText(font_state.current_font_name, "SNAPPING", Vector2D(fakePreviewDrawX, fakePreviewDrawY - 18), false, false, GUI.colors.text)
        end
        local isFakePreviewHovered = menuOpen and isMouseInDamageIndicatorRect and not global_ui_state.isDamageIndicatorDragging
        DrawDamageIndicator(fakeIndicatorData, fakePreviewDrawY, fadeAlpha, damage_indicator_state.current_alignment, fakePreviewDrawX, fakeInnerBoxWidth_calc, fakeInnerBoxWidth_calc, isFakePreviewHovered)
    end
    damage_indicator_state.prev_mouse_down = mouseDown
end


local function addImpact(imp)
    hit_effects_state.num_impacts = hit_effects_state.num_impacts + 1
    hit_effects_state.impacts[hit_effects_state.num_impacts] = imp
end

local function removeImpact(i)
    for j = i, hit_effects_state.num_impacts - 1 do
        hit_effects_state.impacts[j] = hit_effects_state.impacts[j+1]
    end
    hit_effects_state.impacts[hit_effects_state.num_impacts] = nil
    hit_effects_state.num_impacts = hit_effects_state.num_impacts - 1
end

local function addHitmarker(hm)
    hm.offset = 0
    hit_effects_state.num_hitmarkers = hit_effects_state.num_hitmarkers + 1
    hit_effects_state.hitmarkers[hit_effects_state.num_hitmarkers] = hm
end

local function removeHitmarker(i)
    for j = i, hit_effects_state.num_hitmarkers - 1 do
        hit_effects_state.hitmarkers[j] = hit_effects_state.hitmarkers[j+1]
    end
    hit_effects_state.hitmarkers[hit_effects_state.num_hitmarkers] = nil
    hit_effects_state.num_hitmarkers = hit_effects_state.num_hitmarkers - 1
end

local function WorldToScreenVec(pos)
    return Renderer.WorldToScreen(pos)
end

local function hit(event)
    if global_ui_state.isIntroActive then return end -- Don't process hits during intro
    if not gui_options.dmgmarkers_enable then return end
    local name = event:GetName()

    if name == "bullet_impact" then
        local ctrl = event:GetPlayerController("userid")
        if ctrl and ctrl.m_bIsLocalPlayerController then
            local pos = Vector(
                event:GetInt("x"),
                event:GetInt("y"),
                event:GetInt("z")
            )
            addImpact({ time = 1.0, position = pos })

        end

    elseif name == "player_hurt" then
        local att = event:GetPlayerController("attacker")
        if not att or not att.m_bIsLocalPlayerController then return end
        if att == nil then return end
        local victim = event:GetPlayerPawn("userid")
        if not victim or not victim.m_pGameSceneNode then
            return
        end

        -- pick closest impact
        local vicPos = victim.m_pGameSceneNode.m_vecAbsOrigin
        if not vicPos then
            return
        end
        local best_i, best_d2 = -1, math.huge

        for i = 1, hit_effects_state.num_impacts do
            local imp = hit_effects_state.impacts[i]
            local dx = imp.position.x - vicPos.x
            local dy = imp.position.y - vicPos.y
            local dz = imp.position.z - vicPos.z
            local d2 = dx*dx + dy*dy + dz*dz
            if d2 < best_d2 then
                best_d2, best_i = d2, i
            end
        end

        if best_i > 0 then
            local damage = event:GetInt("dmg_health")
            addHitmarker({
                opacity  = 1.0,
                position = hit_effects_state.impacts[best_i].position,
                damage   = damage,
            })
            removeImpact(best_i)
        end
    end
end

local function hitmarkers_render()
    if global_ui_state.isIntroActive then return end -- Don't render hitmarkers during intro

    if not gui_options.dmgmarkers_enable then return end

    local dt = Globals.GetFrameTime()

    -- loop forwards to update
    for i = 1, hit_effects_state.num_hitmarkers do
        local hm = hit_effects_state.hitmarkers[i]
        hm.opacity = hm.opacity - dt * hit_effects_state.fade_rate
        hm.offset  = hm.offset  + dt * hit_effects_state.rise_speed

        if hm.opacity > 0 then
            local sp = WorldToScreenVec(hm.position)
            if sp then
                sp.y = sp.y - 15 - hm.offset
                local alpha = math.floor(hm.opacity * 255)
                -- Use theme accent color for hitmarkers
                local accentColor = GUI.colors.headerAccent
                Renderer.DrawText(font_state.current_font_name, tostring(hm.damage), sp, true, true, Color(accentColor.r, accentColor.g, accentColor.b, alpha))
            end
        end
    end

    for i = hit_effects_state.num_hitmarkers, 1, -1 do
        if hit_effects_state.hitmarkers[i].opacity <= 0 then
            removeHitmarker(i)
        end
    end
end

--- Team Damage and Kills Tracker

-- Reset all statistics
local function ResetStats()
    team_dmg_tracker_state.damage_count = 0
    team_dmg_tracker_state.team_names  = {}
    team_dmg_tracker_state.team_dmg    = {}
    team_dmg_tracker_state.team_kills  = {}
end

-- Sort players by damage descending without using table.sort
local function SortTeamDamageData()
    if team_dmg_tracker_state.damage_count <= 1 then return end
    -- Bubble sort in-place on parallel arrays
    for i = 1, team_dmg_tracker_state.damage_count - 1 do
        for j = 1, team_dmg_tracker_state.damage_count - i do
            if (team_dmg_tracker_state.team_dmg[j+1] or 0) > (team_dmg_tracker_state.team_dmg[j] or 0) then -- Added nil checks
                -- swap damage
                team_dmg_tracker_state.team_dmg[j], team_dmg_tracker_state.team_dmg[j+1]     = team_dmg_tracker_state.team_dmg[j+1], team_dmg_tracker_state.team_dmg[j]
                -- swap names
                team_dmg_tracker_state.team_names[j], team_dmg_tracker_state.team_names[j+1] = team_dmg_tracker_state.team_names[j+1], team_dmg_tracker_state.team_names[j]
                -- swap kills
                team_dmg_tracker_state.team_kills[j], team_dmg_tracker_state.team_kills[j+1] = team_dmg_tracker_state.team_kills[j+1], team_dmg_tracker_state.team_kills[j]
            end
        end
    end
end

-- Helpers: get local player's team
local function GetLocalPlayerController_Team()
    local maxIdx = Entities.GetHighestEntityIndex() or 0
    for i = 1, maxIdx do
        local ent = Entities.GetEntityFromIndex(i)
        if ent and ent.m_bIsLocalPlayerController then
            return ent
        end
    end
    return nil
end

local function GetLocalPlayerPawn_TeamNum()
    local ctrl = GetLocalPlayerController_Team()
    if not ctrl or not ctrl.m_hPawn then return nil end
    local pawn_entity = ctrl.m_hPawn
    if not pawn_entity then return nil end
    return pawn_entity.m_iTeamNum
end

-- Event handler: track damage and kills
local function team_dmg_event(event)
    if global_ui_state.isIntroActive then return end -- Skip tracking during intro
    if not Globals.IsConnected() then return end -- Only track when connected

    local eventName = event:GetName()
    local localTeam = GetLocalPlayerPawn_TeamNum()

    if eventName == "player_hurt" then
        local attacker = event:GetPlayerController("attacker")
        local victim   = event:GetPlayerController("userid")
        local pawnA    = event:GetPlayerPawn("attacker")
        local pawnV    = event:GetPlayerPawn("userid")
        if not (attacker and victim and pawnA and pawnV) then return end

        -- Only track if both shooter and target are on the local player's team (friendly fire)
        if pawnA.m_iTeamNum == localTeam and pawnV.m_iTeamNum == localTeam then
            local aName = attacker.m_sSanitizedPlayerName or "Unknown"
            local dmg = event:GetInt("dmg_health") or 0
            local found = false

            for i = 1, team_dmg_tracker_state.damage_count do
                if team_dmg_tracker_state.team_names[i] == aName then
                    team_dmg_tracker_state.team_dmg[i] = (team_dmg_tracker_state.team_dmg[i] or 0) + dmg
                    found = true
                    break
                end
            end

            if not found then
                team_dmg_tracker_state.damage_count = team_dmg_tracker_state.damage_count + 1
                team_dmg_tracker_state.team_names[team_dmg_tracker_state.damage_count] = aName
                team_dmg_tracker_state.team_dmg[team_dmg_tracker_state.damage_count]   = dmg
                team_dmg_tracker_state.team_kills[team_dmg_tracker_state.damage_count] = 0
            end
        end

    elseif eventName == "player_death" then
        local attacker = event:GetPlayerController("attacker")
        local pawnA    = event:GetPlayerPawn("attacker")
        local pawnV    = event:GetPlayerPawn("userid")
        if not (attacker and pawnA and pawnV) then return end

        -- Only track team kills if both attacker and victim are on the local team
        if pawnA.m_iTeamNum == localTeam and pawnV.m_iTeamNum == localTeam then
            local aName = attacker.m_sSanitizedPlayerName or "Unknown"
            local found = false

            for i = 1, team_dmg_tracker_state.damage_count do
                if team_dmg_tracker_state.team_names[i] == aName then
                    team_dmg_tracker_state.team_kills[i] = (team_dmg_tracker_state.team_kills[i] or 0) + 1
                    found = true
                    break
                end
            end

            if not found then
                team_dmg_tracker_state.damage_count = team_dmg_tracker_state.damage_count + 1
                team_dmg_tracker_state.team_names[team_dmg_tracker_state.damage_count] = aName
                team_dmg_tracker_state.team_dmg[team_dmg_tracker_state.damage_count]   = 0 -- Start with zero damage if only a kill is recorded
                team_dmg_tracker_state.team_kills[team_dmg_tracker_state.damage_count] = 1
            end
        end

    elseif eventName == "cs_win_panel_match" then
        ResetStats()
    end
end


-- New OnRenderer function for Team Damage Tracker
function OnRendererTeamDamage()
    if global_ui_state.isIntroActive then return end -- Don't render during intro

    if not gui_options.teamlist_enable then
         if global_ui_state.isTeamDmgDragging then
            global_ui_state.isTeamDmgDragging = false
            team_dmg_tracker_state.prev_mouse_down = false -- Reset mouse state too
         end
         return
    end

    -- Reset if disconnected
    if not Globals.IsConnected() then ResetStats() end

    local isPreview = (team_dmg_tracker_state.damage_count == 0)
    -- only show preview when menu open
    if isPreview and not Input.IsMenuOpen() then return end

    -- sort if data present
    if not isPreview then SortTeamDamageData() end

    -- measure title
    local titleText = "Team Damage Tracker"
    local titleWidth = ApproxTextWidthUI(titleText)

    -- compute dimensions
    local maxTextW = titleWidth
    local previewEntryText = "Player: 88 dmg, 0 kills"
    if isPreview then
         maxTextW = math.max(maxTextW, ApproxTextWidthUI(trim_whitespace(previewEntryText))) -- Trim preview text
    else
        for i = 1, team_dmg_tracker_state.damage_count do
            -- Trim player name before formatting and measuring
            local trimmed_name = trim_whitespace(team_dmg_tracker_state.team_names[i] or "Unknown")
            local entryStr = string.format("%s: %d dmg, %d kills", trimmed_name, team_dmg_tracker_state.team_dmg[i] or 0, team_dmg_tracker_state.team_kills[i] or 0) -- Added nil checks
            maxTextW = math.max(maxTextW, ApproxTextWidthUI(entryStr))
        end
    end
    local lineCount = isPreview and 2.5 or (team_dmg_tracker_state.damage_count + 1.5)
    local innerW = maxTextW + team_dmg_tracker_state.padding*2
    -- Recalculate inner height based on current font size
    local innerH = lineCount * (font_state.current_font_size + 2) + team_dmg_tracker_state.padding * 2
    local outerW = innerW + team_dmg_tracker_state.outer_outline_offset*2
    local outerH = innerH + team_dmg_tracker_state.outer_outline_offset*2

    -- gather input
    local mousePos = Input.GetCursorPos()
    local mouseDown = Input.GetKeyDown(0x01)
    local controlDown = Input.GetKeyDown(global_ui_state.CONTROL_KEY)
    local menuOpen = Input.IsMenuOpen()
    local mouseReleased = team_dmg_tracker_state.prev_mouse_down and not mouseDown

    -- draggable area
    local clickX = team_dmg_tracker_state.saved_x - team_dmg_tracker_state.outer_outline_offset
    local clickY = team_dmg_tracker_state.saved_y - team_dmg_tracker_state.outer_outline_offset
    local clickW, clickH = outerW, outerH
    -- Corrected the hit detection logic here
    local over = menuOpen and mousePos.x >= clickX and mousePos.x <= clickX + clickW
                         and mousePos.y >= clickY and mousePos.y <= clickY + clickH

    -- start drag if menu open, over box, and nothing else dragging
    if menuOpen and mouseDown and not global_ui_state.isTeamDmgDragging and not global_ui_state.isWatermarkDragging and not global_ui_state.isDamageIndicatorDragging and not global_ui_state.isSpecDragging and not GUI.draggingWindow and over then
        global_ui_state.isTeamDmgDragging = true
        team_dmg_tracker_state.drag_offset_x = mousePos.x - team_dmg_tracker_state.saved_x
        team_dmg_tracker_state.drag_offset_y = mousePos.y - team_dmg_tracker_state.saved_y
    end

    -- stop drag, apply snap
    if global_ui_state.isTeamDmgDragging and (mouseReleased or not menuOpen) then
        local newX = team_dmg_tracker_state.saved_x
        if controlDown then
            local outerX = team_dmg_tracker_state.saved_x - team_dmg_tracker_state.outer_outline_offset
            local bestX, minDist = nil, global_ui_state.SNAP_DISTANCE_THRESHOLD+1
            -- left snap
            local d = math.abs(outerX)
            if d<=global_ui_state.SNAP_DISTANCE_THRESHOLD and d<minDist then bestX,minDist=team_dmg_tracker_state.outer_outline_offset,d end
            -- center
            local centerX = outerX + outerW/2
            d = math.abs(centerX - (Renderer.GetScreenSize().x/2))
            if d<=global_ui_state.SNAP_DISTANCE_THRESHOLD and d<minDist then
                bestX,minDist=(Renderer.GetScreenSize().x/2 - outerW/2 + team_dmg_tracker_state.outer_outline_offset),d
            end
            -- right
            local rightX = outerX + outerW
            d = math.abs(rightX - Renderer.GetScreenSize().x)
            if d<=global_ui_state.SNAP_DISTANCE_THRESHOLD and d<minDist then
                bestX,minDist=Renderer.GetScreenSize().x - outerW + team_dmg_tracker_state.outer_outline_offset,d
            end
            if bestX then newX=bestX end
        end
        team_dmg_tracker_state.saved_x = newX
        global_ui_state.isTeamDmgDragging = false
    end

    -- update dragging position
    if global_ui_state.isTeamDmgDragging then
        local rawX = mousePos.x - team_dmg_tracker_state.drag_offset_x
        local rawY = mousePos.y - team_dmg_tracker_state.drag_offset_y
        -- clamp
        rawX = math.max(team_dmg_tracker_state.outer_outline_offset, math.min(Renderer.GetScreenSize().x - outerW + team_dmg_tracker_state.outer_outline_offset, rawX))
        rawY = math.max(team_dmg_tracker_state.outer_outline_offset, math.min(Renderer.GetScreenSize().y - outerH + team_dmg_tracker_state.outer_outline_offset, rawY))
        team_dmg_tracker_state.saved_x, team_dmg_tracker_state.saved_y = rawX, rawY
    end

    -- preview snap rectangles if dragging + control
    if menuOpen and global_ui_state.isTeamDmgDragging and controlDown then
        local outerX = team_dmg_tracker_state.saved_x - team_dmg_tracker_state.outer_outline_offset
        local innerY = team_dmg_tracker_state.saved_y
        -- left
        if math.abs(outerX)<=global_ui_state.SNAP_DISTANCE_THRESHOLD then
            DrawRect(0,innerY,innerW,innerH,global_ui_state.SNAP_INDICATOR_COLOR)
        end
        -- center
        local cenOuter = outerX + outerW / 2
        if math.abs(cenOuter - (Renderer.GetScreenSize().x / 2 )) <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
            DrawRect(Renderer.GetScreenSize().x / 2 - innerW / 2 , innerY, innerW, innerH, global_ui_state.SNAP_INDICATOR_COLOR)
        end
        -- right
        local rightX = outerX + outerW
        if math.abs(rightX-Renderer.GetScreenSize().x)<=global_ui_state.SNAP_DISTANCE_THRESHOLD then
            DrawRect(Renderer.GetScreenSize().x - outerW + team_dmg_tracker_state.outer_outline_offset, innerY, innerW, innerH, global_ui_state.SNAP_INDICATOR_COLOR)
        end
    end

    DrawRect(team_dmg_tracker_state.saved_x-team_dmg_tracker_state.outer_outline_offset, team_dmg_tracker_state.saved_y-team_dmg_tracker_state.outer_outline_offset, outerW, outerH, GUI.colors.windowOutline)
    DrawRect(team_dmg_tracker_state.saved_x, team_dmg_tracker_state.saved_y, innerW, innerH, GUI.colors.window)
    if global_ui_state.isTeamDmgDragging and not controlDown then
        Renderer.DrawText(font_state.current_font_name, "Hold CTRL To Snap", Vector2D(team_dmg_tracker_state.saved_x, team_dmg_tracker_state.saved_y - 18), false, false, GUI.colors.text)
    elseif global_ui_state.isTeamDmgDragging and controlDown then
        Renderer.DrawText(font_state.current_font_name, "SNAPPING", Vector2D(team_dmg_tracker_state.saved_x, team_dmg_tracker_state.saved_y - 18), false, false, GUI.colors.text)
    end
    -- hover highlight
    if over and not global_ui_state.isTeamDmgDragging then
        DrawRect(team_dmg_tracker_state.saved_x, team_dmg_tracker_state.saved_y, innerW, innerH, GUI.colors.buttonHover)
    end

    -- draw title centered using the outer box’s left and width
    local outerX = team_dmg_tracker_state.saved_x - team_dmg_tracker_state.outer_outline_offset
    local titleX = outerX + (outerW / 2) + 2
    local titleY = team_dmg_tracker_state.saved_y + team_dmg_tracker_state.padding
    Renderer.DrawText(font_state.current_font_name, titleText, Vector2D(titleX, titleY), true, false, GUI.colors.headerAccent)
    local ly = titleY + ((font_state.current_font_size + 2) * 0.6) -- Adjust underline Y based on current font size
    local titleStartX_team = titleX - (titleWidth / 2)
    local titleEndX_team = titleX + (titleWidth / 2)
    Renderer.DrawLine(Vector2D(titleStartX_team, ly + 5), Vector2D(titleEndX_team, ly + 5), GUI.colors.headerAccent, 1)

    -- draw entries or preview
    local y = titleY + ((font_state.current_font_size + 2) * 1.5) -- Adjust starting Y based on current font size
    if isPreview then
        Renderer.DrawText(font_state.current_font_name, trim_whitespace(previewEntryText), Vector2D(team_dmg_tracker_state.saved_x+team_dmg_tracker_state.padding,y), false, false, GUI.colors.text)
    else
        for i=  1, team_dmg_tracker_state.damage_count do
            -- Trim player name before formatting and drawing
            local trimmed_name = trim_whitespace(team_dmg_tracker_state.team_names[i] or "Unknown")
            local s = string.format("%s: %d dmg, %d kills", trimmed_name, team_dmg_tracker_state.team_dmg[i] or 0, team_dmg_tracker_state.team_kills[i] or 0)
            Renderer.DrawText(font_state.current_font_name, s, Vector2D(team_dmg_tracker_state.saved_x+team_dmg_tracker_state.padding,y), false, false, GUI.colors.text)
            y= y + (font_state.current_font_size + 2)
        end
    end

    team_dmg_tracker_state.prev_mouse_down = mouseDown
end

local snd_toolvolume = CVar.FindVar( "snd_toolvolume" );

local function hit_sound_event(event)
    if global_ui_state.isIntroActive then return end -- Don't play sounds during intro

    if not gui_options.hitsound_enable then return end
    if event:GetName() == "player_hurt" then
        local attacker = event:GetPlayerController("attacker")
        if not attacker or not attacker.m_bIsLocalPlayerController then return end
        CVar.ExecuteClientCmd("snd_toolvolume " .. gui_options.hitsound_volume / 100)
        if gui_options.hitsound == 0 then
            CVar.ExecuteClientCmd("play \\sounds\\hitmarker");
        elseif gui_options.hitsound == 1 then
            CVar.ExecuteClientCmd("play \\sounds\\ft");
        elseif gui_options.hitsound == 2 then
            CVar.ExecuteClientCmd("play \\sounds\\minecraft");
        elseif gui_options.hitsound == 3 then
            CVar.ExecuteClientCmd("play \\sounds\\cod");
        end
    end
end


-- Helper function to get local player pawn (already exists, ensuring it's used by sound functions)
local function GetLocalPlayerPawn()
    local highest_entity_index = Entities.GetHighestEntityIndex() or 0
    for i = 1, highest_entity_index do
        local entity = Entities.GetEntityFromIndex(i)
        if entity and entity.m_bIsLocalPlayerController then
            return entity.m_hPawn or 0
        end
    end
    return 0
end

-- Sound Indicator functions
local function Sounds(event)
    if not gui_options.sound_enabled then -- Use gui_options.sound_enabled
        return
    end

    if not Globals.IsConnected() then
        return
    end

    local eventName = event:GetName()

    if eventName == "player_sound" then
        local currentTime = Globals.GetCurrentTime()
        local userid = event:GetPlayerPawn("userid") or 0
        local eventRadius = event:GetInt("radius") or 0
        local duration = event:GetInt("duration") or 0

        if duration == nil or duration == 0 then
            duration = 2
        end

        local soundId = string.format("%s_%f_%d", tostring(userid), currentTime, eventRadius)

        if sound_runtime_state.processed_sounds[soundId] then -- Use sound_runtime_state
            return
        end

        sound_runtime_state.processed_sounds[soundId] = currentTime -- Use sound_runtime_state

        local localPlayerPawn = GetLocalPlayerPawn()
        local localPlayerTeam = nil
        if localPlayerPawn and localPlayerPawn ~= 0 and localPlayerPawn.m_iTeamNum then
            localPlayerTeam = localPlayerPawn.m_iTeamNum
        end

        if userid and userid.m_iTeamNum and localPlayerTeam and (userid.m_iTeamNum == localPlayerTeam) then
            return
        end

        if userid and userid.m_pGameSceneNode and userid.m_pGameSceneNode.m_vecAbsOrigin then
            local position = userid.m_pGameSceneNode.m_vecAbsOrigin

            local initial_distance_check_passed = false
            if localPlayerPawn and localPlayerPawn ~= 0 and localPlayerPawn.m_pGameSceneNode and localPlayerPawn.m_pGameSceneNode.m_vecAbsOrigin then
                local current_player_distance = localPlayerPawn.m_pGameSceneNode.m_vecAbsOrigin:DistTo(position)
                if gui_options.sound_draw_always or (current_player_distance <= eventRadius) then -- Use gui_options.sound_draw_always
                    initial_distance_check_passed = true
                end
            end

            table.insert(sound_runtime_state.active_sound_indicators, { -- Use sound_runtime_state
                startTime = Globals.GetCurrentTime(),
                duration = duration,
                position = position,
                eventRadius = eventRadius,
                soundId = soundId,
                initial_distance_check_passed = initial_distance_check_passed
            })
        end
    end
end

local function OnDraw()
    if not gui_options.sound_enabled then -- Use gui_options.sound_enabled
        return
    end

    local currentTime = Globals.GetCurrentTime()
    local indicatorsToRemove = {}

    local localPlayerPawn = GetLocalPlayerPawn()
    local localPlayerPosition = nil
    if localPlayerPawn and localPlayerPawn ~= 0 and localPlayerPawn.m_pGameSceneNode and localPlayerPawn.m_pGameSceneNode.m_vecAbsOrigin then
        localPlayerPosition = localPlayerPawn.m_pGameSceneNode.m_vecAbsOrigin
    end

    local peakDrawingRadius = gui_options.sound_peak_radius -- Use gui_options.sound_peak_radius

    for i, indicator in ipairs(sound_runtime_state.active_sound_indicators) do -- Use sound_runtime_state
        if not indicator.initial_distance_check_passed then
            goto continue_loop
        end

        local elapsedTime = currentTime - indicator.startTime

        if elapsedTime > indicator.duration then
            table.insert(indicatorsToRemove, i)
        else
            -- Base color for sound indicators, using headerAccent from the theme
            local baseColor = GUI.colors.headerAccent

            -- Dynamic color based on distance
            if localPlayerPosition and indicator.position and indicator.eventRadius then
                local distance = localPlayerPosition:DistTo(indicator.position)

                local maxDistance = indicator.eventRadius
                local normalizedDistance = math.min(distance / maxDistance, 1.0)

                -- Adjust base color based on proximity (red for close, yellow for mid, green for far)
                if normalizedDistance <= 0.33 then
                    baseColor = Color(255, 0, 0) -- Red for very close
                elseif normalizedDistance <= 0.66 then
                    baseColor = Color(255, 255, 0) -- Yellow for mid-range
                else
                    baseColor = Color(0, 255, 0) -- Green for far
                end
            end

            local currentRadius
            local currentAlpha

            local currentPeakRadius = (peakDrawingRadius * 0.5) + (indicator.eventRadius / 15)
            currentPeakRadius = math.min(currentPeakRadius, 50)

            local expansionPhaseDuration = indicator.duration * 0.3
            local shatterPhaseDuration = indicator.duration * 0.4

            if elapsedTime <= expansionPhaseDuration then
                local progress = elapsedTime / expansionPhaseDuration
                currentRadius = currentPeakRadius * progress
                currentAlpha = 255

                local drawColor = Color(baseColor.r, baseColor.g, baseColor.b, math.floor(currentAlpha))
                Renderer.DrawCircle3D(indicator.position, drawColor, math.max(1, math.floor(currentRadius)))
            else
                local progressInShatter = (elapsedTime - expansionPhaseDuration) / shatterPhaseDuration

                local numFragments = 6
                local fragmentSpread = 5 + (indicator.eventRadius / 60)

                local fragmentBaseSize = 2

                for f = 0, numFragments - 1 do
                    local fragmentAngle = (f / numFragments) * 2 * math.pi + (currentTime * 5)

                    local startFragmentRadius = currentPeakRadius * 0.8
                    local currentFragmentRadius = startFragmentRadius + (fragmentSpread * progressInShatter)

                    currentFragmentRadius = math.min(currentFragmentRadius, currentPeakRadius + 15)

                    local fragmentX = indicator.position.x + math.cos(fragmentAngle) * currentFragmentRadius
                    local fragmentY = indicator.position.y + math.sin(fragmentAngle) * currentFragmentRadius
                    local fragmentZ = indicator.position.z

                    local fragmentPosition = Vector(fragmentX, fragmentY, fragmentZ)

                    local fragmentAlpha = 255 * (1 - progressInShatter)
                    fragmentAlpha = math.max(0, math.floor(fragmentAlpha))

                    local fragmentSize = math.max(1, math.floor(fragmentBaseSize * (1 - progressInShatter * 0.5)))

                    local fragmentColor = Color(baseColor.r, baseColor.g, baseColor.b, fragmentAlpha)
                    Renderer.DrawCircle3D(fragmentPosition, fragmentColor, fragmentSize)
                end
            end
        end
        ::continue_loop::
    end

    for i = #indicatorsToRemove, 1, -1 do
        table.remove(sound_runtime_state.active_sound_indicators, indicatorsToRemove[i]) -- Use sound_runtime_state
    end

    local ticksToKeep = currentTime - 10.0
    for soundId, soundTime in pairs(sound_runtime_state.processed_sounds) do -- Use sound_runtime_state
        if soundTime < ticksToKeep then
            sound_runtime_state.processed_sounds[soundId] = nil -- Use sound_runtime_state
        end
    end
end


local function CorrectPitch(cmd)
    -- Will correct shots (NEED TO DO) --
end


local function FakePitch(cmd)

    if global_ui_state.isIntroActive then return end -- Don't apply fake pitch during intro

    if not gui_options.fakepitch_enable then return end
    cmd.m_angViewAngles = Vector(-2182423346297399750336966557899, cmd.m_angViewAngles.y, 9.0)
end

-- Helper function to convert HSV to RGB (Hue, Saturation, Value)
-- H: 0–1, S: 0–1, V: 0–1
-- Returns Color(r, g, b) with r, g, b from 0–255
local function hsv_to_rgb(h, s, v)
    local i = math.floor(h * 6)
    local f = h * 6 - i
    local p = v * (1 - s)
    local q = v * (1 - f * s)
    local t = v * (1 - (1 - f) * s)

    local r, g, b
    if i % 6 == 0 then
        r, g, b = v, t, p
    elseif i % 6 == 1 then
        r, g, b = q, v, p
    elseif i % 6 == 2 then
        r, g, b = p, v, t
    elseif i % 6 == 3 then
        r, g, b = p, q, v
    elseif i % 6 == 4 then
        r, g, b = t, p, v
    else
        r, g, b = v, p, q
    end

    return Color(math.floor(r * 255), math.floor(g * 255), math.floor(b * 255))
end

--- Player Trails Logic ---
local function OnRendererPlayerTrails()
    if global_ui_state.isIntroActive then return end -- Don't render trails during intro

    if not gui_options.playertrails_enable or not Globals.IsConnected() then
        player_trails_state.trails = {}
        return
    end

    local max_length = gui_options.trail_length
    if max_length < 1 then return end
    local local_player_pawn, local_player_index
    local highest_index = Entities.GetHighestEntityIndex() or 0

    -- Find local player pawn
    for i = 1, highest_index do
        local entity = Entities.GetEntityFromIndex(i)
        if entity and entity.m_bIsLocalPlayerController then
            local pawn = entity.m_hPawn
            if pawn and pawn.m_pGameSceneNode then
                local_player_pawn = pawn
                local_player_index = i
                break
            end
        end
    end

    if not local_player_pawn or not local_player_pawn.m_pGameSceneNode then
        player_trails_state.trails = {}
        return
    end

    -- Record position in circular buffer
    local current_pos = local_player_pawn.m_pGameSceneNode.m_vecAbsOrigin
    if not player_trails_state.trails[local_player_index] then
        player_trails_state.trails[local_player_index] = {
            positions  = {},
            next_index = 1,
            is_full    = false
        }

        for k = 1, max_length do
            player_trails_state.trails[local_player_index].positions[k] = nil
        end
    end

    local trail_data = player_trails_state.trails[local_player_index]
    trail_data.positions[trail_data.next_index] = current_pos
    trail_data.next_index = trail_data.next_index + 1
    if trail_data.next_index > max_length then
        trail_data.next_index = 1
        trail_data.is_full = true
    end

    -- Determine how many positions to draw
    local num_to_draw = trail_data.is_full and max_length or (trail_data.next_index - 1)
    local start_index = trail_data.is_full and trail_data.next_index or 1

    if num_to_draw > 1 then
        for j = 1, num_to_draw - 1 do
            -- Circular-buffer indexing
            local cur_idx = (start_index + j - 2) % max_length + 1
            local next_idx = (start_index + j -  1) % max_length + 1

            local start_pos = trail_data.positions[cur_idx]
            local end_pos = trail_data.positions[next_idx]
            if start_pos and end_pos then
                local screen_start = Renderer.WorldToScreen(start_pos)
                local screen_end = Renderer.WorldToScreen(end_pos)

                if screen_start and screen_end and screen_start.x and screen_start.y and screen_end.x and screen_end.y then
                    local sz = Renderer.GetScreenSize()
                    local on_screen =
                      screen_start.x >= 0 and screen_start.x <= sz.x and
                      screen_start.y >= 0 and screen_start.y <= sz.y and
                      screen_end.x   >= 0 and screen_end.x   <= sz.x and
                      screen_end.y   >= 0 and screen_end.y   <= sz.y

                    if on_screen then
                        if  gui_options.trail_color == nil or gui_options.trail_color == 0 then
                            local hue = j / num_to_draw
                            -- Use theme accent color for rainbow effect
                            local themeAccent = GUI.colors.headerAccent
                            local baseColor = hsv_to_rgb(hue, 1, 1) -- Pure HSV color
                            -- Blend pure HSV with theme's accent color for a themed rainbow
                            player_trails_state.segment_color = Color(
                                math.floor((baseColor.r + themeAccent.r) / 2),
                                math.floor((baseColor.g + themeAccent.g) / 2),
                                math.floor((baseColor.b + themeAccent.b) / 2)
                            )
                        else
                            player_trails_state.segment_color = gui_options.trail_color_picker or Color(255, 255, 255, 255)
                        end

                        -- Triangle fade: start at α=10, peak at α=255, back to α=10
                        local t    = j / num_to_draw
                        local fade = 1 - math.abs(2 * t - 1)
                        local start_a, peak_a = 10, 255
                        local alpha = math.floor(start_a + fade * (peak_a - start_a))
                        local color = Color(
                            player_trails_state.segment_color.r,
                            player_trails_state.segment_color.g,
                            player_trails_state.segment_color.b,
                            alpha
                        )
                        Renderer.DrawLine(screen_start, screen_end, color, gui_options.trail_width)
                    end
		end
            end
        end
    end
end


--- Startup Animation Logic ---
-- Linear interpolation
local function Lerp(a, b, t) return a + (b - a) * t end

local function Clamp(v, lo, hi)
  if v < lo then return lo
  elseif v > hi then return hi
  else return v end
end

local function EaseIn(t) return t^3 end
local function EaseOut(t) return 1 - (1 - t)^3 end
local function EaseInOut(t)
  if t < 0.5 then
    return 4 * t^3
  else
    return 1 - ((-2*t + 2)^3) / 2
  end
end


local function RenderIntro()
    if not global_ui_state.isIntroActive then return end

    if global_ui_state.introStartDelayTime and Globals.GetCurrentTime() - global_ui_state.introStartDelayTime < global_ui_state.introCancelDelay then
        return
    end

    if Input.GetKeyDown(0x01) then
       global_ui_state.isIntroActive = false
       return
    end

    local e = Globals.GetCurrentTime() - animation_state.start_time

    local phase
    if e < animation_state.t1 then phase = 0
    elseif e < animation_state.t2 then phase = 1
    elseif e < animation_state.t3 then phase = 2
    elseif e < animation_state.t4 then phase = 3
    elseif e < animation_state.t5 then phase = 4
    else phase = 5 end

    if phase == 5 then
        global_ui_state.isIntroActive = false
        return
    end

    local bgA, txA
    if phase == 0 then
        -- Fade in background and text
        local tt = Clamp(e/animation_state.fade_in_duration,0,1)
        bgA = math.floor(Lerp(0,180,tt))
        txA = math.floor(Lerp(0,255,tt))
    elseif phase <= 3 then
        bgA, txA = 180,255
    else
        local tt = Clamp((e - animation_state.t4)/animation_state.fade_out_duration,0,1)
        bgA = math.floor(Lerp(180,0,tt))
        txA = math.floor(Lerp(255,0,tt))
    end

    animation_state.screen_size = animation_state.screen_size or Renderer.GetScreenSize()

    -- Draw the background rectangle
    Renderer.DrawRectFilled(
        Vector2D(0,0),
        Vector2D(animation_state.screen_size.x, animation_state.screen_size.y),
        Color(0,0,0,bgA)
    )

    local cx, cy = (animation_state.screen_size.x * 0.5) , animation_state.screen_size.y * 0.5
    local baseY = cy - animation_state.font_size*0.5

    if phase < 2 then
        local y = (phase==0)
            and Lerp(-animation_state.font_size, baseY, EaseOut(e/animation_state.fade_in_duration))
            or baseY

        Renderer.DrawText(
            "Segu","B",
            Vector2D(cx - animation_state.p_width*0.5, y),
            false,false,
            Color(255,0,0, txA)
        )
    else
        local at = Clamp((e - animation_state.t2)/animation_state.text_reveal_duration,0,1)
        if phase > 2 then at = 1 end
        at = EaseInOut(at)

        local startX = cx - (animation_state.p_width * 0.5)
        local endX = cx - (animation_state.full_text_width * 0.5) + 60

        local px = Lerp(startX,endX,at)

        Renderer.DrawText(
            "Segu","B",
            Vector2D(px, baseY),
            false,false,
            -- Use theme accent color for the 'B' in Bloodline
             Color(255,0,0, txA)
        )

        if at > 0.1 or phase > 2 then
            local ft = (phase==2)
                and Clamp((at-0.1)*1.2,0,1)
                or 1
            local restA = math.floor(txA * EaseIn(ft))
            local ry = baseY
            local x = px + animation_state.p_width + animation_state.letter_spacing
            for i = 1, #animation_state.rest_chars do
                local ch = animation_state.rest_chars[i]

                if ft >= (i / #animation_state.rest_chars) then
                    Renderer.DrawText(
                        "Segu", ch,
                        Vector2D(x - 15, ry),
                        false,false,
                        -- Use theme accent color for the rest of "BLOODLINE"
                         Color(255,0,0, restA)
                    )
                end

                local advance = animation_state.rest_char_widths[i] + animation_state.letter_spacing
                if i < #animation_state.rest_chars then
                    local pair = ch .. animation_state.rest_chars[i+1]
                    advance = advance + (animation_state.kerning_map[pair] or 0)
                end
                x = x + advance
            end
        end
    end

    -- Subtitle Animation (Made By text)
    if e >= animation_state.s1 and e < animation_state.s4 then
        local se = e - animation_state.s1

        local subA
        local subY

        local finalSubY = baseY + animation_state.font_size * 1.1
        local startSubY = finalSubY - 100

        if se < animation_state.sub_animation_duration then
            local progress = Clamp(se / animation_state.sub_animation_duration, 0, 1)
            local easedProgress = EaseOut(progress)

            subA = math.floor(Lerp(0, 255, easedProgress))
            subY = Lerp(startSubY, finalSubY, easedProgress)
        elseif e < animation_state.s3 then
            subA = 255
            subY = finalSubY
        else
            -- Fade out phase
            local fadeProg = Clamp((e - animation_state.s3) / animation_state.sub_fade_duration, 0, 1)
            local easedFadeProg = EaseIn(fadeProg)

            subA = math.floor(Lerp(255, 0, easedFadeProg))
            subY = finalSubY
        end

        local subX = cx - animation_state.sub_text_width * 0.5 + 50

        Renderer.DrawText(
            "SeguSmall", animation_state.sub_text,
            Vector2D(subX , subY),
            false,false,
            -- Use theme text color for subtitle
            Color(GUI.colors.text.r, GUI.colors.text.g, GUI.colors.text.b, subA)
        )
    end
end



local function GetSpec()
	local localPlayer = nil
	for i = 1, 64 do
		local entity = Entities.GetEntityFromIndex(i)
		if entity and entity.m_bIsLocalPlayerController then
			localPlayer = entity.m_hPawn
			break
		end
	end

	local spectatedTarget = nil
	if localPlayer then
		if localPlayer.m_iHealth ~= 0 then
			spectatedTarget = localPlayer
		else
			local observerServices = localPlayer.m_pObserverServices
			if observerServices then
				spectatedTarget = Entities.GetEntityFromHandle(observerServices.m_hObserverTarget)
			end
		end
	end
	if spectatedTarget == nil then return end

	local spectatorList = {}

	for i = 1, 64 do
		local entity = Entities.GetEntityFromIndex(i)
		if entity and not entity.m_bIsHLTV and not entity.m_bPawnIsAlive then
			local pawn = entity.m_hObserverPawn
			if pawn then
				local observer_services = pawn.m_pObserverServices
				if observer_services then
					local targetEntity = Entities.GetEntityFromHandle(observer_services.m_hObserverTarget)
					if targetEntity ~= nil and targetEntity == spectatedTarget then
						local targetController = targetEntity.m_hController
						if targetController ~= nil then
							table.insert(spectatorList, entity.m_sSanitizedPlayerName)
						else
							table.insert(spectatorList, entity.m_sSanitizedPlayerName .. " (unknown)")
						end
					end
				end
			end
		end
	end
	return spectatorList
end

local function RenderSpecs()
    if not gui_options.spectator_enable then return end
    if global_ui_state.isIntroActive then return end

    local defaultWidth = 150
    local specList = GetSpec() or {}
    local lineHeight = 13
    local titleText = "Spectator List"
    local textWidth = ApproxTextWidthUI(titleText)
    local padding = 5
    local maxContentWidth = textWidth

    for i, name in ipairs(specList) do
        local w = ApproxTextWidthUI(name)
        if w > maxContentWidth then
            maxContentWidth = w
        end
    end

    local dynWidth = math.max(defaultWidth, maxContentWidth + (padding * 2))
    local baseHeight = 40
    local rectHeight = baseHeight + (#specList * lineHeight)

    -- Use global dragging position instead of fixed values
    local Spec_x = spectator_list_state.x
    local Spec_y = spectator_list_state.y

    -- DRAGGING & SNAPPING LOGIC
    local mousePos = Input.GetCursorPos()
    local mouseDown = Input.GetKeyDown(0x01)
    local menuOpen = Input.IsMenuOpen()
    local controlDown = Input.GetKeyDown(global_ui_state.CONTROL_KEY) -- CTRL key

    if mousePos then
        -- Start dragging logic
        if menuOpen and mouseDown and not global_ui_state.isWatermarkDragging and not global_ui_state.isTeamDmgDragging and not GUI.draggingWindow and not global_ui_state.isDamageIndicatorDragging then
            if mousePos.x >= Spec_x - 5 and mousePos.x <= Spec_x + dynWidth + 5 and
               mousePos.y >= Spec_y - 5 and mousePos.y <= Spec_y + rectHeight + 5 then
                if not global_ui_state.isSpecDragging then
                    global_ui_state.isSpecDragging = true
                    spectator_list_state.drag_offset_x = mousePos.x - Spec_x
                    spectator_list_state.drag_offset_y = mousePos.y - Spec_y
                end
            end
        end

        -- Update position while dragging
        if global_ui_state.isSpecDragging then
            local screenSize = Renderer.GetScreenSize()
            local potentialX = mousePos.x - spectator_list_state.drag_offset_x
            local potentialY = mousePos.y - spectator_list_state.drag_offset_y
            spectator_list_state.x = math.max(5, math.min(potentialX, screenSize.x - dynWidth - 5))
            spectator_list_state.y = math.max(5, math.min(potentialY, screenSize.y - rectHeight - 5))
            Spec_x = spectator_list_state.x
            Spec_y = spectator_list_state.y
        end
    end

    -- **FIXED: Mouse release and snapping logic**
    if spectator_list_state.prev_mouse_down and not mouseDown then
        if global_ui_state.isSpecDragging and controlDown then
            local screenSize = Renderer.GetScreenSize()
            local leftSnap = 5
            local middleSnap = math.floor((screenSize.x - dynWidth) / 2)
            local rightSnap = screenSize.x - dynWidth - 5

            -- **APPLY SNAPPING** - Actually move the element to snap positions
            if math.abs(Spec_x - leftSnap) <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
                spectator_list_state.x = leftSnap
            elseif math.abs(Spec_x - middleSnap) <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
                spectator_list_state.x = middleSnap
            elseif math.abs(Spec_x - rightSnap) <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
                spectator_list_state.x = rightSnap
            end

            -- Update local position after snapping
            Spec_x = spectator_list_state.x
        end

        -- Always stop dragging when mouse is released
        global_ui_state.isSpecDragging = false
    end

    spectator_list_state.prev_mouse_down = mouseDown

    -- **VISUAL SNAP INDICATORS** (while dragging with CTRL held)
    if menuOpen and global_ui_state.isSpecDragging and controlDown then
        local screenSize = Renderer.GetScreenSize()
        local leftSnap = 5
        local middleSnap = math.floor((screenSize.x - dynWidth) / 2)
        local rightSnap = screenSize.x - dynWidth - 5
        local screenCenterX = screenSize.x / 2

        -- Show snap indicators
        if math.abs(Spec_x - leftSnap) <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
            DrawRect(leftSnap, Spec_y, dynWidth, rectHeight, global_ui_state.SNAP_INDICATOR_COLOR)
        end
        if math.abs(Spec_x - middleSnap) <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
            DrawRect(screenCenterX - dynWidth / 2, Spec_y, dynWidth, rectHeight, global_ui_state.SNAP_INDICATOR_COLOR)
        end
        if math.abs(Spec_x - rightSnap) <= global_ui_state.SNAP_DISTANCE_THRESHOLD then
            DrawRect(rightSnap, Spec_y, dynWidth, rectHeight, global_ui_state.SNAP_INDICATOR_COLOR)
        end
    end

    -- Render the spectator list
    if #specList > 0 or menuOpen then
        local Title_spec_x = Spec_x + (dynWidth / 2)
        local Title_spec_y = Spec_y + 3
        Renderer.DrawRectFilled(Vector2D(Spec_x - 5, Spec_y - 5), Vector2D(Spec_x + dynWidth + 5, Spec_y + rectHeight + 5), GUI.colors.windowOutline)
        Renderer.DrawRectFilled(Vector2D(Spec_x, Spec_y), Vector2D(Spec_x + dynWidth, Spec_y + rectHeight), GUI.colors.window)

        -- Determine if mouse is hovering over the spectator list (inner area)
        local isMouseInSpectatorRect = menuOpen and
                mousePos.x >= Spec_x and mousePos.x <= Spec_x + dynWidth and
                mousePos.y >= Spec_y and mousePos.y <= Spec_y + rectHeight

        -- Draw hover effect only if not currently dragging the spectator list itself
        if isMouseInSpectatorRect and not global_ui_state.isSpecDragging then
            Renderer.DrawRectFilled(Vector2D(Spec_x, Spec_y), Vector2D(Spec_x + dynWidth, Spec_y + rectHeight), GUI.colors.buttonHover)
        end

        Renderer.DrawText(font_state.current_font_name, "Spectator List", Vector2D(Title_spec_x, Title_spec_y), true, false, GUI.colors.headerAccent)



        if global_ui_state.isSpecDragging and not controlDown then
            Renderer.DrawText(font_state.current_font_name, "Hold CTRL To Snap", Vector2D(Spec_x, Spec_y - 18), false, false, GUI.colors.text)
        elseif global_ui_state.isSpecDragging and controlDown then
            Renderer.DrawText(font_state.current_font_name, "SNAPPING", Vector2D(Spec_x, Spec_y - 18), false, false, GUI.colors.text)
        end
        local titleStartX = Title_spec_x - (textWidth / 2)
        local titleEndX = Title_spec_x + (textWidth / 2)
        local lineY = Title_spec_y + 16
        Renderer.DrawLine(Vector2D(titleStartX - 2, lineY), Vector2D(titleEndX + 2, lineY), GUI.colors.headerAccent, 1)

        local startY = lineY + 5
        for i, name in ipairs(specList) do
            Renderer.DrawText(font_state.current_font_name, name, Vector2D(Spec_x + (dynWidth / 2), startY + (i-1)*lineHeight), true, false, GUI.colors.text)
        end
    end
end

-- Load the font
Renderer.LoadFontFromFile("TahomaDebug", "Tahoma", 12, true)

-- Input handling function
local function HandleBlockBotInput()
    local keybind = GUI_ELEMENTS.Blockbot_key
    if not keybind then return end

    local key_pressed = Input.GetKeyDown(keybind.boundKey)

    -- Toggle mode: activate on key press (not held)
    if keybind.mode == "Toggle" then
        if key_pressed and not blockbot_state.last_key_state then
            blockbot_state.keybind_active = not blockbot_state.keybind_active -- Toggle the keybind's active state
        end
    -- Hold mode: active while key is held
    elseif keybind.mode == "Hold" then
        blockbot_state.keybind_active = key_pressed -- Keybind active while held
    end

    blockbot_state.last_key_state = key_pressed
end

-- Helper functions
local function fmod(a, b)
    return a - math.floor(a / b) * b
end

local function NormalizeYaw(yaw)
    local sign = 1
    if yaw < 0 then sign = -1 end
    return (fmod(math.abs(yaw) + 180, 360) - 180) * sign
end

local function NormalizeVector(vec)
    local magnitude = math.sqrt(vec.x^2 + vec.y^2 + vec.z^2)
    if magnitude > 1e-4 then -- Avoid division by zero or near-zero
        return Vector(vec.x / magnitude, vec.y / magnitude, vec.z / magnitude)
    else
        return Vector(0, 0, 0)
    end
end

local function CheckSameXY(pos1, pos2, tolerance)
    tolerance = tolerance or 32 -- Default tolerance if not provided
    return math.abs(pos1.x - pos2.x) <= tolerance and math.abs(pos1.y - pos2.y) <= tolerance
end

local function GetTeammateViewYaw(teammatePawn)
    if teammatePawn.m_angEyeAngles then
        return teammatePawn.m_angEyeAngles.y
    end
    -- Fallback to velocity direction if eye angles are not available
    local velocity = teammatePawn.m_vecAbsVelocity or Vector(0,0,0)
    if math.sqrt(velocity.x^2 + velocity.y^2) > 10 then -- Only if moving significantly
        return math.atan2(velocity.y, velocity.x) * (180 / math.pi)
    end
    return 0 -- Default yaw
end

local function IsOnScreen(screenPos)
    if not screenPos or (screenPos.x == 0 and screenPos.y == 0) then return false end
    local screenSize = Renderer.GetScreenSize()
    return screenPos.x >= 0 and screenPos.x <= screenSize.x and screenPos.y >= 0 and screenPos.y <= screenSize.y
end

local function IsTeammateValid(teammatePawn)
    if not teammatePawn or not teammatePawn.m_pGameSceneNode then return false end

    local health = teammatePawn.m_iHealth or 0
    if health <= 0 then return false end

    if teammatePawn.m_lifeState and teammatePawn.m_lifeState ~= 0 then -- LIFE_ALIVE is 0
        return false
    end
    return true
end

-- Find the closest teammate to block
local function FindBlockTeammate()
    local localPlayerControllerPawn = GetLocalPlayerPawn()
    if not localPlayerControllerPawn or not localPlayerControllerPawn.m_pGameSceneNode then
        blockbot_state.blockEnemy = nil; blockbot_state.currentTarget = nil
        blockbot_state.prev_lateral_offset_sign_for_adad = 0; blockbot_state.adad_active_timer = 0
        blockbot_state.last_lateral_change_time = 0; blockbot_state.adad_rhythm_streak = 0
        return
    end

    local localPlayerOrigin = localPlayerControllerPawn.m_pGameSceneNode.m_vecAbsOrigin
    local localPlayerTeam = localPlayerControllerPawn.m_iTeamNum

    -- Target stickiness: if current target is still valid and relatively close, keep them.
    if blockbot_state.currentTarget and IsTeammateValid(blockbot_state.currentTarget) then
        if blockbot_state.currentTarget.m_pGameSceneNode then -- Ensure scene node exists before accessing origin
             if localPlayerOrigin:DistTo(blockbot_state.currentTarget.m_pGameSceneNode.m_vecAbsOrigin) < 1000 then
                blockbot_state.blockEnemy = blockbot_state.currentTarget
                return
             end
        end
    end

    -- Reset current target and search for a new one
    blockbot_state.currentTarget = nil
    local closestDistance = math.huge
    local bestTeammatePawn = nil
    local highestIndex = Entities.GetHighestEntityIndex() or 0

    for i = 1, highestIndex do
        local entity = Entities.GetEntityFromIndex(i)
        if entity and entity.m_bIsLocalPlayerController ~= nil and not entity.m_bIsLocalPlayerController and entity.m_hPawn then
            local potentialTeammatePawn = entity.m_hPawn
            if potentialTeammatePawn and potentialTeammatePawn.m_iTeamNum == localPlayerTeam and potentialTeammatePawn ~= localPlayerControllerPawn then
                if IsTeammateValid(potentialTeammatePawn) and potentialTeammatePawn.m_pGameSceneNode then
                    local teammateOrigin = potentialTeammatePawn.m_pGameSceneNode.m_vecAbsOrigin
                    local distanceToTeammate = localPlayerOrigin:DistTo(teammateOrigin)

                    -- Consider a teammate if they are close enough and closer than the current best
                    if distanceToTeammate > 1 and distanceToTeammate < 800 and distanceToTeammate < closestDistance then
                        closestDistance = distanceToTeammate
                        bestTeammatePawn = potentialTeammatePawn
                    end
                end
            end
        end
    end

    blockbot_state.blockEnemy = bestTeammatePawn
    blockbot_state.currentTarget = bestTeammatePawn -- Set for stickiness next frame

    if not blockbot_state.blockEnemy then -- If no target found, reset ADAD state
        blockbot_state.prev_lateral_offset_sign_for_adad = 0
        blockbot_state.adad_active_timer = 0
        blockbot_state.last_lateral_change_time = 0; blockbot_state.adad_rhythm_streak = 0
    end
end

--// CORE BLOCKBOT LOGIC //--
local function BlockbotLogic(cmd)
    if not cmd then return end

    local localPlayerPawn = GetLocalPlayerPawn()
    local local_ping = GetLocalPlayerPing()

    -- Convert ping to seconds for prediction offset
    local ping_offset_seconds = local_ping / 1000.0

    -- Calculate actual frametime for prediction, with a safe default
    local actualFrameTime = Globals.GetFrameTime() or 0.015625
    if actualFrameTime <= 0 then actualFrameTime = 0.015625 end -- Ensure positive frametime
    local predictionFrameTime = math.min(actualFrameTime, blockbot_constants.MAX_PREDICTION_FRAMETIME) -- Cap frametime for prediction

    if not Globals.IsConnected() then
        if blockbot_state.bot_has_active_jump_command then CVar.ExecuteClientCmd("-jump"); blockbot_state.bot_has_active_jump_command = false end
        blockbot_state.blockEnemy = nil; blockbot_state.currentTarget = nil
        blockbot_state.prev_lateral_offset_sign_for_adad = 0; blockbot_state.adad_active_timer = 0
        blockbot_state.last_lateral_change_time = 0; blockbot_state.adad_rhythm_streak = 0
        blockbot_state.keybind_active = false -- Reset keybind state on disconnect
        return
    end

    -- Handle local player state (on ground, jump commands)
    local is_on_ground_this_frame = true
    if localPlayerPawn and localPlayerPawn.m_pGameSceneNode then
        if localPlayerPawn.m_fFlags ~= nil then
            is_on_ground_this_frame = bit.band(localPlayerPawn.m_fFlags, 1) ~= 0 -- FL_ONGROUND
        end
        -- If a jump was commanded and we are now on ground, release jump key
        if blockbot_state.bot_has_active_jump_command and is_on_ground_this_frame then
            CVar.ExecuteClientCmd("-jump")
            blockbot_state.bot_has_active_jump_command = false
        end
    else
        -- Invalid local player, reset jump state and exit
        if blockbot_state.bot_has_active_jump_command then CVar.ExecuteClientCmd("-jump"); blockbot_state.bot_has_active_jump_command = false end
        blockbot_state.blockEnemy = nil; blockbot_state.currentTarget = nil
        blockbot_state.prev_lateral_offset_sign_for_adad = 0; blockbot_state.adad_active_timer = 0
        blockbot_state.last_lateral_change_time = 0; blockbot_state.adad_rhythm_streak = 0
        return
    end

    -- Call HandleBlockBotInput to update blockbot_state.keybind_active
    HandleBlockBotInput()

    -- Check if blockbot is globally enabled by the checkbox AND active via keybind
    if not gui_options.blockbot_enable or not blockbot_state.keybind_active then
        -- If blockbot is not active, ensure jump command is released
        if blockbot_state.bot_has_active_jump_command then CVar.ExecuteClientCmd("-jump"); blockbot_state.bot_has_active_jump_command = false end
        -- Reset other blockbot state variables if not active
        blockbot_state.blockEnemy = nil; blockbot_state.currentTarget = nil
        blockbot_state.prev_lateral_offset_sign_for_adad = 0; blockbot_state.adad_active_timer = 0
        blockbot_state.last_lateral_change_time = 0; blockbot_state.adad_rhythm_streak = 0
        return
    end

    FindBlockTeammate() -- Find or update the teammate to block

    -- Initialize acceleration variables
    local accel_x, accel_y = 0, 0

    -- Validate block target
    if not blockbot_state.blockEnemy or not blockbot_state.blockEnemy.m_pGameSceneNode or not IsTeammateValid(blockbot_state.blockEnemy) then
        if blockbot_state.bot_has_active_jump_command then CVar.ExecuteClientCmd("-jump"); blockbot_state.bot_has_active_jump_command = false end
        blockbot_state.blockEnemy = nil; blockbot_state.currentTarget = nil;
        -- Reset acceleration prediction state
        blockbot_state.prev_block_enemy_ref_for_accel = nil
        blockbot_state.prev_target_pos_for_accel = nil
        blockbot_state.prev_target_vel_for_accel = nil
        -- Reset ADAD state
        blockbot_state.prev_lateral_offset_sign_for_adad = 0
        blockbot_state.adad_active_timer = 0
        blockbot_state.last_lateral_change_time = 0; blockbot_state.adad_rhythm_streak = 0
        return
    end

    -- Get teammate's ping and convert to seconds for prediction offset
    local teammate_ping = blockbot_state.blockEnemy.m_iPing or 0
    local teammate_ping_offset_seconds = teammate_ping / 1000.0

    -- Get positions and velocities
    local localPos = localPlayerPawn.m_pGameSceneNode.m_vecAbsOrigin
    local teammatePos = blockbot_state.blockEnemy.m_pGameSceneNode.m_vecAbsOrigin
    local teammateVel = blockbot_state.blockEnemy.m_vecAbsVelocity or Vector(0,0,0)
    local teammateSpeedXY = math.sqrt(teammateVel.x^2 + teammateVel.y^2)

    -- Calculate target's acceleration
    if blockbot_state.prev_block_enemy_ref_for_accel ~= blockbot_state.blockEnemy or not blockbot_state.prev_target_pos_for_accel or not blockbot_state.prev_target_vel_for_accel then
        -- New target or first time, initialize previous state
        blockbot_state.prev_target_pos_for_accel = Vector(teammatePos.x, teammatePos.y, teammatePos.z)
        blockbot_state.prev_target_vel_for_accel = Vector(teammateVel.x, teammateVel.y, teammateVel.z)
        blockbot_state.prev_actual_frame_time_for_accel_calc = actualFrameTime
        blockbot_state.prev_block_enemy_ref_for_accel = blockbot_state.blockEnemy
    else
        -- Calculate acceleration based on change in velocity over time
        if blockbot_state.prev_actual_frame_time_for_accel_calc > blockbot_constants.VIEW_ANGLES_MIN_VALID_PREV_FRAME_TIME then
            local delta_vx = teammateVel.x - blockbot_state.prev_target_vel_for_accel.x
            local delta_vy = teammateVel.y - blockbot_state.prev_target_vel_for_accel.y
            accel_x = delta_vx / blockbot_state.prev_actual_frame_time_for_accel_calc
            accel_y = delta_vy / blockbot_state.prev_actual_frame_time_for_accel_calc

            -- Apply damping to smooth out acceleration values
            accel_x = accel_x * blockbot_constants.VIEW_ANGLES_ACCEL_DAMPING_FACTOR
            accel_y = accel_y * blockbot_constants.VIEW_ANGLES_ACCEL_DAMPING_FACTOR
        end
        -- Update previous state for next frame's calculation
        blockbot_state.prev_target_pos_for_accel = Vector(teammatePos.x, teammatePos.y, teammatePos.z)
        blockbot_state.prev_target_vel_for_accel = Vector(teammateVel.x, teammateVel.y, teammateVel.z)
        blockbot_state.prev_actual_frame_time_for_accel_calc = actualFrameTime
    end

    -- Check if local player is on the target's head
    local isOnHead = (localPos.z - teammatePos.z) > blockbot_constants.ON_HEAD_Z_THRESHOLD and
                     CheckSameXY(localPos, teammatePos, blockbot_constants.ON_HEAD_XY_TOLERANCE)

    -- Auto-jump logic: if enabled, not on head, target is jumping, and we are on ground
    if gui_options.autojump_enable and
       not isOnHead and
       math.abs(teammateVel.z) > blockbot_constants.AUTOJUMP_TARGET_Z_VEL_THRESHOLD and
       is_on_ground_this_frame and
       not blockbot_state.bot_has_active_jump_command then
        CVar.ExecuteClientCmd("+jump")
        blockbot_state.bot_has_active_jump_command = true
    end

    -- Decrement ADAD active timer
    if blockbot_state.adad_active_timer > 0 then
        blockbot_state.adad_active_timer = blockbot_state.adad_active_timer - actualFrameTime
        if blockbot_state.adad_active_timer < 0 then blockbot_state.adad_active_timer = 0 end
    end
    local is_adad_currently_active = blockbot_state.adad_active_timer > 0

    --// MOVEMENT LOGIC BRANCH: ON-HEAD OR GROUND //--
    if isOnHead then
        local predFrames = blockbot_constants.ON_HEAD_PREDICTION_FRAMES
        -- Add both local and teammate ping to prediction time
        local total_pred_time = (predictionFrameTime * predFrames) + ping_offset_seconds + teammate_ping_offset_seconds

        local predictedTeammatePos = Vector(
            teammatePos.x + teammateVel.x * total_pred_time,
            teammatePos.y + teammateVel.y * total_pred_time,
            teammatePos.z + teammateVel.z * total_pred_time
        )

        -- Target position: center of the head (X, Y of predicted teammate origin, Z at head height)
        local targetPos = Vector(predictedTeammatePos.x, predictedTeammatePos.y, predictedTeammatePos.z + blockbot_constants.ON_HEAD_HEIGHT_OFFSET)

        -- Calculate needed movement relative to local player (only XY for ground movement)
        local neededMovement = Vector(targetPos.x - localPos.x, targetPos.y - localPos.y, 0)

        local finalForwardMove = 0.0
        local finalLeftMove = 0.0

        local horizontalDistanceToTarget = math.sqrt(neededMovement.x^2 + neededMovement.y^2)

        -- Apply movement only if outside the deadzone
        if horizontalDistanceToTarget > blockbot_constants.ON_HEAD_DEADZONE_HORIZONTAL then
            -- Normalize the needed movement to get a direction vector using our custom function
            local normalizedNeededMovement = NormalizeVector(neededMovement)

            local ourViewRadians = math.rad(cmd.m_angViewAngles.y)
            local cos_yaw = math.cos(ourViewRadians)
            local sin_yaw = math.sin(ourViewRadians)

            -- Calculate correction speed based on distance error
            local correctionSpeedFactor = math.min(horizontalDistanceToTarget / blockbot_constants.MAX_CORRECTION_DISTANCE, 1.0)
            local desiredTotalSpeed = teammateSpeedXY + (blockbot_constants.MAX_PLAYER_SPEED * correctionSpeedFactor)

            -- Clamp desired total speed to MAX_PLAYER_SPEED
            desiredTotalSpeed = math.min(desiredTotalSpeed, blockbot_constants.MAX_PLAYER_SPEED)

            -- Calculate movement scale based on desired total speed
            local movementScale = 0
            if blockbot_constants.MAX_PLAYER_SPEED > 1e-5 then
                movementScale = desiredTotalSpeed / blockbot_constants.MAX_PLAYER_SPEED
            end

            -- Convert normalizedNeededMovement to local player's forward/left axes
            -- And apply scaled speed
            finalForwardMove = (normalizedNeededMovement.x * cos_yaw + normalizedNeededMovement.y * sin_yaw) * movementScale
            finalLeftMove = (-normalizedNeededMovement.x * sin_yaw + normalizedNeededMovement.y * cos_yaw) * movementScale

            -- Clamp to -1.0 to 1.0 (full movement) - this is still important for safety
            finalForwardMove = math.max(-1.0, math.min(1.0, finalForwardMove))
            finalLeftMove = math.max(-1.0, math.min(1.0, finalLeftMove))
        end
        -- If within deadzone, finalForwardMove and finalLeftMove remain 0.0, effectively quick stopping.

        cmd.m_flForwardMove = finalForwardMove
        cmd.m_flLeftMove = finalLeftMove
    else -- GROUND LOGIC (View Angles or Front Block)
        local selectedMode = gui_options.blockbotmode

        if selectedMode == 0 then -- View Angles Mode
            cmd.m_flForwardMove = 0.0 -- Ensure no forward/backward movement in this mode
            cmd.m_flLeftMove = 0.0

            -- Adaptive Prediction: Adjust prediction frames based on target speed (when not in ADAD mode)
            local speed_factor = math.max(0, math.min(1, teammateSpeedXY / blockbot_constants.MAX_PLAYER_SPEED)) -- Normalize speed 0-1
            local dynamic_pred_frames_base = blockbot_constants.VIEW_ANGLES_PREDICTION_FRAMES_ACCEL_BASE_MIN +
                                             (blockbot_constants.VIEW_ANGLES_PREDICTION_FRAMES_ACCEL_BASE_MAX - blockbot_constants.VIEW_ANGLES_PREDICTION_FRAMES_ACCEL_BASE_MIN) * speed_factor

            local current_prediction_frames_accel = dynamic_pred_frames_base
            if is_adad_currently_active then
                current_prediction_frames_accel = blockbot_constants.VIEW_ANGLES_PREDICTION_FRAMES_ACCEL_ADAD -- Override with ADAD specific short prediction
            end

            -- Calculate predicted target position using velocity and acceleration
            -- Add both local and teammate ping to prediction time
            local pred_time_seconds = (predictionFrameTime * current_prediction_frames_accel) + ping_offset_seconds + teammate_ping_offset_seconds

            local predicted_x = teammatePos.x + (teammateVel.x * pred_time_seconds) + (0.5 * accel_x * pred_time_seconds^2)
            local predicted_y = teammatePos.y + (teammateVel.y * pred_time_seconds) + (0.5 * accel_y * pred_time_seconds^2)
            local targetPosForLateralCalc = Vector(predicted_x, predicted_y, teammatePos.z)

            -- Calculate lateral offset: how far left/right the target is relative to our facing direction
            local vectorToTarget = Vector(targetPosForLateralCalc.x - localPos.x, targetPosForLateralCalc.y - localPos.y, 0)
            local currentYawRad = math.rad(cmd.m_angViewAngles.y)
            local localRightVectorX = math.sin(currentYawRad)
            local localRightVectorY = -math.cos(currentYawRad)
            local lateralOffset = vectorToTarget.x * localRightVectorX + vectorToTarget.y * localRightVectorY

            -- ADAD Detection Logic: Check for reversals in lateral movement
            local current_lateral_offset_sign = 0
            if math.abs(lateralOffset) > blockbot_constants.ADAD_MIN_LATERAL_OFFSET_FOR_SIGN then
                 current_lateral_offset_sign = lateralOffset > 0 and 1 or -1 -- 1 for right, -1 for left
            end

            local current_time = Globals.GetCurrentTime() or 0

            if teammateSpeedXY > blockbot_constants.ADAD_DETECTION_MIN_SPEED_XY and
               current_lateral_offset_sign ~= 0 and
               blockbot_state.prev_lateral_offset_sign_for_adad ~= 0 and
               current_lateral_offset_sign ~= blockbot_state.prev_lateral_offset_sign_for_adad then -- Sign must have changed (reversal)

                local time_since_last_change = current_time - blockbot_state.last_lateral_change_time
                if time_since_last_change > 0 and time_since_last_change <= blockbot_constants.ADAD_RHYTHM_WINDOW_SECONDS then
                    blockbot_state.adad_rhythm_streak = blockbot_state.adad_rhythm_streak + 1
                else
                    blockbot_state.adad_rhythm_streak = 1 -- Reset streak if rhythm is broken or first reversal
                end
                blockbot_state.last_lateral_change_time = current_time -- Update last change time

                if blockbot_state.adad_rhythm_streak >= blockbot_constants.ADAD_MIN_RHYTHM_COUNT then
                    blockbot_state.adad_active_timer = blockbot_constants.ADAD_COUNTER_DURATION_SECONDS -- Activate/Refresh ADAD countermeasures
                    is_adad_currently_active = true                   -- Update for this frame's logic
                end
            else
                -- If no reversal or conditions not met, reset streak
                blockbot_state.adad_rhythm_streak = 0
                blockbot_state.last_lateral_change_time = current_time -- Keep updating for future checks
            end
            blockbot_state.prev_lateral_offset_sign_for_adad = current_lateral_offset_sign -- Store for next frame's comparison

            -- Apply Dynamic Deadzone and Strafe Power based on ADAD state
            local effective_deadzone = blockbot_constants.VIEW_ANGLES_LATERAL_OFFSET_DEADZONE_BASE
            local effective_strafe_power = blockbot_constants.VIEW_ANGLES_MAX_STRAFE_POWER_BASE
            if is_adad_currently_active then
                effective_deadzone = blockbot_constants.VIEW_ANGLES_LATERAL_OFFSET_DEADZONE_ADAD
                effective_strafe_power = blockbot_constants.VIEW_ANGLES_MAX_STRAFE_POWER_ADAD
            end

            -- Apply strafe movement if outside the deadzone
            if math.abs(lateralOffset) > effective_deadzone then
                if lateralOffset > 0 then
                    cmd.m_flLeftMove = -effective_strafe_power -- Target is to our right, so we move left
                else
                    cmd.m_flLeftMove = effective_strafe_power  -- Target is to our left, so we move right
                end
            end

        elseif selectedMode == 1 then -- Front Block Mode
            -- Reset ADAD state if switching out of View Angles mode
            blockbot_state.prev_lateral_offset_sign_for_adad = 0
            blockbot_state.adad_active_timer = 0
            blockbot_state.last_lateral_change_time = 0; blockbot_state.adad_rhythm_streak = 0
            is_adad_currently_active = false

            local predFramesFB = blockbot_constants.FRONT_BLOCK_PREDICTION_FRAMES
            -- Add both local and teammate ping to prediction time
            local total_pred_time_fb = (predictionFrameTime * predFramesFB) + ping_offset_seconds + teammate_ping_offset_seconds

            local predTargetPosFB = Vector(
                teammatePos.x + teammateVel.x * total_pred_time_fb,
                teammatePos.y + teammateVel.y * total_pred_time_fb,
                teammatePos.z
            )

            local targetForwardAngleDegreesFB
            if teammateSpeedXY > blockbot_constants.FRONT_BLOCK_VELOCITY_THRESHOLD_FOR_DIRECTION and (teammateVel.x ~= 0 or teammateVel.y ~= 0) then
                targetForwardAngleDegreesFB = math.atan2(teammateVel.y, teammateVel.x) * (180 / math.pi) -- Use velocity direction
            else
                targetForwardAngleDegreesFB = GetTeammateViewYaw(blockbot_state.blockEnemy) -- Use teammate's view yaw
            end

            local angleRadiansFB = math.rad(targetForwardAngleDegreesFB)
            local blockPositionFB = Vector(
                predTargetPosFB.x + math.cos(angleRadiansFB) * blockbot_constants.FRONT_BLOCK_DISTANCE,
                predTargetPosFB.y + math.sin(angleRadiansFB) * blockbot_constants.FRONT_BLOCK_DISTANCE,
                predTargetPosFB.z + blockbot_constants.FRONT_BLOCK_HEIGHT_OFFSET
            )

            local neededMoveFB = Vector(blockPositionFB.x - localPos.x, blockPositionFB.y - localPos.y, 0)
            local distToTargetXY_FB = math.sqrt(neededMoveFB.x^2 + neededMoveFB.y^2)

            local fwdMoveFB, leftMoveFB = 0.0, 0.0
            if distToTargetXY_FB > blockbot_constants.FRONT_BLOCK_DEADZONE_HORIZONTAL then
                local corrTimeFB = predictionFrameTime * math.max(0.001, blockbot_constants.FRONT_BLOCK_CORRECTION_TIMESCALE_FRAMES)
                if corrTimeFB <= 1e-5 then corrTimeFB = 1e-5 end

                local speedGapCloseFB = (distToTargetXY_FB / corrTimeFB) * blockbot_constants.FRONT_BLOCK_CORRECTION_GAIN
                local desiredSpeedFB = math.min(teammateSpeedXY + speedGapCloseFB, blockbot_constants.MAX_PLAYER_SPEED)

                local normMoveFB = NormalizeVector(neededMoveFB)
                local viewRadFB = math.rad(cmd.m_angViewAngles.y)
                local cosY_fb, sinY_fb = math.cos(viewRadFB), math.sin(viewRadFB)

                local moveScaleFB = 0
                if blockbot_constants.MAX_PLAYER_SPEED > 0.001 then moveScaleFB = desiredSpeedFB / blockbot_constants.MAX_PLAYER_SPEED end

                fwdMoveFB = (normMoveFB.x * cosY_fb + normMoveFB.y * sinY_fb) * moveScaleFB
                leftMoveFB = (-normMoveFB.x * sinY_fb + normMoveFB.y * cosY_fb) * moveScaleFB
            end
            cmd.m_flForwardMove = math.max(-1, math.min(1, fwdMoveFB))
            cmd.m_flLeftMove = math.max(-1, math.min(1, leftMoveFB))
        end
    end
end

local function DrawPlayerIndicators()
    if not gui_options.blockbot_enable then return end
    if not blockbot_state.blockEnemy or not blockbot_state.blockEnemy.m_pGameSceneNode then return end

    local teammatePosRaw = blockbot_state.blockEnemy.m_pGameSceneNode.m_vecAbsOrigin
    local teammateVel = blockbot_state.blockEnemy.m_vecAbsVelocity or Vector(0,0,0)

    local actualFrameTime = Globals.GetFrameTime() or 0.015625
    if actualFrameTime <= 0 then actualFrameTime = 0.015625 end
    local predFrameTimeForVisuals = math.min(actualFrameTime, blockbot_constants.MAX_PREDICTION_FRAMETIME)

    local visualPredictionFrames = 4 -- How many frames ahead to predict for the visual indicator
    local predictedTeammateVisualPos = Vector(
        teammatePosRaw.x + teammateVel.x * predFrameTimeForVisuals * visualPredictionFrames,
        teammatePosRaw.y + teammateVel.y * predFrameTimeForVisuals * visualPredictionFrames,
        teammatePosRaw.z + teammateVel.z * predFrameTimeForVisuals * visualPredictionFrames
    )

    -- Interpolate visual position for smoothness
    local interpolatedPos
    if blockbot_state.lastDrawnTeammatePos then
        interpolatedPos = Vector(
            blockbot_state.lastDrawnTeammatePos.x + (predictedTeammateVisualPos.x - blockbot_state.lastDrawnTeammatePos.x) * blockbot_constants.INTERPOLATION_ALPHA,
            blockbot_state.lastDrawnTeammatePos.y + (predictedTeammateVisualPos.y - blockbot_state.lastDrawnTeammatePos.y) * blockbot_constants.INTERPOLATION_ALPHA,
            blockbot_state.lastDrawnTeammatePos.z + (predictedTeammateVisualPos.z - blockbot_state.lastDrawnTeammatePos.z) * blockbot_constants.INTERPOLATION_ALPHA
        )
    else
        interpolatedPos = predictedTeammateVisualPos
    end
    blockbot_state.lastDrawnTeammatePos = interpolatedPos -- Store for next frame's interpolation

    -- Get screen positions for drawing
    local screenPosTargetFeet = Renderer.WorldToScreen(interpolatedPos)

    if not IsOnScreen(screenPosTargetFeet) then return end

    -- Define colors based on menu settings or defaults
    local baseCircleColor = gui_options.blockbot_circle_color -- Cyan
    local onHeadCircleColor = gui_options.blockbot_on_head_color -- Yellow

    local localPlayerPawnForDraw = GetLocalPlayerPawn()
    if not localPlayerPawnForDraw or not localPlayerPawnForDraw.m_pGameSceneNode then return end

    local localPlayerPosForDraw = localPlayerPawnForDraw.m_pGameSceneNode.m_vecAbsOrigin
    local currentTargetPosForDraw = blockbot_state.blockEnemy.m_pGameSceneNode.m_vecAbsOrigin

    local isPlayerOnHeadForDraw = (localPlayerPosForDraw.z - currentTargetPosForDraw.z) > blockbot_constants.ON_HEAD_Z_THRESHOLD and CheckSameXY(localPlayerPosForDraw, currentTargetPosForDraw, blockbot_constants.ON_HEAD_XY_TOLERANCE)

    -- Draw main circle around target
    if IsOnScreen(screenPosTargetFeet) then
        if isPlayerOnHeadForDraw then
            Renderer.DrawCircleGradient3D(interpolatedPos, onHeadCircleColor, Color(onHeadCircleColor.r, onHeadCircleColor.g, onHeadCircleColor.b, 100), 25)
            Renderer.DrawCircle3D(interpolatedPos, onHeadCircleColor, 35)
        else
            Renderer.DrawCircleGradient3D(interpolatedPos, baseCircleColor, Color(baseCircleColor.r, baseCircleColor.g, baseCircleColor.b, 50), 20)

            -- Update animated circle phase
            blockbot_state.animated_circle_phase = blockbot_state.animated_circle_phase + (Globals.GetFrameTime() * blockbot_constants.ANIMATED_CIRCLE_SPEED)
            if blockbot_state.animated_circle_phase > math.pi * 2 then
                blockbot_state.animated_circle_phase = blockbot_state.animated_circle_phase - (math.pi * 2)
            end

            -- Calculate Z offset for the animated circle (moves from feet to head)
            local z_offset_animated_circle = blockbot_constants.ANIMATED_CIRCLE_BASE_Z_OFFSET + (math.sin(blockbot_state.animated_circle_phase) * 0.5 + 0.5) * blockbot_constants.ANIMATED_CIRCLE_HEIGHT_RANGE

            local animatedCirclePos = Vector(interpolatedPos.x, interpolatedPos.y, interpolatedPos.z + z_offset_animated_circle)

            -- Draw animated circle
            Renderer.DrawCircle3D(animatedCirclePos, baseCircleColor, blockbot_constants.ANIMATED_CIRCLE_RADIUS)
        end
    end

    -- Draw line from local player to target (if not on head)
    if not isPlayerOnHeadForDraw then
        local localPlayerScreenPos = Renderer.WorldToScreen(localPlayerPosForDraw)
        if IsOnScreen(localPlayerScreenPos) and IsOnScreen(screenPosTargetFeet) then
            -- Use the baseCircleColor from the theme for the line
            Renderer.DrawLine(localPlayerScreenPos, screenPosTargetFeet, Color(baseCircleColor.r, baseCircleColor.g, baseCircleColor.b, 100), 2)
        end
    end
end

local particles = {}

local ParticleConfig = {
    PARTICLE_MIN_SIZE = 3,
    PARTICLE_MAX_SIZE = 8,
    PARTICLE_MIN_VELOCITY = -50,
    PARTICLE_MAX_VELOCITY = 50,
    PARTICLE_LIFETIME_SECONDS = 7,
    spawnTimer = 0,
    backgroundAlpha = 0,
    BACKGROUND_FADE_SPEED = 80,
    hasBackgroundFadedIn = false,
}

local function random_float(min, max)
    return min + (math.random() * (max - min))
end

local function ParticalsRender(amount)
    if global_ui_state.isIntroActive then return end
    local screenSize = Renderer.GetScreenSize()
    local deltaTime = Globals.CurDelta or 0.016

    if amount == 0 then
        particles = {}
        ParticleConfig.spawnTimer = 0
        ParticleConfig.backgroundAlpha = 0
        ParticleConfig.hasBackgroundFadedIn = false
        Renderer.DrawRectFilled(Vector2D(0, 0), screenSize, Color(0, 0, 0, 0))
        return
    end

    if Input.IsMenuOpen() then
        if not ParticleConfig.hasBackgroundFadedIn then
            ParticleConfig.backgroundAlpha = math.min(180, ParticleConfig.backgroundAlpha + ParticleConfig.BACKGROUND_FADE_SPEED * deltaTime)
            if ParticleConfig.backgroundAlpha >= 180 then
                ParticleConfig.hasBackgroundFadedIn = true
            end
        end

        -- Use GUI.colors.windowOutline for the particle background
        Renderer.DrawRectFilled(Vector2D(0, 0), screenSize, Color(0,0,0,ParticleConfig.backgroundAlpha))

        ParticleConfig.spawnTimer = ParticleConfig.spawnTimer + deltaTime

        local targetSpawnRatePerSecond = amount / ParticleConfig.PARTICLE_LIFETIME_SECONDS
        local spawnInterval = (targetSpawnRatePerSecond > 0) and (1 / targetSpawnRatePerSecond) or 0

        while #particles < amount and ParticleConfig.spawnTimer >= spawnInterval and spawnInterval > 0 do
            local x = random_float(0, screenSize.x)
            local y = random_float(0, screenSize.y)

            local velX = random_float(ParticleConfig.PARTICLE_MIN_VELOCITY, ParticleConfig.PARTICLE_MAX_VELOCITY)
            local velY = random_float(ParticleConfig.PARTICLE_MIN_VELOCITY, ParticleConfig.PARTICLE_MAX_VELOCITY)

            local size = random_float(ParticleConfig.PARTICLE_MIN_SIZE, ParticleConfig.PARTICLE_MAX_SIZE)
            local initialAlpha = math.random(150, 255)

            -- Dynamically generate particle colors based on GUI.colors.headerAccent
            local baseR, baseG, baseB = GUI.colors.headerAccent.r, GUI.colors.headerAccent.g, GUI.colors.headerAccent.b
            local r = math.max(0, math.min(255, baseR + math.random(-50, 50)))
            local g = math.max(0, math.min(255, baseG + math.random(-50, 50)))
            local b = math.max(0, math.min(255, baseB + math.random(-50, 50)))
            local color = Color(r, g, b, initialAlpha)

            table.insert(particles, {
                pos = Vector2D(x, y),
                vel = Vector2D(velX, velY),
                size = size,
                color = color,
                currentAlpha = initialAlpha,
                maxLife = ParticleConfig.PARTICLE_LIFETIME_SECONDS,
                lifeElapsed = 0
            })
            ParticleConfig.spawnTimer = ParticleConfig.spawnTimer - spawnInterval
        end

        while #particles > amount do
            table.remove(particles, 1)
        end

        for i = #particles, 1, -1 do
            local p = particles[i]

            p.lifeElapsed = p.lifeElapsed + deltaTime

            p.pos.x = p.pos.x + p.vel.x * deltaTime
            p.pos.y = p.pos.y + p.vel.y * deltaTime

            local fadeProgress = p.lifeElapsed / p.maxLife
            p.currentAlpha = math.max(0, p.color.a * (1 - fadeProgress))

            p.color = Color(p.color.r, p.color.g, p.color.b, p.currentAlpha)

            local buffer = p.size * 2
            if p.currentAlpha <= 0 or
               p.pos.x < -buffer or p.pos.x > screenSize.x + buffer or
               p.pos.y < -buffer or p.pos.y > screenSize.y + buffer then
                table.remove(particles, i)
            else
                Renderer.DrawCircleFilled(p.pos, p.color, p.size)
            end
        end
    else
        if #particles > 0 then
            particles = {}
        end
        ParticleConfig.spawnTimer = 0
    end
end

-- Register callbacks
Cheat.RegisterCallback("OnPreCreateMove", function(cmd)
    BlockbotLogic(cmd)
end)

Cheat.RegisterCallback("OnRenderer", function()
    DrawPlayerIndicators()
end)
Cheat.RegisterCallback("OnRenderer", function()
    ParticalsRender(gui_options.particle_amount)
end)

-- cheat call backs
Cheat.RegisterCallback("OnFireGameEvent", OnFireGameEvent) -- damage indicator event handling
Cheat.RegisterCallback("OnFireGameEvent", hit) -- hitmarkers event handling
Cheat.RegisterCallback("OnFireGameEvent", team_dmg_event) -- team_dmg event handling
Cheat.RegisterCallback("OnFireGameEvent", hit_sound_event) -- hit sound event handling
Cheat.RegisterCallback("OnFireGameEvent", Sounds) -- Sound indicators event handling
Cheat.RegisterCallback("OnRenderer", OnDraw) -- Sound indicators rendering
Cheat.RegisterCallback("OnPostCreateMove", FakePitch)
-- Register separate OnRenderer callbacks
Cheat.RegisterCallback("OnRenderer", OnRendererWatermark) -- Watermark rendering and dragging
Cheat.RegisterCallback("OnRenderer", OnRendererDamageIndicators) -- Damage Indicator rendering and dragging
Cheat.RegisterCallback("OnRenderer", hitmarkers_render) -- Hitmarkers rendering
Cheat.RegisterCallback("OnRenderer", OnRendererTeamDamage) -- Team Damage Tracker rendering
Cheat.RegisterCallback("OnRenderer", OnRendererPlayerTrails) -- Player Trails rendering
Cheat.RegisterCallback("OnRenderer", RenderSpecs) -- Spectator List rendering

local hitsound_picker = {
    "Neverlose",
    "Fatality",
    "Minecraft",
    "Call of Duty",
}
local Color_Picker = {
    "Rainbow",
    "Custom",
}

local function SetupGUIMenu()
    if not global_ui_state.guiMenuInitialized then
        if GUI.CreatePropperMenuLayout then
            local mainMenu = GUI.CreatePropperMenuLayout({
                windowTitle = "BLOODLINE",
                topRightText = "V 1.0",
                x = 100,
                y = 100,
                width = 500,
                height = 280,
                categories = {"General", "Trails", "Anti-Aim","BlockBot","Sounds", "Misc", "Config"},
            })

            -- GENERAL CATEGORY
            GUI.BeginCategory("General")
            GUI_ELEMENTS.font_size_slider = GUI.MenuSlider("Font Size", 1, 25, gui_options.font_size, function(value)
                gui_options.font_size = value
            end)
            GUI_ELEMENTS.particle_amount = GUI.MenuSlider("Particle Amount", 0, 5000, gui_options.particle_amount, function(value)
                gui_options.particle_amount = value
            end)
            local themes = {"Bloodline", "Crimson Twilight", "Cyber Mint", "Solar Flare", "Midnight Violet", "Ocean Breeze"}
            GUI_ELEMENTS.theme = GUI.MenuCombobox("Themes", themes, gui_options.theme, function(index)
                gui_options.theme = index
                print(index)
                print(themes[gui_options.theme + 1])
                GUI.SetTheme(themes[gui_options.theme + 1])
            end)
            GUI.BeginCategory("Misc")
            GUI_ELEMENTS.enable_watermark_cb = GUI.MenuCheckbox("Enable Watermark", gui_options.watermark_enable, function(checked)
                gui_options.watermark_enable = checked
            end)

            GUI_ELEMENTS.enable_hitlogs_cb = GUI.MenuCheckbox("Enable Hitlogs", gui_options.hitlogs_enable, function(checked)
                gui_options.hitlogs_enable = checked
            end)

            GUI_ELEMENTS.enable_dmgmarkers_cb = GUI.MenuCheckbox("Enable Dmg Markers", gui_options.dmgmarkers_enable, function(checked)
                gui_options.dmgmarkers_enable = checked
            end)

            GUI_ELEMENTS.enable_team_cb = GUI.MenuCheckbox("Enable Team Dmg Tracker", gui_options.teamlist_enable, function(checked)
                gui_options.teamlist_enable = checked
            end)

            GUI_ELEMENTS.enable_spec_cb = GUI.MenuCheckbox("Enable Spectator List", gui_options.spectator_enable, function(checked)
                gui_options.spectator_enable = checked
            end)

            GUI.BeginCategory("BlockBot")
            GUI_ELEMENTS.blockbot_enable_cb = GUI.MenuCheckbox("Blockbot Enable", gui_options.blockbot_enable, function(checked)
                gui_options.blockbot_enable = checked
                if not checked then -- If master enable is turned off
                    blockbot_state.keybind_active = false -- Deactivate keybind state
                end
            end)
            GUI_ELEMENTS.Blockbot_key = GUI.MenuKeybind("BlockBot Key", gui_options.blockbot_key, gui_options.blockbot_key_mode, function(key, mode)
                gui_options.blockbot_key = key
                gui_options.blockbot_key_mode = mode
            end)
            local blockbot_modes = {"View Angles", "Front Block"}
            GUI_ELEMENTS.blockbot_combo = GUI.MenuCombobox("Blocking Mode",blockbot_modes , gui_options.blockbotmode, function(index)
                gui_options.blockbotmode = index
            end)
            GUI_ELEMENTS.autojump_enable_cb = GUI.MenuCheckbox("Auto Jump", gui_options.autojump_enable, function(checked)
                gui_options.autojump_enable = checked
            end)
            GUI_ELEMENTS.blockbot_circle_color_picker = GUI.MenuColorPicker("Blockbot Circle Color", gui_options.blockbot_circle_color, function(color)
                gui_options.blockbot_circle_color = color
            end)
            GUI_ELEMENTS.blockbot_on_head_color_picker = GUI.MenuColorPicker("Blockbot On Head Color", gui_options.blockbot_on_head_color, function(color)
                gui_options.blockbot_on_head_color = color
            end)

            GUI.BeginCategory("Config")
            GUI.MenuButton("Save Config", function() SaveConfig() end)
            GUI.MenuButton("Load Config", function() LoadConfig() end)

            -- VISUAL CATEGORY
            GUI.BeginCategory("Trails")
            GUI_ELEMENTS.trailsCheckbox = GUI.MenuCheckbox("Enable Trails", gui_options.playertrails_enable, function(checked)
                gui_options.playertrails_enable = checked
            end)

            GUI_ELEMENTS.trailsLengthSlider = GUI.MenuSlider("Trails Length", 0, 100, gui_options.trail_length, function(value)
                gui_options.trail_length = value
            end)
            GUI_ELEMENTS.trailsLengthSlider.depCheckbox = GUI_ELEMENTS.trailsCheckbox

            GUI_ELEMENTS.trailsWidthSlider = GUI.MenuSlider("Trails Width", 0, 10, gui_options.trail_width, function(value)
                gui_options.trail_width = value
            end)
            GUI_ELEMENTS.trailsWidthSlider.depCheckbox = GUI_ELEMENTS.trailsCheckbox

            GUI_ELEMENTS.trailColorPicker_combo = GUI.MenuCombobox("Trail Color Mode", Color_Picker, gui_options.trail_color, function(index)
                gui_options.trail_color = index
            end)

            GUI_ELEMENTS.trailColorPicker = GUI.MenuColorPicker("Trails Color", Color(255,255,255), function(color)
                gui_options.trail_color_picker = color
            end)

            -- ANTI-AIM CATEGORY
            GUI.BeginCategory("Anti-Aim")
            GUI_ELEMENTS.enable_fake_cb = GUI.MenuCheckbox("Unhittable AA", gui_options.fakepitch_enable, function(checked)
                gui_options.fakepitch_enable = checked
            end)

            -- SOUNDS CATEGORY
            GUI.BeginCategory("Sounds")
            GUI_ELEMENTS.hitsoundCheckbox = GUI.MenuCheckbox("Enable Hitsound", gui_options.hitsound_enable, function(checked)
                gui_options.hitsound_enable = checked
            end)

            GUI_ELEMENTS.hitsoundVolumeSlider = GUI.MenuSlider("Hitsound Volume", 1, 100, gui_options.hitsound_volume, function(value)
                gui_options.hitsound_volume = value
            end)
            GUI_ELEMENTS.hitsoundVolumeSlider.depCheckbox = GUI_ELEMENTS.hitsoundCheckbox

            GUI_ELEMENTS.hitsound_combobox = GUI.MenuCombobox("Hitsound", hitsound_picker, gui_options.hitsound, function(index)
                gui_options.hitsound = index
            end)
            GUI_ELEMENTS.sound_enabled_cb = GUI.MenuCheckbox("Sound Esp", gui_options.sound_enabled, function(checked)
                gui_options.sound_enabled = checked
            end)
            GUI_ELEMENTS.sound_peak_radius_slider = GUI.MenuSlider("Peak Radius", 0, 100, gui_options.sound_peak_radius, function(value) -- Changed to gui_options
                gui_options.sound_peak_radius = value
            end)
            GUI_ELEMENTS.sound_peak_radius_slider.depCheckbox = GUI_ELEMENTS.sound_enabled_cb

            GUI_ELEMENTS.sound_draw_always_cb = GUI.MenuCheckbox("Draw Always (Ignore Distance)", gui_options.sound_draw_always, function(checked) -- Changed to gui_options
                gui_options.sound_draw_always = checked
            end)
        else
            -- Fallback for basic GUI creation if CreatePropperMenuLayout fails
            print("Warning: Using fallback GUI creation")
            local menu = GUI.Window("Cheat Options", 50, 50, 400, 400)
            -- Add fallback elements here if needed
        end
        global_ui_state.guiMenuInitialized = true
        -- Initialize start_time for intro animation immediately after GUI setup
        animation_state.start_time = Globals.GetCurrentTime()
        global_ui_state.introStartDelayTime = Globals.GetCurrentTime()
    end
end

-- Removed CreateLoginWindow function

Cheat.RegisterCallback("OnRenderer", function()
    -- Always show the GUI and handle intro animation
    if global_ui_state.isIntroActive then
        RenderIntro()
    else
        SetupGUIMenu()

        -- Load config only once after GUI is initialized
        if global_ui_state.guiMenuInitialized and not global_ui_state.configLoadAttempted then
            LoadConfig()
            global_ui_state.configLoadAttempted = true
        end

        GUI.Render()
    end
end)


local function ExpandConfigData(sectionData)
    -- Handle gui_options section
    if sectionData.gui_options then
        for key, value in pairs(sectionData.gui_options) do
            if key == "font_size" and GUI_ELEMENTS.font_size_slider then
                gui_options.font_size = value
                GUI.SetValue(GUI_ELEMENTS.font_size_slider, value)
            elseif key == "particle_amount" and GUI_ELEMENTS.particle_amount then
                gui_options.particle_amount = value
                GUI.SetValue(GUI_ELEMENTS.particle_amount, value)
            elseif key == "watermark_enable" and GUI_ELEMENTS.enable_watermark_cb then
                gui_options.watermark_enable = value
                GUI.SetValue(GUI_ELEMENTS.enable_watermark_cb, value)
            elseif key == "hitlogs_enable" and GUI_ELEMENTS.enable_hitlogs_cb then
                gui_options.hitlogs_enable = value
                GUI.SetValue(GUI_ELEMENTS.enable_hitlogs_cb, value)
            elseif key == "dmgmarkers_enable" and GUI_ELEMENTS.enable_dmgmarkers_cb then
                gui_options.dmgmarkers_enable = value
                GUI.SetValue(GUI_ELEMENTS.enable_dmgmarkers_cb, value)
            elseif key == "teamlist_enable" and GUI_ELEMENTS.enable_team_cb then
                gui_options.teamlist_enable = value
                GUI.SetValue(GUI_ELEMENTS.enable_team_cb, value)
            elseif key == "spectator_enable" and GUI_ELEMENTS.enable_spec_cb then
                gui_options.spectator_enable = value
                GUI.SetValue(GUI_ELEMENTS.enable_spec_cb, value)
            elseif key == "playertrails_enable" and GUI_ELEMENTS.trailsCheckbox then
                gui_options.playertrails_enable = value
                GUI.SetValue(GUI_ELEMENTS.trailsCheckbox, value)
            elseif key == "trail_length" and GUI_ELEMENTS.trailsLengthSlider then
                gui_options.trail_length = value
                GUI.SetValue(GUI_ELEMENTS.trailsLengthSlider, value)
            elseif key == "trail_width" and GUI_ELEMENTS.trailsWidthSlider then
                gui_options.trail_width = value
                GUI.SetValue(GUI_ELEMENTS.trailsWidthSlider, value)
            elseif key == "trail_color" and GUI_ELEMENTS.trailColorPicker_combo then
                gui_options.trail_color = value
                GUI.SetValue(GUI_ELEMENTS.trailColorPicker_combo, value)
            elseif key == "fakepitch_enable" and GUI_ELEMENTS.enable_fake_cb then
                gui_options.fakepitch_enable = value
                GUI.SetValue(GUI_ELEMENTS.enable_fake_cb, value)
            elseif key == "hitsound_enable" and GUI_ELEMENTS.hitsoundCheckbox then
                gui_options.hitsound_enable = value
                GUI.SetValue(GUI_ELEMENTS.hitsoundCheckbox, value)
            elseif key == "hitsound_volume" and GUI_ELEMENTS.hitsoundVolumeSlider then
                gui_options.hitsound_volume = value
                GUI.SetValue(GUI_ELEMENTS.hitsoundVolumeSlider, value)
            elseif key == "hitsound" and GUI_ELEMENTS.hitsound_combobox then
                gui_options.hitsound = value
                GUI.SetValue(GUI_ELEMENTS.hitsound_combobox, value)
            elseif key == "trail_color_picker_r" then
                local r = value
                local g = sectionData.gui_options["trail_color_picker_g"] or 255
                local b = sectionData.gui_options["trail_color_picker_b"] or 255
                gui_options.trail_color_picker = Color(r, g, b, 255)
                GUI.SetValue(GUI_ELEMENTS.trailColorPicker, gui_options.trail_color_picker)
            elseif key == "blockbotmode" and GUI_ELEMENTS.blockbot_combo then
                gui_options.blockbotmode = value
                GUI.SetValue(GUI_ELEMENTS.blockbot_combo, value)
            -- Fixed keybind loading
            elseif key == "blockbot_key" and GUI_ELEMENTS.Blockbot_key then
                GUI.SetValue(GUI_ELEMENTS.Blockbot_key, value, "boundKey")
            elseif key == "blockbot_key_mode" and GUI_ELEMENTS.Blockbot_key then
                GUI.SetValue(GUI_ELEMENTS.Blockbot_key, value, "mode")
            elseif key == "autojump_enable" and GUI_ELEMENTS.autojump_enable_cb then
                gui_options.autojump_enable = value
                GUI.SetValue(GUI_ELEMENTS.autojump_enable_cb, value)
            elseif key == "blockbot_enable" and GUI_ELEMENTS.blockbot_enable_cb then
                gui_options.blockbot_enable = value
                GUI.SetValue(GUI_ELEMENTS.blockbot_enable_cb, value)
            elseif key == "blockbot_circle_color_r" then
                local r = value
                local g = sectionData.gui_options["blockbot_circle_color_g"] or 0
                local b = sectionData.gui_options["blockbot_circle_color_b"] or 255
                gui_options.blockbot_circle_color = Color(r, g, b, 255)
                GUI.SetValue(GUI_ELEMENTS.blockbot_circle_color_picker, gui_options.blockbot_circle_color)
            elseif key == "blockbot_on_head_color_r" then
                local r = value
                local g = sectionData.gui_options["blockbot_on_head_color_g"] or 255
                local b = sectionData.gui_options["blockbot_on_head_color_b"] or 0
                gui_options.blockbot_on_head_color = Color(r, g, b, 255)
                GUI.SetValue(GUI_ELEMENTS.blockbot_on_head_color_picker, gui_options.blockbot_on_head_color)
            -- Sound Indicators
            elseif key == "sound_enabled" and GUI_ELEMENTS.sound_enabled_cb then
                gui_options.sound_enabled = value
                GUI.SetValue(GUI_ELEMENTS.sound_enabled_cb, value)
            elseif key == "sound_peak_radius" and GUI_ELEMENTS.sound_peak_radius_slider then
                gui_options.sound_peak_radius = value
                GUI.SetValue(GUI_ELEMENTS.sound_peak_radius_slider, value)
            elseif key == "sound_draw_always" and GUI_ELEMENTS.sound_draw_always_cb then
                gui_options.sound_draw_always = value
                GUI.SetValue(GUI_ELEMENTS.sound_draw_always_cb, value)
            elseif key == "theme" and GUI_ELEMENTS.theme then
                gui_options.theme = value
                GUI.SetValue(GUI_ELEMENTS.theme, value)
            end
        end
    end

    -- Handle Script section (positions)
    if sectionData.Script then
        for key, value in pairs(sectionData.Script) do
            if key == "watermark_pos_x" then
                watermark_state.x = value
                watermark_state.saved_x = value
            elseif key == "watermark_pos_y" then
                watermark_state.y = value
                watermark_state.saved_y = value
            elseif key == "hitlog_pos_x" then
                damage_indicator_state.saved_x = value
            elseif key == "hitlog_pos_y" then
                damage_indicator_state.saved_y = value
            elseif key == "team_dmg_tracker_pos_x" then
                team_dmg_tracker_state.saved_x = value
            elseif key == "team_dmg_tracker_pos_y" then
                team_dmg_tracker_state.saved_y = value
            elseif key == "spec_pos_x" then
                spectator_list_state.x = value
            elseif key == "spec_pos_y" then
                spectator_list_state.y = value
            elseif key == "currentAlignment" then
                damage_indicator_state.current_alignment = tostring(value)
            end
        end
    end
end

-- Add this function to flatten your config data
local function FlattenConfigData()
    local t = {
        gui_options = {
            font_size = gui_options.font_size,
            watermark_enable = gui_options.watermark_enable,
            hitlogs_enable = gui_options.hitlogs_enable,
            dmgmarkers_enable = gui_options.dmgmarkers_enable,
            teamlist_enable = gui_options.teamlist_enable,
            spectator_enable = gui_options.spectator_enable,
            fakepitch_enable = gui_options.fakepitch_enable,
            playertrails_enable = gui_options.playertrails_enable,
            trail_length = gui_options.trail_length,
            trail_width = gui_options.trail_width,
            trail_color = gui_options.trail_color,
            trail_color_picker_r = gui_options.trail_color_picker.r,
            trail_color_picker_g = gui_options.trail_color_picker.g,
            trail_color_picker_b = gui_options.trail_color_picker.b,
            hitsound_enable = gui_options.hitsound_enable,
            hitsound_volume = gui_options.hitsound_volume,
            hitsound = gui_options.hitsound,
            blockbotmode = gui_options.blockbotmode,
            blockbot_key = GUI_ELEMENTS.Blockbot_key and GUI_ELEMENTS.Blockbot_key.boundKey or 0x56,
            blockbot_key_mode = GUI_ELEMENTS.Blockbot_key and GUI_ELEMENTS.Blockbot_key.mode or "Toggle",
            autojump_enable = gui_options.autojump_enable,
            blockbot_enable = gui_options.blockbot_enable,
            blockbot_circle_color_r = gui_options.blockbot_circle_color.r,
            blockbot_circle_color_g = gui_options.blockbot_circle_color.g,
            blockbot_circle_color_b = gui_options.blockbot_circle_color.b,
            blockbot_on_head_color_r = gui_options.blockbot_on_head_color.r,
            blockbot_on_head_color_g = gui_options.blockbot_on_head_color.g,
            blockbot_on_head_color_b = gui_options.blockbot_on_head_color.b,
            particle_amount = gui_options.particle_amount,
            sound_enabled = gui_options.sound_enabled,
            sound_peak_radius = gui_options.sound_peak_radius,
            sound_draw_always = gui_options.sound_draw_always,
            theme = gui_options.theme,
        },
        Script = {
            watermark_pos_x = watermark_state.x,
            watermark_pos_y = watermark_state.y,
            hitlog_pos_x = damage_indicator_state.saved_x,
            hitlog_pos_y = damage_indicator_state.saved_y,
            team_dmg_tracker_pos_x = team_dmg_tracker_state.saved_x,
            team_dmg_tracker_pos_y = team_dmg_tracker_state.saved_y,
            spec_pos_x = spectator_list_state.x,
            spec_pos_y = spectator_list_state.y,
            currentAlignment = tostring(damage_indicator_state.current_alignment),
        }
    }
    return t
end

function SaveConfig()
    local data = FlattenConfigData()
    LIP.save(global_ui_state.cfgFile, data)
    print("SaveConfig: Config saved to " .. global_ui_state.cfgFile)
end

function LoadConfig()
    local function createDefaultConfig()
        print("Creating default config file...")
        SaveConfig()
        print("Default config created at: " .. global_ui_state.cfgFile)
    end

    local function validateConfig(data)
        if not data or type(data) ~= "table" then return false end
        if not data.gui_options or not data.Script then return false end

        local essentialKeys = {"font_size", "watermark_enable", "hitlogs_enable"}
        for _, key in ipairs(essentialKeys) do
            if data.gui_options[key] == nil then return false end
        end
        return true
    end

    -- Check if file exists
    local file = io.open(global_ui_state.cfgFile, "r")
    if not file then
        print("Config file not found.")
        createDefaultConfig()
        return
    end
    file:close()

    -- Validate prerequisites
    if not LIP or not LIP.load then
        print("Error: LIP library not available")
        createDefaultConfig()
        return
    end

    -- Load configuration
    local sectionData = LIP.load(global_ui_state.cfgFile)

    -- Validate result
    if not sectionData or type(sectionData) ~= "table" then
        print("Error: Failed to load or parse config file")
        createDefaultConfig()
        return
    end

    if not validateConfig(sectionData) then
        print("Config file is invalid or corrupted.")
        createDefaultConfig()
        return
    end

    -- Apply configuration
    ExpandConfigData(sectionData)
    print("Config loaded and applied successfully")
end
